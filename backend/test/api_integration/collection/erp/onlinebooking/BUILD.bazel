load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "ob_test",
    srcs = ["ob_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/pointer",
        "//backend/proto/tools/v1:tools",
        "//backend/test/api_integration/utils/suite/bookingdomain",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)
