load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "appointment_report_test",
    srcs = ["appointment_report_test.go"],
    tags = ["production_only"],
    deps = [
        "//backend/common/utils/pointer",
        "//backend/test/api_integration/utils/suite/godomain",
        "//third_party/googleapis/google/type:calendar_period_go_proto",
        "//third_party/googleapis/google/type:interval_go_proto",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/reporting/v2:reporting",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/utils/v2:utils",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "payment_report_test",
    srcs = ["payment_report_test.go"],
    tags = ["production_only"],
    deps = [
        "//backend/common/utils/pointer",
        "//backend/test/api_integration/utils/suite/godomain",
        "//third_party/googleapis/google/type:calendar_period_go_proto",
        "//third_party/googleapis/google/type:interval_go_proto",
        "//third_party/googleapis/google/type:money_go_proto",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/reporting/v2:reporting",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/reporting/v2:reporting",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/utils/v2:utils",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "sales_report_test",
    srcs = ["sales_report_test.go"],
    tags = ["production_only"],
    deps = [
        "//backend/common/utils/pointer",
        "//backend/test/api_integration/utils/suite/godomain",
        "//third_party/googleapis/google/type:calendar_period_go_proto",
        "//third_party/googleapis/google/type:interval_go_proto",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/reporting/v2:reporting",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/utils/v2:utils",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "staff_report_test",
    srcs = ["staff_report_test.go"],
    tags = ["production_only"],
    deps = [
        "//backend/common/utils/pointer",
        "//backend/test/api_integration/utils/suite/godomain",
        "//third_party/googleapis/google/type:interval_go_proto",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/reporting/v2:reporting",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/reporting/v2:reporting",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/utils/v2:utils",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
