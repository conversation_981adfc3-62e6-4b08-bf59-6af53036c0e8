load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "account_profile_test",
    srcs = ["account_profile_test.go"],
    deps = [
        "//backend/test/api_integration/utils/suite/mydomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "book_new_test",
    srcs = ["book_new_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "check_exist_email_test",
    srcs = ["check_exist_email_test.go"],
    deps = [
        "//backend/test/api_integration/utils/suite/godomain",
        "//backend/test/api_integration/utils/suite/mydomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "customer_address_test",
    srcs = ["customer_address_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "customer_agreement_test",
    srcs = ["customer_agreement_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/def/business/model",
        "//backend/test/api_integration/utils/suite/clientdomain",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/offering/v1:offering",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//proto",
    ],
)

go_test(
    name = "customer_basic_test",
    srcs = ["customer_basic_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "customer_by_contacts_test",
    srcs = ["customer_by_contacts_test.go"],
    deps = [
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "customer_detail_test",
    srcs = ["customer_detail_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "customer_export_test",
    srcs = ["customer_export_test.go"],
    deps = [
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "customer_filter_view_test",
    srcs = ["customer_filter_view_test.go"],
    deps = [
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "customer_list_test",
    srcs = ["customer_list_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "customer_nearby_test",
    srcs = ["customer_nearby_test.go"],
    deps = [
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "customer_overview_test",
    srcs = ["customer_overview_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/def/grooming/model",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/offering/v1:offering",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//proto",
    ],
)

go_test(
    name = "customer_pet_test",
    srcs = ["customer_pet_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "customer_pet_vaccine_binding_test",
    srcs = ["customer_pet_vaccine_binding_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "customer_with_pet_note_test",
    srcs = ["customer_with_pet_note_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "detailed_form_test",
    srcs = ["detailed_form_test.go"],
    deps = [
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "incident_report_test",
    srcs = ["incident_report_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/business_customer/v1:business_customer",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/business_customer/v1:business_customer",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "invite_url_test",
    srcs = ["invite_url_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "pet_options_test",
    srcs = ["pet_options_test.go"],
    deps = [
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "phone_batch_check_test",
    srcs = ["phone_batch_check_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_stretchr_testify//suite",
    ],
)

go_test(
    name = "smart_list_test",
    srcs = ["smart_list_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/offering/v1:offering",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//proto",
    ],
)
