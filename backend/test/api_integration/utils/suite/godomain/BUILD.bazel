load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "godomain",
    srcs = [
        "appointment.go",
        "context.go",
        "customer.go",
        "lodging.go",
        "ob_b.go",
        "order.go",
        "payment.go",
        "permission.go",
        "refund.go",
        "reporting.go",
        "retails.go",
        "service.go",
        "tax.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/pointer",
        "//backend/proto/tools/v1:tools",
        "//backend/test/api_integration/def/business/model",
        "//backend/test/api_integration/def/grooming/model",
        "//backend/test/api_integration/def/payment/model",
        "//backend/test/api_integration/def/retail/model",
        "//backend/test/api_integration/utils/api",
        "//backend/test/api_integration/utils/env",
        "//backend/test/api_integration/utils/session",
        "//backend/test/api_integration/utils/trace",
        "@com_github_datadog_dd_trace_go_v2//ddtrace/tracer",
        "@com_github_google_uuid//:uuid",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/account/v1:account",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/business_customer/v1:business_customer",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/offering/v1:offering",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/order/v1:order",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/organization/v1:organization",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/permission/v1:permission",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/reporting/v2:reporting",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/offering/v1:offering",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/order/v1:order",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/reporting/v2:reporting",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//proto",
    ],
)
