load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "platformsales",
    srcs = [
        "entity.go",
        "platform_sales.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/adminopenapi/logic/platformsales",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/adminopenapi/repo",
        "//backend/common/rpc/framework/log",
        "//backend/proto/openapi/admin/platform_sales/v1:platform_sales",
        "//third_party/googleapis/google/type:decimal_go_proto",
        "//third_party/googleapis/google/type:money_go_proto",
        "@com_github_shopspring_decimal//:decimal",
    ],
)

go_test(
    name = "platformsales_test",
    srcs = ["platform_sales_test.go"],
    deps = [
        ":platformsales",
        "//backend/proto/openapi/admin/platform_sales/v1:platform_sales",
        "//third_party/googleapis/google/type:decimal_go_proto",
        "//third_party/googleapis/google/type:money_go_proto",
        "@com_github_stretchr_testify//require",
    ],
)
