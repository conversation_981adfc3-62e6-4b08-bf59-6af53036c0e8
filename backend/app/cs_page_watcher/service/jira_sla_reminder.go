package service

import (
	"fmt"
	"sort"
	"strings"
	"time"

	cron "github.com/robfig/cron/v3"
	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/logic/jiraslareminder"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/slack"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ScheduledJiraReminder struct {
	slaReminder          jiraslareminder.SLAReminder
	slackClient          slack.Client
	jiraRepo             jira.IssueRepository
	slaReminderChannelID string
}

func NewScheduledJiraReminder(
	slaReminder jiraslareminder.SLAReminder,
	slackClient slack.Client,
	jiraRepo jira.IssueRepository,
	slaReminderChannelID string,
) *ScheduledJiraReminder {
	return &ScheduledJiraReminder{
		slaReminder:          slaReminder,
		slackClient:          slackClient,
		jiraRepo:             jiraRepo,
		slaReminderChannelID: slaReminderChannelID,
	}
}

func (s *ScheduledJiraReminder) Remind() {
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.Fatalf("Remind: Failed to load location: %v", err)
		return
	}

	c := cron.New(cron.WithLocation(beijingLocation))

	// Schedule to run at 3 PM Beijing time every day
	_, err = c.AddFunc("0 15 * * *", func() {
		log.Infof("Remind: Running scheduled Jira SLA reminder...")

		// Fetch tickets from the last 7 days to ensure nothing is missed.
		issues, err := s.jiraRepo.GetNewOrUpdatedBugTickets(time.Now().AddDate(0, 0, -30))
		if err != nil {
			log.Errorf("Remind: Error fetching Jira tickets: %v", err)
			return
		}

		slackMessage := s.remind(issues)
		if slackMessage == "" {
			log.Infof("Remind: No Slack message generated.")
			return
		}

		if slackMessage != "" {
			// Send the message to the configured channel
			if s.slaReminderChannelID == "" {
				log.Warnf("Remind: SLAReminderChannelID is not configured. Cannot send Slack message.")
				return
			}
			_, err := s.slackClient.SendMessage(s.slaReminderChannelID, slackMessage)
			if err != nil {
				log.Errorf("Remind: Error sending Slack message: %v", err)
				return
			}
			log.Infof("Remind: Sent SLA reminder to Slack channel %s, message:%v", s.slaReminderChannelID, slackMessage)
		}
	})
	if err != nil {
		log.Fatalf("Remind: Failed to add cron job: %v", err)
		return
	}

	c.Start()
	log.Infof("Remind: Jira SLA reminder scheduler started.")
}

func (s *ScheduledJiraReminder) remind(issues []*jira.Issue) (slackRemindText string) {
	results, err := s.slaReminder.EvaluateJiraTickets(issues)
	if err != nil {
		return ""
	}

	filteredResults := s.filterSLAResults(results)
	if len(filteredResults) == 0 {
		log.Infof("Remind: No SLAResults found.")
		return ""
	}

	email2UserIDMap := s.getEmailToUserIDMap(filteredResults)
	issueMap := lo.KeyBy(issues, func(issue *jira.Issue) string {
		return issue.Key
	})

	squadGroups, noSquadGroup := s.groupMessagesBySquad(filteredResults, issueMap, email2UserIDMap)

	return s.formatSlackMessage(squadGroups, noSquadGroup)
}

func (s *ScheduledJiraReminder) filterSLAResults(results []*jiraslareminder.SLAResult) []*jiraslareminder.SLAResult {
	var filteredResults []*jiraslareminder.SLAResult
	for _, res := range results {
		if !res.IsSLABreached && res.TimeUntilBreach > 0 && res.TimeUntilBreach <= 8*time.Hour {
			filteredResults = append(filteredResults, res)
		}
	}
	return filteredResults
}

func (s *ScheduledJiraReminder) getEmailToUserIDMap(results []*jiraslareminder.SLAResult) map[string]string {
	var emails []string
	emailSet := make(map[string]struct{})
	for _, res := range results {
		if res.DevEngineerEmail != "" {
			if _, ok := emailSet[res.DevEngineerEmail]; !ok {
				emails = append(emails, res.DevEngineerEmail)
				emailSet[res.DevEngineerEmail] = struct{}{}
			}
		}
		if res.AssigneeEmail != "" {
			if _, ok := emailSet[res.AssigneeEmail]; !ok {
				emails = append(emails, res.AssigneeEmail)
				emailSet[res.AssigneeEmail] = struct{}{}
			}
		}
	}

	userIDList := s.slackClient.LookUpByEmail(emails)
	email2UserIDMap := make(map[string]string)
	for i, email := range emails {
		if i < len(userIDList) {
			email2UserIDMap[email] = userIDList[i]
		}
	}
	return email2UserIDMap
}

func (s *ScheduledJiraReminder) groupMessagesBySquad(results []*jiraslareminder.SLAResult,
	issueMap map[string]*jira.Issue,
	email2UserIDMap map[string]string) (map[string][]string, []string) {

	squadGroups := make(map[string][]string)
	var noSquadGroup []string

	for _, slaResult := range results {
		issue, ok := issueMap[slaResult.JiraKey]
		if !ok {
			continue
		}

		jiraURL := fmt.Sprintf("https://moego.atlassian.net/browse/%s", slaResult.JiraKey)
		msg := fmt.Sprintf("<%s|%s> %s (Expires in: %s)",
			jiraURL, slaResult.JiraKey, issue.Summary, formatDuration(slaResult.TimeUntilBreach))

		if slaResult.DevEngineerEmail != "" {
			if userID, ok := email2UserIDMap[slaResult.DevEngineerEmail]; ok && userID != "" {
				msg += fmt.Sprintf(" <@%s>", userID)
			}
		}

		if slaResult.AssigneeEmail != "" {
			if userID, ok := email2UserIDMap[slaResult.AssigneeEmail]; ok && userID != "" {
				msg += fmt.Sprintf(" <@%s>", userID)
			}
		}

		if issue.JiraShowSquad != "" {
			squadGroups[issue.JiraShowSquad] = append(squadGroups[issue.JiraShowSquad], msg)
		} else {
			noSquadGroup = append(noSquadGroup, msg)
		}
	}
	return squadGroups, noSquadGroup
}

func (s *ScheduledJiraReminder) formatSlackMessage(squadGroups map[string][]string, noSquadGroup []string) string {
	var squadKeys []string
	for key := range squadGroups {
		squadKeys = append(squadKeys, key)
	}
	sort.Strings(squadKeys)

	var messages []string
	for _, key := range squadKeys {
		messages = append(messages, fmt.Sprintf("\n--- %s ---", key))
		messages = append(messages, squadGroups[key]...)
	}

	if len(noSquadGroup) > 0 {
		messages = append(messages, "\n--- Other ---")
		messages = append(messages, noSquadGroup...)
	}

	return strings.Join(messages, "\n")
}

func formatDuration(d time.Duration) string {
	hours := int(d.Hours())
	minutes := int(d.Minutes()) % 60
	return fmt.Sprintf("%dh %dm", hours, minutes)
}
