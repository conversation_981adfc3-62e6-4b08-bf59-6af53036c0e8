package service

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/logic/jiraslareminder"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/slack"
)

func TestScheduledJiraReminder_Remind_Manual(t *testing.T) {
	t.Skip("Skipping manual test for sending real Slack message")

	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	jiraRepo, err := jira.NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.Nil(t, err)

	slackClient := slack.NewClient(cfg.CsPageWatcher.SlackToken)

	slaReminder := jiraslareminder.NewSLAReminder()

	reminder := &ScheduledJiraReminder{
		slaReminder: slaReminder,
		slackClient: slackClient,
	}

	issues, err := jiraRepo.GetNewOrUpdatedBugTickets(time.Now().AddDate(0, 0, -30))
	assert.Nil(t, err)
	slackMessage := reminder.remind(issues)

	slackClient.SendMessage(cfg.CsPageWatcher.SLAReminderChannelID, slackMessage)

	t.Log("Generated Slack Message:")
	t.Log(slackMessage)
}
