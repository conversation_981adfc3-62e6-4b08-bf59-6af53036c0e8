package service

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/logic/watcher"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
)

func TestRealJiraEvaluate(t *testing.T) {
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	r, err := jira.NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.Nil(t, err)
	issue, err := r.GetIssueDetails("CS-30797")
	assert.Nil(t, err)
	t.Logf("issue: %s", lo.Must(json.Marshal(issue)))

	evaluator := watcher.NewJiraIncidentEvaluator([]string{"<EMAIL>"})
	retIncident, err := evaluator.EvaluateBug(issue)
	t.Logf("retIncident: %s", lo.Must(json.Marshal(retIncident)))
}

func TestPageWatcherIns_retryOperation(t *testing.T) {
	tests := []struct {
		name          string
		op            func() error
		errMsgFormat  string
		args          []interface{}
		expectedErr   error
		expectedCalls int
	}{
		{
			name: "success on first try",
			op: func() error {
				return nil
			},
			errMsgFormat:  "operation failed: %v",
			args:          []interface{}{"test"},
			expectedErr:   nil,
			expectedCalls: 1,
		},
		{
			name: "success after retries",
			op: func() func() error {
				callCount := 0
				return func() error {
					callCount++
					if callCount < 3 {
						return errors.New("temporary error")
					}
					return nil
				}
			}(),
			errMsgFormat:  "operation failed: %v",
			args:          []interface{}{"test"},
			expectedErr:   nil,
			expectedCalls: 3,
		},
		{
			name: "fail after max retries",
			op: func() error {
				return errors.New("persistent error")
			},
			errMsgFormat:  "operation failed: %v, %w",
			args:          []interface{}{"test"},
			expectedErr:   errors.New("operation failed: test, persistent error"),
			expectedCalls: 4, // 1 initial + 3 retries
		},
		{
			name: "fail after max retries and with no args",
			op: func() error {
				return errors.New("persistent error")
			},
			errMsgFormat:  "operation failed: %w",
			args:          nil,
			expectedErr:   errors.New("operation failed: persistent error"),
			expectedCalls: 4, // 1 initial + 3 retries
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &PageWatcherIns{}
			ctx := context.Background()

			// Track actual calls
			callCount := 0
			wrappedOp := func() error {
				callCount++
				return tt.op()
			}

			var err error
			if tt.args == nil {
				err = p.retryOperation(ctx, wrappedOp, tt.errMsgFormat)
			} else {
				err = p.retryOperation(ctx, wrappedOp, tt.errMsgFormat, tt.args...)
			}

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.expectedCalls, callCount)
		})
	}
}
