package configloader

import (
	"fmt"
	"os"
	"sync"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Config struct {
	CsPageWatcher struct {
		JiraToken string `yaml:"jira_token"`
		JiraEmail string `yaml:"jira_email"`

		SlackToken string `yaml:"slack_token"`

		T1SlackChannelID string `yaml:"t1_slack_channel_id"`

		DatadogAPIKey        string   `yaml:"datadog_api_key"`
		DatadogAPPKey        string   `yaml:"datadog_app_key"`
		RefUser              []string `yaml:"ref_user"`
		SLAReminderChannelID string   `yaml:"sla_reminder_channel_id"`
	} `yaml:"cs_page_watcher"`
}

var initCfg sync.Once

func Init(dir string) *Config {
	var cfg *Config
	initCfg.Do(func() {
		env := os.Getenv("MOEGO_ENVIRONMENT")
		if len(env) == 0 {
			env = "local"
			log.Warnf("MOEGO_ENVIRONMENT is not set, using default value: %s", env)
		}

		c, err := config.DefaultConfigLoader.Load(fmt.Sprintf("%s/%s/cs_page_watcher.yaml", dir, env))
		if err != nil {
			panic(err)
		}

		cfg = &Config{}
		err = c.Unmarshal(cfg)
		if err != nil {
			panic(err)
		}
	})
	return cfg
}
