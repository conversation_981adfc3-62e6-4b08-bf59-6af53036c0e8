package jira

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
)

func TestIssueRepositoryIns_GetNewOrUpdatedBugTickets(t *testing.T) {
	t.<PERSON><PERSON>("manual test")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	r, err := NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.Nil(t, err)
	yesterday := time.Now().AddDate(0, 0, -1)
	issues, err := r.GetNewOrUpdatedBugTickets(yesterday)
	assert.Nil(t, err)
	for _, issue := range issues {
		t.Logf("issue: %s", lo.Must(json.Marshal(issue)))
	}
}

func TestIssueRepositoryIns_CloseIssue(t *testing.T) {
	t.Skip("manual test")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	r, err := NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.Nil(t, err)
	err = r.CloseIssue("CS-30650")
	assert.Nil(t, err)

}

func TestIssueRepositoryIns_GetIssueDetails(t *testing.T) {
	t.Skip("manual test")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	r, err := NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.Nil(t, err)
	issue, err := r.GetIssueDetails("CS-31366")
	assert.Nil(t, err)
	t.Logf("issue: %s", lo.Must(json.Marshal(issue)))
	assert.Equal(t, issue.Key, "CS-31366")
	assert.Equal(t, "Non-T1 Ticket (General)", issue.T1OrGeneral)
	assert.Equal(t, "P3-Moderate", issue.IssuePriority)
	assert.Equal(t, "No", issue.SLABreach)
	assert.Equal(t, "", issue.CustomerStage)
	assert.Equal(t, "", issue.Description)
	assert.Equal(t, `biz go to shift management > list view > to edit an override, but it is not supported to save the change - <EMAIL>

See video:

!IMG_3706 (1).mov|width=1080,height=1920,alt="IMG_3706 (1).mov"!`, issue.IssueDescription)
	assert.Equal(t, "<EMAIL>", issue.DevEngineer[0].EmailAddress)
	assert.Equal(t, "21 hours", issue.ResolutionTimeCustom)
	assert.Equal(t, "Wayne Wei", issue.CreatedByCustom)
	assert.Equal(t, "Merchant", issue.JiraShowSquad)
}
