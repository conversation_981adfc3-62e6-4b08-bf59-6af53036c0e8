package jira

import "time"

// UserInfo represents the assignee of a Jira issue.
type UserInfo struct {
	DisplayName  string
	EmailAddress string
}

type Issue struct {
	ID                   string
	Key                  string
	Summary              string
	Status               string
	Comment              string
	JiraShowSquad        string
	IssueDescription     string
	Description          string
	Created              time.Time
	Updated              time.Time
	Assignee             UserInfo // 添加Assignee字段
	Reporter             UserInfo // 添加Assignee字段
	T1OrGeneral          string
	IssuePriority        string
	CustomerStage        string
	Components           []string // 添加 Components 字段
	SLABreach            string
	LogoName             string
	DevEngineer          []UserInfo
	ResolutionTimeCustom string // 添加 ResolutionTimeCustom 字段
	CreatedByCustom      string // 添加 CreatedByCustom 字段
	// CustomFields  map[string]interface{} // 用于存储用户自定义的字段
}
