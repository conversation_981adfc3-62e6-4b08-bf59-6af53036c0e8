package slack

import (
	"testing"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
)

func TestSendMessageToPerson(t *testing.T) {
	t.<PERSON><PERSON>("manual test")
	// Initialize the Slack client with your token
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	t.Logf("token:%v", cfg.CsPageWatcher.SlackToken)
	client := NewClient(cfg.CsPageWatcher.SlackToken)

	// Add the emoji to a message
	err := client.SendMessageToPerson("<EMAIL>", "hello world")
	if err != nil {
		t.Fatalf("TestSendMessageToPerson error: %v", err)
	}
}

func TestAddEmojiToMessage(t *testing.T) {
	t.<PERSON><PERSON>("manual test")
	// Initialize the Slack client with your token
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	t.Logf("token:%v", cfg.CsPageWatcher.SlackToken)
	client := NewClient(cfg.CsPageWatcher.SlackToken)

	// Add the emoji to a message
	err := client.AddEmojiToMessage("C092AS8EL1Y", "1750410965.975779", global.EmojiDoing)
	if err != nil {
		t.Fatalf("TestAddEmojiToMessage error: %v", err)
	}
}

func TestSendMessage(t *testing.T) {
	t.Skip("manual test")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	t.Logf("token:%v", cfg.CsPageWatcher.SlackToken)
	client := NewClient(cfg.CsPageWatcher.SlackToken)
	ts, err := client.SendMessage("C092AS8EL1Y", "Hello, world!")
	if err != nil {
		t.Fatalf("TestSendMessage error: %v", err)
	}
	t.Logf("message ts:%v", ts)
}

func TestAddMembersToChannel(t *testing.T) {
	t.Skip("manual test")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	t.Logf("token:%v", cfg.CsPageWatcher.SlackToken)
	client := NewClient(cfg.CsPageWatcher.SlackToken)

	// Ensure the bot is in the channel before adding members
	err := client.JoinChannel("C092AS8EL1Y")
	if err != nil {
		t.Logf("Bot might already be in channel C092AS8EL1Y or failed to join: %v", err)
	} else {
		t.Logf("Bot successfully joined channel C092AS8EL1Y")
	}

	err = client.AddMembersToChannel("C092AS8EL1Y", []string{
		"<EMAIL>",
	})
	if err != nil {
		t.Fatalf("TestAddMembersToChannel error: %v", err)
	}
}
