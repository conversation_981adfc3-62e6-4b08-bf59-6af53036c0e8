load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "watcher",
    srcs = [
        "components_squards_mapping.go",
        "evaluator.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/logic/watcher",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/cs_page_watcher/global",
        "//backend/app/cs_page_watcher/repo/datadog",
        "//backend/app/cs_page_watcher/repo/jira",
        "//backend/common/rpc/framework/log",
    ],
)

go_test(
    name = "watcher_test",
    srcs = ["evaluator_test.go"],
    embed = [":watcher"],
    deps = [
        "//backend/app/cs_page_watcher/global",
        "//backend/app/cs_page_watcher/repo/datadog",
        "//backend/app/cs_page_watcher/repo/jira",
        "@com_github_stretchr_testify//assert",
    ],
)
