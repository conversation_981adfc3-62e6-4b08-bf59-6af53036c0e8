load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "feature",
    srcs = [
        "boarding_daycare.go",
        "context.go",
        "http_request.go",
        "online_booking.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/tools/repo/feature",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/tools/utils",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/pointer",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/offering/v1:offering",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/organization/v1:organization",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/offering/v1:offering",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/metadata/v1:metadata",
        "@org_golang_google_protobuf//encoding/protojson",
    ],
)
