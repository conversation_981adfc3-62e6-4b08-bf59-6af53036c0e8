package organization

import organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"

type Country struct {
	Country        organizationpb.CountryDef
	TimeZone       organizationpb.TimeZone
	CurrencyCode   string
	CurrencySymbol string
}

//nolint:mnd
var countries = map[string]*Country{
	"US": {
		Country: organizationpb.CountryDef{
			Name: "United States",
			Code: "US",
		},
		TimeZone: organizationpb.TimeZone{
			Name:    "America/Los_Angeles",
			Seconds: -28800,
		},
		CurrencyCode:   "USD",
		CurrencySymbol: "$",
	},
	"CN": {
		Country: organizationpb.CountryDef{
			Name: "China",
			Code: "CN",
		},
		TimeZone: organizationpb.TimeZone{
			Name:    "Asia/Shanghai",
			Seconds: 28800,
		},
		CurrencyCode:   "CNY",
		CurrencySymbol: "¥",
	},
	"AU": {
		Country: organizationpb.CountryDef{
			Name: "Australia",
			Code: "AU",
		},
		TimeZone: organizationpb.TimeZone{
			Name:    "Australia/Sydney",
			Seconds: 36000,
		},
		CurrencyCode:   "AUD",
		CurrencySymbol: "$",
	},
	"CA": {
		Country: organizationpb.CountryDef{
			Name: "Canada",
			Code: "CA",
		},
		TimeZone: organizationpb.TimeZone{
			Name:    "America/Toronto",
			Seconds: -18000,
		},
		CurrencyCode:   "CAD",
		CurrencySymbol: "$",
	},
	"GB": {
		Country: organizationpb.CountryDef{
			Name: "United Kingdom",
			Code: "GB",
		},
		TimeZone: organizationpb.TimeZone{
			Name:    "Europe/London",
			Seconds: 0,
		},
		CurrencyCode:   "GBP",
		CurrencySymbol: "£",
	},
}
