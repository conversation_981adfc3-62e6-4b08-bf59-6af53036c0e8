CREATE TABLE deploy_task
(
    id            SERIAL PRIMARY KEY,
    name          <PERSON><PERSON><PERSON><PERSON>(255)  NOT NULL UNIQUE,
    title         VARCHAR(255)  NOT NULL,
    description   VARCHAR(5000) NOT NULL,
    created_by    <PERSON><PERSON><PERSON><PERSON>(255)  NOT NULL,
    created_at    TIMESTAMPTZ   NOT NULL DEFAULT NOW(),
    updated_at    TIMESTAMPTZ   NOT NULL DEFAULT NOW(),
    ended_at      TIMESTAMPTZ,
    state         VARCHAR(50)   NOT NULL,
    current_phase VARCHAR(50),
    parameters    JSONB
);
COMMENT ON TABLE deploy_task IS 'Deployment task table';
COMMENT ON COLUMN deploy_task.id IS 'Unique identifier for the deployment task';
COMMENT ON COLUMN deploy_task.name IS 'Unique name for the deployment task';
COMMENT ON COLUMN deploy_task.title IS 'Title of the deployment task';
COMMENT ON COLUMN deploy_task.description IS 'Description of the deployment task';
COMMENT ON COLUMN deploy_task.created_by IS 'User who created the deployment task';
COMMENT ON COLUMN deploy_task.created_at IS 'Timestamp when the deployment task was created';
COMMENT ON COLUMN deploy_task.updated_at IS 'Timestamp when the deployment task was last updated';
COMMENT ON COLUMN deploy_task.ended_at IS 'Timestamp when the deployment task ended (nullable)';
COMMENT ON COLUMN deploy_task.state IS 'Current state of the deployment task';
COMMENT ON COLUMN deploy_task.current_phase IS 'Current phase of the deployment task';
COMMENT ON COLUMN deploy_task.parameters IS 'Parameters for the deployment task';

CREATE TABLE deploy_phase
(
    id             SERIAL PRIMARY KEY,
    deploy_task_id INTEGER REFERENCES deploy_task (id) ON DELETE CASCADE,
    name           VARCHAR(255) NOT NULL,
    type           VARCHAR(255) NOT NULL,
    state          VARCHAR(255) NOT NULL,
    parameters     JSONB,
    started_at     TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    ended_at       TIMESTAMPTZ
);
COMMENT ON TABLE deploy_phase IS 'Deployment phase table';
COMMENT ON COLUMN deploy_phase.id IS 'Unique identifier for the deployment phase';
COMMENT ON COLUMN deploy_phase.deploy_task_id IS 'ID of the associated deployment task';
COMMENT ON COLUMN deploy_phase.name IS 'Name of the deployment phase';
COMMENT ON COLUMN deploy_phase.type IS 'Phase of the deployment phase';
COMMENT ON COLUMN deploy_phase.state IS 'Current state of the deployment phase';
COMMENT ON COLUMN deploy_phase.parameters IS 'Parameters for the deployment phase';
COMMENT ON COLUMN deploy_phase.started_at IS 'Timestamp when the phase started';
COMMENT ON COLUMN deploy_phase.ended_at IS 'Timestamp when the phase ended (nullable)';

CREATE TABLE deploy_log
(
    id             SERIAL PRIMARY KEY,
    deploy_task_id INTEGER REFERENCES deploy_task (id) ON DELETE CASCADE,
    log_time       TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    type           VARCHAR(50) NOT NULL,
    state          VARCHAR(50) NOT NULL,
    phase          VARCHAR(50),
    message        JSONB
);
COMMENT ON TABLE deploy_log IS 'Deployment log table';
COMMENT ON COLUMN deploy_log.id IS 'Unique identifier for the log entry';
COMMENT ON COLUMN deploy_log.deploy_task_id IS 'ID of the associated deployment task';
COMMENT ON COLUMN deploy_log.log_time IS 'Timestamp of the log entry';
COMMENT ON COLUMN deploy_log.type IS 'Type of the log entry';
COMMENT ON COLUMN deploy_log.state IS 'State of the deployment task at the time of the log entry';
COMMENT ON COLUMN deploy_log.phase IS 'Phase of the deployment task at the time of the log entry';
COMMENT ON COLUMN deploy_log.message IS 'Message associated with the log entry';

CREATE INDEX idx_deploy_task_ended_at ON deploy_task (ended_at);
CREATE INDEX idx_deploy_phase_deploy_task_id_phase_name ON deploy_phase (deploy_task_id, type);
CREATE INDEX idx_deploy_log_deploy_task_id_phase ON deploy_log (deploy_task_id, phase);
CREATE INDEX idx_deploy_task_name ON deploy_task (name);
CREATE INDEX idx_deploy_task_state_current_phase ON deploy_task (state, current_phase);
CREATE INDEX idx_deploy_phase_phase_name ON deploy_phase (type, name, ended_at);