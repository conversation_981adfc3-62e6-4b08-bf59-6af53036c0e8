package state

import (
	"database/sql"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/entity"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/mocks"
)

type FullDeploymentPhaseTestSuite struct {
	suite.Suite
}

func TestFullDeploymentPhaseSuite(t *testing.T) {
	suite.Run(t, new(FullDeploymentPhaseTestSuite))
}

func (t *FullDeploymentPhaseTestSuite) TestNewCanaryPhase() {
	manager := &mocks.ReadWriter{}
	phase := t.getPhase(deployplatform.Init, deployplatform.Init, deployplatform.PhaseCanary, "{}", manager)

	manager.On("CreatePhaseAndUpdateTask", mock.Anything, mock.Anything,
		mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("CreatePhaseAndUpdateTask run")
	})
	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.CurrentPhase = deployplatform.PhaseFullDeployment
		phase.Task.State = deployplatform.Init
	}).Return(phase.Task, nil)
	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("GetPhasesAndLogs run")
	}).Return(phase.Phases, phase.Logs, nil)

	fullDeploymentPhase, err := NewFullDeploymentPhase(manager, phase.Task)
	t.Nil(err)
	t.NotNil(fullDeploymentPhase)
	t.Equal(phase.Task.CurrentPhase, fullDeploymentPhase.Task.CurrentPhase)
	t.Equal(phase.Task.ID, fullDeploymentPhase.Task.ID)
	t.Equal(phase.Task.State, fullDeploymentPhase.Task.State)
}

func (t *FullDeploymentPhaseTestSuite) getPhase(taskType, phaseType, currentPhase string, parameters string, repo deployplatform.ReadWriter) Phase {
	return Phase{
		Task: &entity.DeployTask{
			ID:           112233,
			State:        taskType,
			CurrentPhase: currentPhase,
			Name:         "test",
			Title:        "123456",
			CreatedBy:    "test",
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
			Parameters:   entity.JSONB(`{}`),
		},
		Phases: []*entity.DeployPhase{
			&entity.DeployPhase{
				ID:           112233,
				DeployTaskID: 112233,
				Type:         deployplatform.PhaseCI,
				State:        deployplatform.Succeeded,
				StartedAt:    time.Now(),
				EndedAt:      sql.NullTime{Time: time.Now(), Valid: true},
				Parameters:   entity.JSONB(`{}`),
			},
			&entity.DeployPhase{
				ID:           112233,
				DeployTaskID: 112233,
				Type:         deployplatform.PhaseCanary,
				State:        deployplatform.Succeeded,
				StartedAt:    time.Now(),
				EndedAt:      sql.NullTime{Time: time.Now(), Valid: true},
				Parameters:   entity.JSONB(`{}`),
			},
			&entity.DeployPhase{
				ID:           112233,
				DeployTaskID: 112233,
				Type:         currentPhase,
				State:        phaseType,
				StartedAt:    time.Now(),
				EndedAt:      sql.NullTime{Time: time.Now()},
				Parameters:   entity.JSONB(parameters),
			},
		},
		Logs: []*entity.DeployLog{
			&entity.DeployLog{
				ID:           112233,
				DeployTaskID: 112233,
				LogTime:      time.Now(),
				Type:         "CIInit",
				Message:      entity.JSONB(`{}`),
				State:        deployplatform.Init,
				Phase:        deployplatform.PhaseCI,
			},
			&entity.DeployLog{
				ID:           112233,
				DeployTaskID: 112233,
				LogTime:      time.Now(),
				Type:         "CISuccess",
				Message:      entity.JSONB(`{}`),
				State:        deployplatform.Succeeded,
				Phase:        deployplatform.PhaseCI,
			},
			&entity.DeployLog{
				ID:           112233,
				DeployTaskID: 112233,
				LogTime:      time.Now(),
				Type:         "CanaryInit",
				Message:      entity.JSONB(`{}`),
				State:        deployplatform.Init,
				Phase:        deployplatform.PhaseCanary,
			},
			&entity.DeployLog{
				ID:           112233,
				DeployTaskID: 112233,
				LogTime:      time.Now(),
				Type:         "CanarySuccess",
				Message:      entity.JSONB(`{}`),
				State:        deployplatform.Succeeded,
				Phase:        deployplatform.PhaseCanary,
			},
			&entity.DeployLog{
				ID:           112233,
				DeployTaskID: 112233,
				LogTime:      time.Now(),
				Type:         "FullDeploymentInit",
				Message:      entity.JSONB(`{}`),
				State:        deployplatform.Init,
				Phase:        deployplatform.PhaseFullDeployment,
			},
		},
		Repo: repo,
	}
}

func (t *FullDeploymentPhaseTestSuite) TestChange() {
	tests := []struct {
		name      string
		state     string
		params    []string
		wantState string
		wantErr   bool
	}{
		{
			name:      "init to running",
			state:     deployplatform.Init,
			params:    []string{"{}"},
			wantState: deployplatform.Running,
			wantErr:   false,
		},
		{
			name:      "running to succeeded",
			state:     deployplatform.Running,
			params:    []string{},
			wantState: deployplatform.Succeeded,
			wantErr:   false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func() {
			for _, param := range tt.params {
				manager := &mocks.ReadWriter{}
				phase := t.getPhase(tt.state, tt.state, deployplatform.PhaseFullDeployment, param, manager)

				manager.On("EndDeployTask", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					phase.Task.EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
					phase.Phases[2].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
				}).Return(nil)
				manager.On("EndDeployPhase", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					phase.Phases[2].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
				}).Return(nil)

				manager.On("UpdatePhaseAndTaskState", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Run(func(args mock.Arguments) {}).Return(nil)

				manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					phase.Task.CurrentPhase = deployplatform.PhaseFullDeployment
					phase.Task.State = tt.wantState
				}).Return(phase.Task, nil)

				manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					t.T().Logf("GetPhasesAndLogs run")
					phase.Phases[2].State = tt.wantState
				}).Return(phase.Phases, phase.Logs, nil)

				p := &FullDeploymentPhase{
					Phase: phase,
				}
				got, err := p.Change(nil)
				if (err != nil) != tt.wantErr {
					t.T().Errorf("Change() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if got.StateType() != tt.wantState {
					t.T().Errorf("Change() got = %v, want %v", got.StateType(), tt.wantState)
				}
				if tt.wantState == deployplatform.End {
					t.True(phase.Phases[2].EndedAt.Valid)
				} else {
					t.False(phase.Phases[2].EndedAt.Valid)
				}
			}
		})
	}
}

func (t *FullDeploymentPhaseTestSuite) TestInitState() {
	manager := &mocks.ReadWriter{}
	phase := t.getPhase(deployplatform.Init, deployplatform.Init, deployplatform.PhaseFullDeployment, "{}", manager)

	manager.On("UpdatePhaseAndTaskState", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Return(phase.Task, nil)
	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Return(phase.Phases, phase.Logs, nil)
	p := &FullDeploymentPhase{
		Phase: phase,
	}
	err := p.InitState()
	t.Nil(err)
}

func (t *FullDeploymentPhaseTestSuite) TestSetState() {
	manager := &mocks.ReadWriter{}
	phase := t.getPhase(deployplatform.Init, deployplatform.Init, deployplatform.PhaseFullDeployment, "{}", manager)

	manager.On("UpdatePhaseAndTaskState", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Return(phase.Task, nil)
	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Return(phase.Phases, phase.Logs, nil)

	p := &FullDeploymentPhase{
		Phase: phase,
	}
	err := p.SetState(deployplatform.Running)
	t.Nil(err)
}

func (t *FullDeploymentPhaseTestSuite) TestEnd() {
	manager := &mocks.ReadWriter{}
	phase := t.getPhase(deployplatform.End, deployplatform.End, deployplatform.PhaseFullDeployment, "{}", manager)

	manager.On("EndDeployTask", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
		phase.Phases[2].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
	}).Return(nil)
	manager.On("EndDeployPhase", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Phases[2].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
	}).Return(nil)

	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.CurrentPhase = deployplatform.PhaseFullDeployment
		phase.Task.State = deployplatform.End
	}).Return(phase.Task, nil)

	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("GetPhasesAndLogs run")
	}).Return(phase.Phases, phase.Logs, nil)

	p := &FullDeploymentPhase{
		Phase: phase,
	}
	got, err := p.Change(nil)
	t.Nil(err)
	t.Equal(deployplatform.End, got.StateType())
	t.True(phase.Task.EndedAt.Valid)
	t.True(phase.Phases[2].EndedAt.Valid)
}
