package state

import (
	"context"
	"errors"

	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/entity"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

/*
总共三个阶段：CI，Canary，FullDeployment
每个阶段有Start,End,Failed,Rollback,Skiped状态
*/

// Task 状态模式
type Task interface {
	// Change 状态根据传入的info进行改变，返回新的状态或者错误
	Change(env map[string]string) (Task, error)
	StateType() string
	GetCurrentPhase() (*entity.DeployPhase, error)
	SyncStateFromDB() (err error)
	ID() int64
}

// Phase 父状态，绑定DB的数据，从DB中同步状态到内存
type Phase struct {
	Repo deployplatform.ReadWriter

	// 根据taskID 映射数据库的数据
	Task   *entity.DeployTask
	Phases []*entity.DeployPhase
	Logs   []*entity.DeployLog
}

// ID 返回taskID
func (p *Phase) ID() int64 {
	return p.Task.ID
}

func (p *Phase) StateType() string {
	return p.Task.State
}

func (p *Phase) SetState(newState string) error {
	for _, phase := range p.Phases {
		if phase.Type == p.Task.CurrentPhase {
			if err := p.Repo.UpdatePhaseAndTaskState(context.Background(), phase, newState, "{}"); err != nil {
				log.Errorf("failed to update phase and task state, taskID: %d", p.Task.ID)
				return err
			}
			// 更新内存数据
			if err := p.SyncStateFromDB(); err != nil {
				log.Errorf("failed to sync state from DB, taskID: %d", p.Task.ID)
				return err
			}
			break
		}
	}
	return nil
}

func (p *Phase) GetCurrentPhase() (*entity.DeployPhase, error) {
	for _, phase := range p.Phases {
		if phase.Type == p.Task.CurrentPhase {
			return phase, nil
		}
	}
	return nil, nil
}

// endPhaseOrTask 结束阶段或任务。
func (p *Phase) endPhaseOrTask(phase *entity.DeployPhase, endTask bool) {
	if endTask {
		if err := p.Repo.EndDeployTask(context.Background(), phase.DeployTaskID, ""); err != nil {
			if errors.Is(err, deployplatform.ErrTaskHasAlreadyEnded) {
				return
			}
			log.Errorf("failed to end task, taskID: %d", p.Task.ID)
		}
	} else {
		if err := p.Repo.EndDeployPhase(context.Background(), phase, ""); err != nil {
			log.Errorf("failed to end phase, taskID: %d", p.Task.ID)
		}
	}
	// 更新内存数据
	if err := p.SyncStateFromDB(); err != nil {
		log.Errorf("failed to sync state from DB, taskID: %d", p.Task.ID)
	}
}

// SyncStateFromDB 从数据库中同步状态到内存，不要直接修改内存的结构，这样不容易出错
func (p *Phase) SyncStateFromDB() (err error) {
	p.Task, err = p.Repo.GetDeployTaskByID(context.Background(), p.Task.ID)
	if err != nil {
		return err
	}
	p.Phases, p.Logs, err = p.Repo.GetPhasesAndLogs(context.Background(), p.Task)
	return
}

type TaskParameters struct {
	Repo          string `json:"repo"`
	Branch        string `json:"branch"`
	Sha           string `json:"sha"`
	Committer     string `json:"committer"`
	Workflow      string `json:"workflow"`
	WorkflowID    int64  `json:"workflow_id"`
	WorkflowRunID int64  `json:"run_id"`
	RunNumber     int64  `json:"run_number"`
	RunAttempt    int64  `json:"run_attempt"`
	URL           string `json:"url"`
	JobsURL       string `json:"jobs_url"`
	CancelURL     string `json:"cancel_url"`
	RerunURL      string `json:"rerun_url"`
	LogsURL       string `json:"logs_url"`
	Cancel        bool   `json:"cancel"`
	Rollback      bool   `json:"rollback"`
	Skip          bool   `json:"skip"`
	Rerun         bool   `json:"rerun"`
	Operator      string `json:"operator"`
}
