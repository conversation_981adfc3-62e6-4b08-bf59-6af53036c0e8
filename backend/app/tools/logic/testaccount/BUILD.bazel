load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "testaccount",
    srcs = ["test_account.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/tools/logic/testaccount",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/tools/logic/testaccount/entity",
        "//backend/app/tools/repo/account",
        "//backend/app/tools/repo/account/entity",
        "//backend/app/tools/repo/feature",
        "//backend/app/tools/repo/organization",
        "//backend/app/tools/repo/testaccount",
        "//backend/app/tools/repo/testaccount/entity",
        "//backend/common/rpc/codec/grpc",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/metadata/v1:metadata",
    ],
)

go_test(
    name = "testaccount_test",
    srcs = ["test_account_test.go"],
)
