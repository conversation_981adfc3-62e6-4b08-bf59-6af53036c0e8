package api

import (
	"context"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/protobuf/proto"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
)

type Service struct {
	ID          int64   `json:"id"`
	Name        string  `json:"name"`
	Price       float64 `json:"price"`
	Description string  `json:"description"`
}

type GetServiceListRequest struct {
	CompanyID int64
}

func GetServiceList(ctx context.Context, req *GetServiceListRequest) ([]*Service, error) {
	conn, err := grpc.NewClient("moego-svc-offering:9090", grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, err
	}

	c := offeringsvcpb.NewServiceManagementServiceClient(conn)

	resp, err := c.GetServiceList(ctx, &offeringsvcpb.GetServiceListRequest{
		ServiceType: offeringpb.ServiceType_SERVICE.Enum(),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  proto.Int32(1),
			PageSize: proto.Int32(100),
		},
		TokenCompanyId: req.CompanyID,
	})
	if err != nil {
		return nil, err
	}

	services := make([]*Service, 0)
	for _, category := range resp.GetCategoryList() {
		for _, service := range category.GetServices() {
			services = append(services, &Service{
				ID:          service.GetServiceId(),
				Name:        service.GetName(),
				Price:       service.GetPrice(),
				Description: service.GetDescription(),
			})
		}
	}

	return services, nil
}
