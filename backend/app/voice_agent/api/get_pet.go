package api

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strconv"
)

type Pet struct {
	ID    int64  `json:"id"`
	Name  string `json:"name"`
	Type  string `json:"type"`
	Breed string `json:"breed"`
}

func GetPetList(ctx context.Context, customerID int64) ([]*Pet, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, "http://moego-service-customer:9201/service/customer/pet/getCustomerPetListByCustomerId?customerIdList="+strconv.FormatInt(customerID, 10), nil)
	if err != nil {
		return nil, err
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	type PetResponse struct {
		ID    int64  `json:"petId"`
		Name  string `json:"petName"`
		Type  string `json:"typeName"`
		Breed string `json:"petBreed"`
	}

	var respData []PetResponse
	if err := json.Unmarshal(body, &respData); err != nil {
		return nil, err
	}

	petList := make([]*Pet, 0)
	for _, pet := range respData {
		petList = append(petList, &Pet{
			ID:    pet.ID,
			Name:  pet.Name,
			Type:  pet.Type,
			Breed: pet.Breed,
		})
	}

	return petList, nil
}
