package customer

type Customer struct {
	ID                     int64  `json:"id"`
	CustomerID             int64  `json:"customerId"`
	BusinessID             int64  `json:"businessId"`
	PreferredBusinessID    string `json:"preferredBusinessId"`
	CompanyID              int64  `json:"companyId"`
	AvatarPath             string `json:"avatarPath"`
	Email                  string `json:"email"`
	FirstName              string `json:"firstName"`
	LastName               string `json:"lastName"`
	CustomerCode           string `json:"customerCode"`
	Status                 int    `json:"status"`
	Inactive               int    `json:"inactive"`
	ClientColor            string `json:"clientColor"`
	IsBlockMessage         int    `json:"isBlockMessage"`
	IsBlockOnlineBooking   int    `json:"isBlockOnlineBooking"`
	LoginEmail             string `json:"loginEmail"`
	ReferralSourceID       int64  `json:"referralSourceId"`
	ReferralSourceDesc     string `json:"referralSourceDesc"`
	SendAutoEmail          int    `json:"sendAutoEmail"`
	SendAutoMessage        int    `json:"sendAutoMessage"`
	SendAppAutoMessage     int    `json:"sendAppAutoMessage"`
	UnconfirmedReminderBy  int    `json:"unconfirmedReminderBy"`
	PreferredGroomerID     int64  `json:"preferredGroomerId"`
	PreferredFrequencyDay  int    `json:"preferredFrequencyDay"`
	PreferredFrequencyType int    `json:"preferredFrequencyType"`
	LastServiceTime        string `json:"lastServiceTime"`
	Source                 string `json:"source"`
	ExternalID             string `json:"externalId"`
	CreateTime             int64  `json:"createTime"`
	UpdateTime             int64  `json:"updateTime"`
	PreferredDay           []int  `json:"preferredDay"`
	PreferredTime          []int  `json:"preferredTime"`
	IsUnsubscribed         int    `json:"isUnsubscribed"`
	ApptReminderByList     []int  `json:"apptReminderByList"`
	AccountID              int64  `json:"accountId"`
	Type                   string `json:"type"`
	LifeCycle              string `json:"lifeCycle"`
	ActionState            string `json:"actionState"`
	AllocateStaffID        int64  `json:"allocateStaffId"`
	IsPhoneConflict        bool   `json:"isPhoneConflict"`
}
