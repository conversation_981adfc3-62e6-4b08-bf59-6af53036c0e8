package caretype

import (
	"context"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/caretype"

	pb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

type Logic interface {
	Create(ctx context.Context, request pb.CreateCareTypeRequest) (*pb.CreateCareTypeResponse, error)
}

// Logic implements the business logic for CareTypeService.
type logic struct {
	repo caretype.Repository
}

func (l logic) Create(ctx context.Context, request pb.CreateCareTypeRequest) (*pb.CreateCareTypeResponse, error) {
	//TODO implement me
	panic("implement me")
}

// NewCareTypeLogic creates a new Logic.
func NewCareTypeLogic() Logic {
	return &logic{repo: caretype.NewCareTypeRepo()}
}
