package service

import (
	"context"

	servicetemplate "github.com/MoeGolibrary/moego/backend/app/offering/logic/servicetemplate"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

type TemplateService struct {
	template *servicetemplate.Logic
	offeringpb.UnimplementedServiceTemplateServiceServer
}

func NewServiceTemplateService() *TemplateService {
	return &TemplateService{
		template: servicetemplate.New(),
	}
}

func (t *TemplateService) CreateServiceTemplate(_ context.Context,
	_ *offeringpb.CreateServiceTemplateRequest) (*offeringpb.CreateServiceTemplateResponse, error) {
	// TODO，待实现
	return &offeringpb.CreateServiceTemplateResponse{}, nil
}
