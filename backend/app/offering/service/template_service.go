package service

import (
	"context"

	template "github.com/MoeGolibrary/moego/backend/app/offering/logic/servicetemplate"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

type TemplateService struct {
	template *template.Logic
	offeringpb.UnimplementedServiceTemplateServiceServer
}

func NewTemplateService() *TemplateService {
	return &TemplateService{
		template: template.New(),
	}
}

func (t *TemplateService) CreateServiceTemplate(_ context.Context,
	_ *offeringpb.CreateServiceTemplateRequest) (*offeringpb.CreateServiceTemplateResponse, error) {
	// TODO，待实现
	return &offeringpb.CreateServiceTemplateResponse{}, nil
}
