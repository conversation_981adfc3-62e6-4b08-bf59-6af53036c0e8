load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = [
        "care_type_service.go",
        "template_service.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/logic/servicetemplate",
        "//backend/proto/offering/v1:offering",
    ],
)
