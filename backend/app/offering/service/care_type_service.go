package service

import (
	"context"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic"

	pb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// CareTypeService implements the CareTypeServiceServer interface.
type CareTypeService struct {
	pb.UnimplementedCareTypeServiceServer
	logic *logic.CareTypeLogic
}

// NewCareTypeService creates a new CareTypeService.
func NewCareTypeService(logic *logic.CareTypeLogic) *CareTypeService {
	return &CareTypeService{logic: logic.N}
}

// CreateCareType creates a new care type.
func (s *CareTypeService) CreateCareType(ctx context.Context, req *pb.CreateCareTypeRequest) (*pb.CreateCareTypeResponse, error) {
	return s.logic.CreateCareType(ctx, req)
}

// GetCareType gets a care type by ID.
func (s *CareTypeService) GetCareType(ctx context.Context, req *pb.GetCareTypeRequest) (*pb.GetCareTypeResponse, error) {
	return s.logic.GetCareType(ctx, req)
}

// UpdateCareType updates a care type.
func (s *CareTypeService) UpdateCareType(ctx context.Context, req *pb.UpdateCareTypeRequest) (*pb.UpdateCareTypeResponse, error) {
	return s.logic.UpdateCareType(ctx, req)
}

// DeleteCareType deletes a care type.
func (s *CareTypeService) DeleteCareType(ctx context.Context, req *pb.DeleteCareTypeRequest) (*pb.DeleteCareTypeResponse, error) {
	return s.logic.DeleteCareType(ctx, req)
}

// ListCareTypes lists care types.
func (s *CareTypeService) ListCareTypes(ctx context.Context, req *pb.ListCareTypesRequest) (*pb.ListCareTypesResponse, error) {
	return s.logic.ListCareTypes(ctx, req)
}
