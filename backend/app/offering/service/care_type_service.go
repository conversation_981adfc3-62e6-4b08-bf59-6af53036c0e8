package service

import (
	"context"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/caretype"

	pb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// CareTypeService implements the CareTypeServiceServer interface.
type CareTypeService struct {
	pb.UnimplementedCareTypeServiceServer
	logic *caretype.Logic
}

// NewCareTypeService creates a new CareTypeService.
func NewCareTypeService() *CareTypeService {
	return &CareTypeService{logic: caretype.NewLogic()}
}

// CreateCareType creates a new care type.
func (s *CareTypeService) CreateCareType(ctx context.Context, req *pb.CreateCareTypeRequest) (*pb.CreateCareTypeResponse, error) {
	// TODO 实现
	return nil, nil
}

// GetCareType gets a care type by ID.
func (s *CareTypeService) GetCareType(ctx context.Context, req *pb.GetCareTypeRequest) (*pb.GetCareTypeResponse, error) {
	// TODO 实现
	return nil, nil
}

// UpdateCareType updates a care type.
func (s *CareTypeService) UpdateCareType(ctx context.Context, req *pb.UpdateCareTypeRequest) (*pb.UpdateCareTypeResponse, error) {
	// TODO 实现
	return nil, nil
}

// DeleteCareType deletes a care type.
func (s *CareTypeService) DeleteCareType(ctx context.Context, req *pb.DeleteCareTypeRequest) (*pb.DeleteCareTypeResponse, error) {
	// TODO 实现
	return nil, nil
}

// ListCareTypes lists care types.
func (s *CareTypeService) ListCareTypes(ctx context.Context, req *pb.ListCareTypesRequest) (*pb.ListCareTypesResponse, error) {
	// TODO 实现
	return nil, nil
}
