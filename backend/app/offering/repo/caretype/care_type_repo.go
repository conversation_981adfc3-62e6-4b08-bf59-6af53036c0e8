package caretype

import (
	"context"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"

	"gorm.io/gorm"
)

//go:generate mockgen -package=care_type -destination=mocks/mock_care_type_repository.go github.com/moego/backend/app/offering/repo/care_type Repository
type Repository interface {
	Delete(context.Context, uint) error
}

// careTypeRepo implements the data access logic for CareType.
type repository struct {
	db *gorm.DB
}

func (r repository) Delete(ctx context.Context, u uint) error {
	//TODO implement me
	panic("implement me")
}

// NewCareTypeRepo creates a new careTypeRepo.
func NewCareTypeRepo() Repository {
	return &repository{db: db.GetDB()}
}
