package servicetemplate

import (
	"context"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/caretype/filter"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/query"
)

//go:generate mockgen -package=caretype -destination=mocks/mock_care_type_repository.go github.com/moego/backend/app/offering/repo/caretype Repository
type Repository interface {
	Create(ctx context.Context, serviceTemplate *model.CareType) error
	Get(ctx context.Context, id int64) (*model.CareType, error)
	List(ctx context.Context, filter *filter.ListCareTypeFilter) ([]*model.CareType, error)
	Update(ctx context.Context, serviceTemplate *model.CareType) error
	Delete(ctx context.Context, id int64) error
}

// repository implements the data access logic for CareType.
type repository struct {
	query *query.Query
}

// NewRepository creates a new repository.
func NewRepository() Repository {
	return &repository{query: query.Use(db.GetDB())}
}

// Create creates a new care type.
func (r *repository) Create(ctx context.Context, serviceTemplate *model.CareType) error {
	return r.query.CareType.WithContext(ctx).Create(serviceTemplate)
}

// Get gets a care type by ID.
func (r *repository) Get(ctx context.Context, id int64) (*model.CareType, error) {
	return r.query.CareType.WithContext(ctx).Where(r.query.CareType.ID.Eq(id)).First()
}

// Update updates a care type.
func (r *repository) Update(ctx context.Context, serviceTemplate *model.CareType) error {
	_, err := r.query.CareType.WithContext(ctx).Where(r.query.CareType.ID.Eq(serviceTemplate.ID)).Updates(serviceTemplate)
	return err
}

// Delete deletes a care type by ID.
func (r *repository) Delete(ctx context.Context, id int64) error {
	_, err := r.query.CareType.WithContext(ctx).Where(r.query.CareType.ID.Eq(id)).Delete()
	return err
}

// List lists care types.
func (r *repository) List(ctx context.Context, filter *filter.ListCareTypeFilter) ([]*model.CareType, error) {
	return r.query.CareType.WithContext(ctx).Find()
}
