load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "caretype",
    srcs = ["care_type_repo.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/caretype",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/db",
        "//backend/app/offering/repo/caretype/filter",
        "//backend/app/offering/repo/model",
        "//backend/app/offering/repo/query",
    ],
)
