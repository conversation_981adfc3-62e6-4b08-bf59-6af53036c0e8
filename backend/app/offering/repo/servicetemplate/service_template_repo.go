package servicetemplate

import (
	"context"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/servicetemplate/filter"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/query"
)

//go:generate mockgen -package=servicetemplate -destination=mocks/mock_service_template_repository.go github.com/moego/backend/app/offering/repo/servicetemplate Repository
type Repository interface {
	Create(ctx context.Context, serviceTemplate *model.ServiceTemplate) error
	Get(ctx context.Context, id int64) (*model.ServiceTemplate, error)
	List(ctx context.Context, filter *filter.ListServiceTemplateFilter) ([]*model.ServiceTemplate, error)
	Update(ctx context.Context, serviceTemplate *model.ServiceTemplate) error
	Delete(ctx context.Context, id int64) error
}

// repository implements the data access logic for ServiceTemplate.
type repository struct {
	query *query.Query
}

// NewRepository creates a new repository.
func NewRepository() Repository {
	return &repository{query: query.Use(db.GetDB())}
}

// Create creates a new service template.
func (r *repository) Create(ctx context.Context, serviceTemplate *model.ServiceTemplate) error {
	return r.query.ServiceTemplate.WithContext(ctx).Create(serviceTemplate)
}

// Get gets a service template by ID.
func (r *repository) Get(ctx context.Context, id int64) (*model.ServiceTemplate, error) {
	return r.query.ServiceTemplate.WithContext(ctx).Where(r.query.ServiceTemplate.ID.Eq(id)).First()
}

// Update updates a service template.
func (r *repository) Update(ctx context.Context, serviceTemplate *model.ServiceTemplate) error {
	_, err := r.query.ServiceTemplate.WithContext(ctx).Where(r.query.ServiceTemplate.ID.Eq(serviceTemplate.ID)).Updates(serviceTemplate)
	return err
}

// Delete deletes a service template by ID.
func (r *repository) Delete(ctx context.Context, id int64) error {
	_, err := r.query.ServiceTemplate.WithContext(ctx).Where(r.query.ServiceTemplate.ID.Eq(id)).Delete()
	return err
}

// List lists service templates.
func (r *repository) List(ctx context.Context, filter *filter.ListServiceTemplateFilter) ([]*model.ServiceTemplate, error) {
	return r.query.ServiceTemplate.WithContext(ctx).Find()
}
