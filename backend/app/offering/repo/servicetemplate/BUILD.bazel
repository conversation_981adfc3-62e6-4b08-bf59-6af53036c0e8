load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "servicetemplate",
    srcs = [
        "entity.go",
        "get.go",
        "service_template.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/servicetemplate",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/db",
        "@io_gorm_gorm//:gorm",
    ],
)
