load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "servicetemplate",
    srcs = ["service_template_repo.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/servicetemplate",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/db",
        "//backend/app/offering/repo/model",
        "//backend/app/offering/repo/query",
        "//backend/app/offering/repo/servicetemplate/filter",
    ],
)
