package servicetemplate

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
)

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	database := db.GetDB()
	return &impl{
		db: database,
	}
}

// List 基于BaseParam和Filter查询ServiceTemplate记录
func (i *impl) List(ctx context.Context, baseParam *BaseParam, filter *Filter) ([]*ServiceTemplate, error) {
	var templates []*ServiceTemplate

	// 使用Model方法，让GORM自动处理表名和字段映射
	query := i.db.WithContext(ctx).Model(&ServiceTemplate{})

	if baseParam == nil {
		return []*ServiceTemplate{}, nil
	}

	// 应用BaseParam过滤条件
	query = i.applyBaseParamFilters(query, baseParam)

	// 应用Filter过滤条件
	if filter != nil {
		query = i.applyFilterConditions(query, filter)
	}

	// 应用分页
	query = i.applyPagination(query, baseParam.PaginationInfo)

	// 按排序字段和创建时间倒序排列
	query = query.Order(ColumnSort + " ASC, " + ColumnCreatedAt + " DESC")

	err := query.Find(&templates).Error
	if err != nil {
		return nil, err
	}

	return templates, nil
}

// GetByIDs 根据ID列表获取ServiceTemplate记录
func (i *impl) GetByIDs(ctx context.Context, ids []int64) ([]*ServiceTemplate, error) {
	if len(ids) == 0 {
		return []*ServiceTemplate{}, nil
	}

	var templates []*ServiceTemplate
	err := i.db.WithContext(ctx).Where(ColumnID+" IN ?", ids).Find(&templates).Error
	if err != nil {
		return nil, err
	}

	return templates, nil
}

// applyBaseParamFilters 应用BaseParam过滤条件
func (i *impl) applyBaseParamFilters(query *gorm.DB, baseParam *BaseParam) *gorm.DB {
	if baseParam.BusinessID != 0 {
		query = query.Where(ColumnBusinessID+" = ?", baseParam.BusinessID)
	}
	if baseParam.CompanyID != 0 {
		query = query.Where(ColumnCompanyID+" = ?", baseParam.CompanyID)
	}
	if !baseParam.StartTime.IsZero() {
		query = query.Where(ColumnCreatedAt+" >= ?", baseParam.StartTime)
	}
	if !baseParam.EndTime.IsZero() {
		query = query.Where(ColumnCreatedAt+" <= ?", baseParam.EndTime)
	}
	return query
}

// applyFilterConditions 应用Filter过滤条件
func (i *impl) applyFilterConditions(query *gorm.DB, filter *Filter) *gorm.DB {
	if len(filter.BusinessIDs) > 0 {
		query = query.Where(ColumnBusinessID+" IN ?", filter.BusinessIDs)
	}
	if len(filter.ServiceTemplateIDs) > 0 {
		query = query.Where(ColumnID+" IN ?", filter.ServiceTemplateIDs)
	}
	if len(filter.CareTypes) > 0 {
		query = query.Where(ColumnCareType+" IN ?", filter.CareTypes)
	}
	if filter.Inactive != nil {
		query = query.Where(ColumnIsActive+" = ?", !*filter.Inactive)
	}
	return query
}

// applyPagination 应用分页条件
func (i *impl) applyPagination(query *gorm.DB, paginationInfo *PaginationInfo) *gorm.DB {
	if paginationInfo != nil {
		if paginationInfo.Offset > 0 {
			query = query.Offset(int(paginationInfo.Offset))
		}
		if paginationInfo.Limit > 0 {
			query = query.Limit(int(paginationInfo.Limit))
		}
	}
	return query
}
