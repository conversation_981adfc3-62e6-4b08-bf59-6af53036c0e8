package servicetemplate

import (
	"time"
)

// 列名常量定义，避免手搓列名
const (
	ColumnBusinessID   = "business_id"
	ColumnCompanyID    = "company_id"
	ColumnCategoryID   = "category_id"
	ColumnCareType     = "care_type"
	ColumnIsActive     = "is_active"
	ColumnSource       = "source"
	ColumnCreatedAt    = "created_at"
	ColumnUpdatedAt    = "updated_at"
	ColumnID           = "id"
	ColumnName         = "name"
	ColumnDescription  = "description"
	ColumnDuration     = "duration"
	ColumnColorCode    = "color_code"
	ColumnPrice        = "price"
	ColumnCurrencyCode = "currency_code"
	ColumnSort         = "sort"
	ColumnImages       = "images"
	ColumnPriceUnit    = "price_unit"
)

type BaseParam struct {
	BusinessID     int32
	CompanyID      int32
	PaginationInfo *PaginationInfo
	StartTime      time.Time
	EndTime        time.Time
}

type Filter struct {
	BusinessIDs        []int64
	ServiceTemplateIDs []int64
	CareTypes          []int32
	ServiceTypes       []int32
	Inactive           *bool
}

type PaginationInfo struct {
	Offset int32
	Limit  int32
}

type ServiceTemplate struct {
	ID           int       `gorm:"column:id;primaryKey;autoIncrement" json:"id" db:"id"`
	CompanyID    int       `gorm:"column:company_id" json:"company_id" db:"company_id"`
	BusinessID   int       `gorm:"column:business_id" json:"business_id" db:"business_id"`
	CategoryID   int       `gorm:"column:category_id" json:"category_id" db:"category_id"`
	CareType     int       `gorm:"column:care_type" json:"care_type" db:"care_type"`
	Name         string    `gorm:"column:name" json:"name" db:"name"`
	Description  string    `gorm:"column:description" json:"description" db:"description"`
	Duration     int       `gorm:"column:duration" json:"duration" db:"duration"`
	ColorCode    string    `gorm:"column:color_code" json:"color_code" db:"color_code"`
	IsActive     bool      `gorm:"column:is_active" json:"is_active" db:"is_active"`
	Price        float64   `gorm:"column:price" json:"price" db:"price"`
	CurrencyCode string    `gorm:"column:currency_code" json:"currency_code" db:"currency_code"`
	Sort         int       `gorm:"column:sort" json:"sort" db:"sort"`
	Images       string    `gorm:"column:images" json:"images" db:"images"`
	Source       int       `gorm:"column:source" json:"source" db:"source"`
	PriceUnit    int       `gorm:"column:price_unit" json:"price_unit" db:"price_unit"`
	CreatedAt    time.Time `gorm:"column:created_at" json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at" json:"updated_at" db:"updated_at"`
}

// TableName 设置表名
func (ServiceTemplate) TableName() string {
	return "moe_service_template"
}
