// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                    db,
		CareType:              newCareType(db, opts...),
		CareTypeAttribute:     newCareTypeAttribute(db, opts...),
		ServiceAttributeValue: newServiceAttributeValue(db, opts...),
		ServiceTemplate:       newServiceTemplate(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	CareType              careType
	CareTypeAttribute     careTypeAttribute
	ServiceAttributeValue serviceAttributeValue
	ServiceTemplate       serviceTemplate
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                    db,
		CareType:              q.CareType.clone(db),
		CareTypeAttribute:     q.CareTypeAttribute.clone(db),
		ServiceAttributeValue: q.ServiceAttributeValue.clone(db),
		ServiceTemplate:       q.ServiceTemplate.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                    db,
		CareType:              q.CareType.replaceDB(db),
		CareTypeAttribute:     q.CareTypeAttribute.replaceDB(db),
		ServiceAttributeValue: q.ServiceAttributeValue.replaceDB(db),
		ServiceTemplate:       q.ServiceTemplate.replaceDB(db),
	}
}

type queryCtx struct {
	CareType              *careTypeDo
	CareTypeAttribute     *careTypeAttributeDo
	ServiceAttributeValue *serviceAttributeValueDo
	ServiceTemplate       *serviceTemplateDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		CareType:              q.CareType.WithContext(ctx),
		CareTypeAttribute:     q.CareTypeAttribute.WithContext(ctx),
		ServiceAttributeValue: q.ServiceAttributeValue.WithContext(ctx),
		ServiceTemplate:       q.ServiceTemplate.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
