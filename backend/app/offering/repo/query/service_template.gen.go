// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
)

func newServiceTemplate(db *gorm.DB, opts ...gen.DOOption) serviceTemplate {
	_serviceTemplate := serviceTemplate{}

	_serviceTemplate.serviceTemplateDo.UseDB(db, opts...)
	_serviceTemplate.serviceTemplateDo.UseModel(&model.ServiceTemplate{})

	tableName := _serviceTemplate.serviceTemplateDo.TableName()
	_serviceTemplate.ALL = field.NewAsterisk(tableName)
	_serviceTemplate.ID = field.NewInt64(tableName, "id")
	_serviceTemplate.OrganizationType = field.NewField(tableName, "organization_type")
	_serviceTemplate.OrganizationID = field.NewInt64(tableName, "organization_id")
	_serviceTemplate.CareTypeID = field.NewInt64(tableName, "care_type_id")
	_serviceTemplate.CategoryID = field.NewInt64(tableName, "category_id")
	_serviceTemplate.Name = field.NewString(tableName, "name")
	_serviceTemplate.Description = field.NewString(tableName, "description")
	_serviceTemplate.ColorCode = field.NewString(tableName, "color_code")
	_serviceTemplate.Sort = field.NewInt64(tableName, "sort")
	_serviceTemplate.Images = field.NewString(tableName, "images")
	_serviceTemplate.Source = field.NewField(tableName, "source")
	_serviceTemplate.IsActive = field.NewBool(tableName, "is_active")
	_serviceTemplate.CreateTime = field.NewTime(tableName, "create_time")
	_serviceTemplate.UpdateTime = field.NewTime(tableName, "update_time")
	_serviceTemplate.DeleteTime = field.NewTime(tableName, "delete_time")

	_serviceTemplate.fillFieldMap()

	return _serviceTemplate
}

type serviceTemplate struct {
	serviceTemplateDo serviceTemplateDo

	ALL              field.Asterisk
	ID               field.Int64  // Primary key ID of the service template
	OrganizationType field.Field  // Organization type: company, enterprise, business
	OrganizationID   field.Int64  // Organization ID corresponding to the type
	CareTypeID       field.Int64  // Reference to care_type used in this service template
	CategoryID       field.Int64  // Optional category to organize service templates
	Name             field.String // Name of the service template, unique within the same organization
	Description      field.String // Optional description of the service template
	ColorCode        field.String // Color code for UI display, such as #F15A2B
	Sort             field.Int64  // Sort order for UI display
	Images           field.String // List of image URLs in JSON array
	Source           field.Field  // Source of the template: 1-MoeGo Platform 2-Enterprise Hub
	IsActive         field.Bool   // Whether the service template is currently active
	CreateTime       field.Time
	UpdateTime       field.Time
	DeleteTime       field.Time

	fieldMap map[string]field.Expr
}

func (s serviceTemplate) Table(newTableName string) *serviceTemplate {
	s.serviceTemplateDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s serviceTemplate) As(alias string) *serviceTemplate {
	s.serviceTemplateDo.DO = *(s.serviceTemplateDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *serviceTemplate) updateTableName(table string) *serviceTemplate {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.OrganizationType = field.NewField(table, "organization_type")
	s.OrganizationID = field.NewInt64(table, "organization_id")
	s.CareTypeID = field.NewInt64(table, "care_type_id")
	s.CategoryID = field.NewInt64(table, "category_id")
	s.Name = field.NewString(table, "name")
	s.Description = field.NewString(table, "description")
	s.ColorCode = field.NewString(table, "color_code")
	s.Sort = field.NewInt64(table, "sort")
	s.Images = field.NewString(table, "images")
	s.Source = field.NewField(table, "source")
	s.IsActive = field.NewBool(table, "is_active")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")
	s.DeleteTime = field.NewTime(table, "delete_time")

	s.fillFieldMap()

	return s
}

func (s *serviceTemplate) WithContext(ctx context.Context) *serviceTemplateDo {
	return s.serviceTemplateDo.WithContext(ctx)
}

func (s serviceTemplate) TableName() string { return s.serviceTemplateDo.TableName() }

func (s serviceTemplate) Alias() string { return s.serviceTemplateDo.Alias() }

func (s serviceTemplate) Columns(cols ...field.Expr) gen.Columns {
	return s.serviceTemplateDo.Columns(cols...)
}

func (s *serviceTemplate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *serviceTemplate) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 15)
	s.fieldMap["id"] = s.ID
	s.fieldMap["organization_type"] = s.OrganizationType
	s.fieldMap["organization_id"] = s.OrganizationID
	s.fieldMap["care_type_id"] = s.CareTypeID
	s.fieldMap["category_id"] = s.CategoryID
	s.fieldMap["name"] = s.Name
	s.fieldMap["description"] = s.Description
	s.fieldMap["color_code"] = s.ColorCode
	s.fieldMap["sort"] = s.Sort
	s.fieldMap["images"] = s.Images
	s.fieldMap["source"] = s.Source
	s.fieldMap["is_active"] = s.IsActive
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["delete_time"] = s.DeleteTime
}

func (s serviceTemplate) clone(db *gorm.DB) serviceTemplate {
	s.serviceTemplateDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s serviceTemplate) replaceDB(db *gorm.DB) serviceTemplate {
	s.serviceTemplateDo.ReplaceDB(db)
	return s
}

type serviceTemplateDo struct{ gen.DO }

func (s serviceTemplateDo) Debug() *serviceTemplateDo {
	return s.withDO(s.DO.Debug())
}

func (s serviceTemplateDo) WithContext(ctx context.Context) *serviceTemplateDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s serviceTemplateDo) ReadDB() *serviceTemplateDo {
	return s.Clauses(dbresolver.Read)
}

func (s serviceTemplateDo) WriteDB() *serviceTemplateDo {
	return s.Clauses(dbresolver.Write)
}

func (s serviceTemplateDo) Session(config *gorm.Session) *serviceTemplateDo {
	return s.withDO(s.DO.Session(config))
}

func (s serviceTemplateDo) Clauses(conds ...clause.Expression) *serviceTemplateDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s serviceTemplateDo) Returning(value interface{}, columns ...string) *serviceTemplateDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s serviceTemplateDo) Not(conds ...gen.Condition) *serviceTemplateDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s serviceTemplateDo) Or(conds ...gen.Condition) *serviceTemplateDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s serviceTemplateDo) Select(conds ...field.Expr) *serviceTemplateDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s serviceTemplateDo) Where(conds ...gen.Condition) *serviceTemplateDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s serviceTemplateDo) Order(conds ...field.Expr) *serviceTemplateDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s serviceTemplateDo) Distinct(cols ...field.Expr) *serviceTemplateDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s serviceTemplateDo) Omit(cols ...field.Expr) *serviceTemplateDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s serviceTemplateDo) Join(table schema.Tabler, on ...field.Expr) *serviceTemplateDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s serviceTemplateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *serviceTemplateDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s serviceTemplateDo) RightJoin(table schema.Tabler, on ...field.Expr) *serviceTemplateDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s serviceTemplateDo) Group(cols ...field.Expr) *serviceTemplateDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s serviceTemplateDo) Having(conds ...gen.Condition) *serviceTemplateDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s serviceTemplateDo) Limit(limit int) *serviceTemplateDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s serviceTemplateDo) Offset(offset int) *serviceTemplateDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s serviceTemplateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *serviceTemplateDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s serviceTemplateDo) Unscoped() *serviceTemplateDo {
	return s.withDO(s.DO.Unscoped())
}

func (s serviceTemplateDo) Create(values ...*model.ServiceTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s serviceTemplateDo) CreateInBatches(values []*model.ServiceTemplate, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s serviceTemplateDo) Save(values ...*model.ServiceTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s serviceTemplateDo) First() (*model.ServiceTemplate, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceTemplate), nil
	}
}

func (s serviceTemplateDo) Take() (*model.ServiceTemplate, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceTemplate), nil
	}
}

func (s serviceTemplateDo) Last() (*model.ServiceTemplate, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceTemplate), nil
	}
}

func (s serviceTemplateDo) Find() ([]*model.ServiceTemplate, error) {
	result, err := s.DO.Find()
	return result.([]*model.ServiceTemplate), err
}

func (s serviceTemplateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ServiceTemplate, err error) {
	buf := make([]*model.ServiceTemplate, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s serviceTemplateDo) FindInBatches(result *[]*model.ServiceTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s serviceTemplateDo) Attrs(attrs ...field.AssignExpr) *serviceTemplateDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s serviceTemplateDo) Assign(attrs ...field.AssignExpr) *serviceTemplateDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s serviceTemplateDo) Joins(fields ...field.RelationField) *serviceTemplateDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s serviceTemplateDo) Preload(fields ...field.RelationField) *serviceTemplateDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s serviceTemplateDo) FirstOrInit() (*model.ServiceTemplate, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceTemplate), nil
	}
}

func (s serviceTemplateDo) FirstOrCreate() (*model.ServiceTemplate, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceTemplate), nil
	}
}

func (s serviceTemplateDo) FindByPage(offset int, limit int) (result []*model.ServiceTemplate, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s serviceTemplateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s serviceTemplateDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s serviceTemplateDo) Delete(models ...*model.ServiceTemplate) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *serviceTemplateDo) withDO(do gen.Dao) *serviceTemplateDo {
	s.DO = *do.(*gen.DO)
	return s
}
