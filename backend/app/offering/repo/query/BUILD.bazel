load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "query",
    srcs = [
        "care_type.gen.go",
        "care_type_attribute.gen.go",
        "gen.go",
        "service_attribute_value.gen.go",
        "service_template.gen.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/query",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/model",
        "@io_gorm_gen//:gen",
        "@io_gorm_gen//field",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
        "@io_gorm_gorm//schema",
        "@io_gorm_plugin_dbresolver//:dbresolver",
    ],
)
