// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
)

func newServiceAttributeValue(db *gorm.DB, opts ...gen.DOOption) serviceAttributeValue {
	_serviceAttributeValue := serviceAttributeValue{}

	_serviceAttributeValue.serviceAttributeValueDo.UseDB(db, opts...)
	_serviceAttributeValue.serviceAttributeValueDo.UseModel(&model.ServiceAttributeValue{})

	tableName := _serviceAttributeValue.serviceAttributeValueDo.TableName()
	_serviceAttributeValue.ALL = field.NewAsterisk(tableName)
	_serviceAttributeValue.ID = field.NewInt64(tableName, "id")
	_serviceAttributeValue.ServiceTemplateID = field.NewInt64(tableName, "service_template_id")
	_serviceAttributeValue.AttributeName = field.NewString(tableName, "attribute_name")
	_serviceAttributeValue.AttributeValue = field.NewString(tableName, "attribute_value")
	_serviceAttributeValue.CreateTime = field.NewTime(tableName, "create_time")
	_serviceAttributeValue.UpdateTime = field.NewTime(tableName, "update_time")
	_serviceAttributeValue.DeleteTime = field.NewTime(tableName, "delete_time")

	_serviceAttributeValue.fillFieldMap()

	return _serviceAttributeValue
}

type serviceAttributeValue struct {
	serviceAttributeValueDo serviceAttributeValueDo

	ALL               field.Asterisk
	ID                field.Int64  // Primary key ID of the service attribute value
	ServiceTemplateID field.Int64  // Reference to the service template
	AttributeName     field.String // Unique name (key) of the attribute within the care type (e.g., size, duration)
	AttributeValue    field.String // Concrete value assigned to the attribute for this service-care_type pair
	CreateTime        field.Time
	UpdateTime        field.Time
	DeleteTime        field.Time

	fieldMap map[string]field.Expr
}

func (s serviceAttributeValue) Table(newTableName string) *serviceAttributeValue {
	s.serviceAttributeValueDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s serviceAttributeValue) As(alias string) *serviceAttributeValue {
	s.serviceAttributeValueDo.DO = *(s.serviceAttributeValueDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *serviceAttributeValue) updateTableName(table string) *serviceAttributeValue {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ServiceTemplateID = field.NewInt64(table, "service_template_id")
	s.AttributeName = field.NewString(table, "attribute_name")
	s.AttributeValue = field.NewString(table, "attribute_value")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")
	s.DeleteTime = field.NewTime(table, "delete_time")

	s.fillFieldMap()

	return s
}

func (s *serviceAttributeValue) WithContext(ctx context.Context) *serviceAttributeValueDo {
	return s.serviceAttributeValueDo.WithContext(ctx)
}

func (s serviceAttributeValue) TableName() string { return s.serviceAttributeValueDo.TableName() }

func (s serviceAttributeValue) Alias() string { return s.serviceAttributeValueDo.Alias() }

func (s serviceAttributeValue) Columns(cols ...field.Expr) gen.Columns {
	return s.serviceAttributeValueDo.Columns(cols...)
}

func (s *serviceAttributeValue) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *serviceAttributeValue) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 7)
	s.fieldMap["id"] = s.ID
	s.fieldMap["service_template_id"] = s.ServiceTemplateID
	s.fieldMap["attribute_name"] = s.AttributeName
	s.fieldMap["attribute_value"] = s.AttributeValue
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["delete_time"] = s.DeleteTime
}

func (s serviceAttributeValue) clone(db *gorm.DB) serviceAttributeValue {
	s.serviceAttributeValueDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s serviceAttributeValue) replaceDB(db *gorm.DB) serviceAttributeValue {
	s.serviceAttributeValueDo.ReplaceDB(db)
	return s
}

type serviceAttributeValueDo struct{ gen.DO }

func (s serviceAttributeValueDo) Debug() *serviceAttributeValueDo {
	return s.withDO(s.DO.Debug())
}

func (s serviceAttributeValueDo) WithContext(ctx context.Context) *serviceAttributeValueDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s serviceAttributeValueDo) ReadDB() *serviceAttributeValueDo {
	return s.Clauses(dbresolver.Read)
}

func (s serviceAttributeValueDo) WriteDB() *serviceAttributeValueDo {
	return s.Clauses(dbresolver.Write)
}

func (s serviceAttributeValueDo) Session(config *gorm.Session) *serviceAttributeValueDo {
	return s.withDO(s.DO.Session(config))
}

func (s serviceAttributeValueDo) Clauses(conds ...clause.Expression) *serviceAttributeValueDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s serviceAttributeValueDo) Returning(value interface{}, columns ...string) *serviceAttributeValueDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s serviceAttributeValueDo) Not(conds ...gen.Condition) *serviceAttributeValueDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s serviceAttributeValueDo) Or(conds ...gen.Condition) *serviceAttributeValueDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s serviceAttributeValueDo) Select(conds ...field.Expr) *serviceAttributeValueDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s serviceAttributeValueDo) Where(conds ...gen.Condition) *serviceAttributeValueDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s serviceAttributeValueDo) Order(conds ...field.Expr) *serviceAttributeValueDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s serviceAttributeValueDo) Distinct(cols ...field.Expr) *serviceAttributeValueDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s serviceAttributeValueDo) Omit(cols ...field.Expr) *serviceAttributeValueDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s serviceAttributeValueDo) Join(table schema.Tabler, on ...field.Expr) *serviceAttributeValueDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s serviceAttributeValueDo) LeftJoin(table schema.Tabler, on ...field.Expr) *serviceAttributeValueDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s serviceAttributeValueDo) RightJoin(table schema.Tabler, on ...field.Expr) *serviceAttributeValueDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s serviceAttributeValueDo) Group(cols ...field.Expr) *serviceAttributeValueDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s serviceAttributeValueDo) Having(conds ...gen.Condition) *serviceAttributeValueDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s serviceAttributeValueDo) Limit(limit int) *serviceAttributeValueDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s serviceAttributeValueDo) Offset(offset int) *serviceAttributeValueDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s serviceAttributeValueDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *serviceAttributeValueDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s serviceAttributeValueDo) Unscoped() *serviceAttributeValueDo {
	return s.withDO(s.DO.Unscoped())
}

func (s serviceAttributeValueDo) Create(values ...*model.ServiceAttributeValue) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s serviceAttributeValueDo) CreateInBatches(values []*model.ServiceAttributeValue, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s serviceAttributeValueDo) Save(values ...*model.ServiceAttributeValue) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s serviceAttributeValueDo) First() (*model.ServiceAttributeValue, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAttributeValue), nil
	}
}

func (s serviceAttributeValueDo) Take() (*model.ServiceAttributeValue, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAttributeValue), nil
	}
}

func (s serviceAttributeValueDo) Last() (*model.ServiceAttributeValue, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAttributeValue), nil
	}
}

func (s serviceAttributeValueDo) Find() ([]*model.ServiceAttributeValue, error) {
	result, err := s.DO.Find()
	return result.([]*model.ServiceAttributeValue), err
}

func (s serviceAttributeValueDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ServiceAttributeValue, err error) {
	buf := make([]*model.ServiceAttributeValue, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s serviceAttributeValueDo) FindInBatches(result *[]*model.ServiceAttributeValue, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s serviceAttributeValueDo) Attrs(attrs ...field.AssignExpr) *serviceAttributeValueDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s serviceAttributeValueDo) Assign(attrs ...field.AssignExpr) *serviceAttributeValueDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s serviceAttributeValueDo) Joins(fields ...field.RelationField) *serviceAttributeValueDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s serviceAttributeValueDo) Preload(fields ...field.RelationField) *serviceAttributeValueDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s serviceAttributeValueDo) FirstOrInit() (*model.ServiceAttributeValue, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAttributeValue), nil
	}
}

func (s serviceAttributeValueDo) FirstOrCreate() (*model.ServiceAttributeValue, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAttributeValue), nil
	}
}

func (s serviceAttributeValueDo) FindByPage(offset int, limit int) (result []*model.ServiceAttributeValue, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s serviceAttributeValueDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s serviceAttributeValueDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s serviceAttributeValueDo) Delete(models ...*model.ServiceAttributeValue) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *serviceAttributeValueDo) withDO(do gen.Dao) *serviceAttributeValueDo {
	s.DO = *do.(*gen.DO)
	return s
}
