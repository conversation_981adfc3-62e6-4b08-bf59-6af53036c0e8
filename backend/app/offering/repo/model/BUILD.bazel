load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "model",
    srcs = [
        "care_type.gen.go",
        "care_type_attribute.gen.go",
        "service_attribute_value.gen.go",
        "service_template.gen.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/model",
    visibility = ["//visibility:public"],
    deps = ["//backend/proto/offering/v1:offering"],
)
