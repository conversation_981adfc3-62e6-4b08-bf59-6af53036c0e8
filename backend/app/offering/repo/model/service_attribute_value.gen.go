// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameServiceAttributeValue = "service_attribute_value"

// ServiceAttributeValue mapped from table <service_attribute_value>
type ServiceAttributeValue struct {
	ID                int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:Primary key ID of the service attribute value" json:"id"`                                                     // Primary key ID of the service attribute value
	ServiceTemplateID int64      `gorm:"column:service_template_id;type:bigint;not null;comment:Reference to the service template" json:"service_template_id"`                                                    // Reference to the service template
	AttributeName     string     `gorm:"column:attribute_name;type:character varying(255);not null;comment:Unique name (key) of the attribute within the care type (e.g., size, duration)" json:"attribute_name"` // Unique name (key) of the attribute within the care type (e.g., size, duration)
	AttributeValue    string     `gorm:"column:attribute_value;type:text;not null;comment:Concrete value assigned to the attribute for this service-care_type pair" json:"attribute_value"`                       // Concrete value assigned to the attribute for this service-care_type pair
	CreateTime        *time.Time `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	UpdateTime        *time.Time `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
	DeleteTime        *time.Time `gorm:"column:delete_time;type:timestamp with time zone" json:"delete_time"`
}

// TableName ServiceAttributeValue's table name
func (*ServiceAttributeValue) TableName() string {
	return TableNameServiceAttributeValue
}
