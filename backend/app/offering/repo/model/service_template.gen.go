// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	"time"
)

const TableNameServiceTemplate = "service_template"

// ServiceTemplate mapped from table <service_template>
type ServiceTemplate struct {
	ID               int64                       `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:Primary key ID of the service template" json:"id"`                                                   // Primary key ID of the service template
	OrganizationType offeringpb.OrganizationType `gorm:"column:organization_type;type:character varying(50);not null;default:company;comment:Organization type: company, enterprise, business" json:"organization_type"` // Organization type: company, enterprise, business
	OrganizationID   int64                       `gorm:"column:organization_id;type:bigint;not null;comment:Organization ID corresponding to the type" json:"organization_id"`                                           // Organization ID corresponding to the type
	CareTypeID       int64                       `gorm:"column:care_type_id;type:bigint;not null;index:idx_care_type,priority:1;comment:Reference to care_type used in this service template" json:"care_type_id"`       // Reference to care_type used in this service template
	CategoryID       int64                       `gorm:"column:category_id;type:bigint;not null;comment:Optional category to organize service templates" json:"category_id"`                                             // Optional category to organize service templates
	Name             string                      `gorm:"column:name;type:character varying(255);not null;comment:Name of the service template, unique within the same organization" json:"name"`                         // Name of the service template, unique within the same organization
	Description      *string                     `gorm:"column:description;type:text;comment:Optional description of the service template" json:"description"`                                                           // Optional description of the service template
	ColorCode        *string                     `gorm:"column:color_code;type:character varying(7);comment:Color code for UI display, such as #F15A2B" json:"color_code"`                                               // Color code for UI display, such as #F15A2B
	Sort             int64                       `gorm:"column:sort;type:bigint;not null;comment:Sort order for UI display" json:"sort"`                                                                                 // Sort order for UI display
	Images           *string                     `gorm:"column:images;type:jsonb;default:[];comment:List of image URLs in JSON array" json:"images"`                                                                     // List of image URLs in JSON array
	Source           offeringpb.ServiceSource    `gorm:"column:source;type:integer;not null;comment:Source of the template: 1-MoeGo Platform 2-Enterprise Hub" json:"source"`                                            // Source of the template: 1-MoeGo Platform 2-Enterprise Hub
	IsActive         bool                        `gorm:"column:is_active;type:boolean;not null;comment:Whether the service template is currently active" json:"is_active"`                                               // Whether the service template is currently active
	CreateTime       *time.Time                  `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	UpdateTime       *time.Time                  `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
	DeleteTime       *time.Time                  `gorm:"column:delete_time;type:timestamp with time zone" json:"delete_time"`
}

// TableName ServiceTemplate's table name
func (*ServiceTemplate) TableName() string {
	return TableNameServiceTemplate
}
