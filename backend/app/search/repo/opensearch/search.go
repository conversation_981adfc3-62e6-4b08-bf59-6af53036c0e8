package opensearch

import (
	"bytes"
	"context"
	"io"
	"net/http"

	"github.com/bytedance/sonic"
	"github.com/opensearch-project/opensearch-go/opensearchapi"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func (i *impl) Search(ctx context.Context, index string, request *SearchRequest) (*SearchResponse, error) {
	jsonBody, err := sonic.Marshal(request)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to marshal query body")
	}

	log.DebugContextf(ctx, "debug search request %+v", string(jsonBody))
	searchreq := opensearchapi.SearchRequest{
		Index: []string{index},
		Body:  bytes.NewReader(jsonBody),
	}

	resp, err := searchreq.Do(ctx, i.client)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to search: %v", err)
	}
	if resp.StatusCode >= http.StatusBadRequest {
		log.InfoContext(ctx, "failed to search", zap.Any("resp", resp))
		return nil, status.Errorf(codes.Internal, "failed to search: %v", resp.Status())
	}

	// 读取响应体
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			log.ErrorContext(ctx, "failed to close search response body", zap.Error(err))
		}
	}(resp.Body)

	var searchResp SearchResponse
	if err := sonic.ConfigDefault.NewDecoder(resp.Body).Decode(&searchResp); err != nil {
		return nil, status.Errorf(codes.Internal, "failed to decode search response: %v", err)
	}
	return &searchResp, nil
}
