package opensearch

import (
	"context"
	"fmt"
	"sync"

	"github.com/aws/aws-sdk-go-v2/config"
	cred "github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/opensearch-project/opensearch-go/opensearchapi"
	opensearch "github.com/opensearch-project/opensearch-go/v4"
	requestsigner "github.com/opensearch-project/opensearch-go/v4/signer/awsv2"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	rconfig "github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
)

type OpenSearch interface {
	CreateIndex(ctx context.Context, index string, request *CreateIndexRequest) error

	// Replace the document if it exists, create it if it does not exist
	IndexDocument(ctx context.Context, request *IndexDocumentRequest) error

	// The Bulk API allows multiple indexing operations to be performed in a single request,
	// such as creating, indexing, updating, or deleting documents
	Bulk(ctx context.Context, bulkreq *opensearchapi.BulkRequest) (*BulkResponse, error)

	// Search Document
	Search(ctx context.Context, index string, request *SearchRequest) (*SearchResponse, error)
}

type impl struct {
	client *opensearch.Client
}

const (
	ENDPOINT = "https://search-business-customer-dev-v1-rjuuv5z44hjr5f5m64hzomqx4u.us-west-2.es.amazonaws.com"
	REGION   = "us-west-2"
)

func getConfig(c rconfig.Config, key string) string {
	if r, ok := c.Get(key, "").(string); ok {
		return r
	}
	panic(fmt.Sprintf("failed to get config: %s", key))
}

var (
	openSearch OpenSearch
	once       sync.Once
)

func SetGlobalOpenSearch(o OpenSearch) {
	once.Do(func() {
		openSearch = o
	})
}

func New() OpenSearch {
	once.Do(func() {
		c, err := rconfig.Load(
			rpc.ServerConfigPath,
			rconfig.WithCodec("yaml"),
			rconfig.WithProvider("file"),
		)

		if err != nil {
			panic(err)
		}

		os, err := NewOpenSearch(
			getConfig(c, "opensearch.endpoint"),
			getConfig(c, "opensearch.accessKey"),
			getConfig(c, "opensearch.secretKey"),
		)
		if err != nil {
			panic(err)
		}
		openSearch = &impl{
			client: os,
		}
	})

	return openSearch
}

func NewTesting() OpenSearch {
	os, err := NewOpenSearch(
		"https://search-business-customer-dev-v1-rjuuv5z44hjr5f5m64hzomqx4u.us-west-2.es.amazonaws.com",
		"********************",
		"qkqeOrqxN+OI0TE9/HumFUJ5/fRBvp8kwK6LajVB",
	)
	if err != nil {
		panic(err)
	}
	openSearch = &impl{
		client: os,
	}

	return openSearch
}

// NewOpenSearch 创建OpenSearch客户端实例
func NewOpenSearch(endpoint, accessKey, secretKey string) (*opensearch.Client, error) {
	ctx := context.Background()

	// log.DebugContext(ctx, "NewOpenSearch",
	//	zap.String("endpoint", endpoint),
	//	zap.String("accessKey", accessKey),
	//	zap.String("secretKey", secretKey),
	//)

	awsCfg, err := config.LoadDefaultConfig(ctx,
		config.WithCredentialsProvider(
			cred.NewStaticCredentialsProvider(accessKey, secretKey, ""),
		),
	)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to load AWS config: %v", err)
	}

	signer, err := requestsigner.NewSignerWithService(awsCfg, "es")
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to create request signer: %v", err)
	}

	config := opensearch.Config{
		Addresses: []string{endpoint},
		Signer:    signer,
	}

	client, err := opensearch.NewClient(config)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to create OpenSearch client: %v", err)
	}

	return client, nil
}
