// Code generated by MockGen. DO NOT EDIT.
// Source: opensearch.go
//
// Generated by this command:
//
//	mockgen -destination=./mock/opensearch_mock.go -package=mock -source=opensearch.go
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	opensearch "github.com/MoeGolibrary/moego/backend/app/search/repo/opensearch"
	opensearchapi "github.com/opensearch-project/opensearch-go/opensearchapi"
	gomock "go.uber.org/mock/gomock"
)

// MockOpenSearch is a mock of OpenSearch interface.
type MockOpenSearch struct {
	ctrl     *gomock.Controller
	recorder *MockOpenSearchMockRecorder
	isgomock struct{}
}

// MockOpenSearchMockRecorder is the mock recorder for MockOpenSearch.
type MockOpenSearchMockRecorder struct {
	mock *MockOpenSearch
}

// NewMockOpenSearch creates a new mock instance.
func NewMockOpenSearch(ctrl *gomock.Controller) *MockOpenSearch {
	mock := &MockOpenSearch{ctrl: ctrl}
	mock.recorder = &MockOpenSearchMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOpenSearch) EXPECT() *MockOpenSearchMockRecorder {
	return m.recorder
}

// Bulk mocks base method.
func (m *MockOpenSearch) Bulk(ctx context.Context, bulkreq *opensearchapi.BulkRequest) (*opensearch.BulkResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Bulk", ctx, bulkreq)
	ret0, _ := ret[0].(*opensearch.BulkResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Bulk indicates an expected call of Bulk.
func (mr *MockOpenSearchMockRecorder) Bulk(ctx, bulkreq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Bulk", reflect.TypeOf((*MockOpenSearch)(nil).Bulk), ctx, bulkreq)
}

// CreateIndex mocks base method.
func (m *MockOpenSearch) CreateIndex(ctx context.Context, index string, request *opensearch.CreateIndexRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateIndex", ctx, index, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateIndex indicates an expected call of CreateIndex.
func (mr *MockOpenSearchMockRecorder) CreateIndex(ctx, index, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIndex", reflect.TypeOf((*MockOpenSearch)(nil).CreateIndex), ctx, index, request)
}

// IndexDocument mocks base method.
func (m *MockOpenSearch) IndexDocument(ctx context.Context, request *opensearch.IndexDocumentRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IndexDocument", ctx, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// IndexDocument indicates an expected call of IndexDocument.
func (mr *MockOpenSearchMockRecorder) IndexDocument(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IndexDocument", reflect.TypeOf((*MockOpenSearch)(nil).IndexDocument), ctx, request)
}

// Search mocks base method.
func (m *MockOpenSearch) Search(ctx context.Context, index string, request *opensearch.SearchRequest) (*opensearch.SearchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Search", ctx, index, request)
	ret0, _ := ret[0].(*opensearch.SearchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Search indicates an expected call of Search.
func (mr *MockOpenSearchMockRecorder) Search(ctx, index, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Search", reflect.TypeOf((*MockOpenSearch)(nil).Search), ctx, index, request)
}
