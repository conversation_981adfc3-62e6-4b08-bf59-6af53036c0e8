load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "document",
    srcs = [
        "converter.go",
        "document.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/search/logic/document",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/search/repo/opensearch",
        "//backend/common/rpc/framework/log",
        "//backend/proto/search/v1:search",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_opensearch_project_opensearch_go//opensearchapi",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "document_test",
    srcs = ["document_test.go"],
    deps = [
        ":document",
        "//backend/app/search/repo/opensearch",
        "//backend/app/search/repo/opensearch/mock",
        "//backend/proto/search/v1:search",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_opensearch_project_opensearch_go//opensearchapi",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_uber_go_mock//gomock",
    ],
)
