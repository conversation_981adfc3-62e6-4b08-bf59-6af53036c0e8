// Code generated by MockGen. DO NOT EDIT.
// Source: ./sales/moego_pay_custom_fee_approval.go
//
// Generated by this command:
//
//	mockgen -source=./sales/moego_pay_custom_fee_approval.go -destination=mock/./sales/moego_pay_custom_fee_approval_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	sales "github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	gomock "go.uber.org/mock/gomock"
)

// MockMoegoPayCustomFeeApprovalReadWriter is a mock of MoegoPayCustomFeeApprovalReadWriter interface.
type MockMoegoPayCustomFeeApprovalReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockMoegoPayCustomFeeApprovalReadWriterMockRecorder
	isgomock struct{}
}

// MockMoegoPayCustomFeeApprovalReadWriterMockRecorder is the mock recorder for MockMoegoPayCustomFeeApprovalReadWriter.
type MockMoegoPayCustomFeeApprovalReadWriterMockRecorder struct {
	mock *MockMoegoPayCustomFeeApprovalReadWriter
}

// NewMockMoegoPayCustomFeeApprovalReadWriter creates a new mock instance.
func NewMockMoegoPayCustomFeeApprovalReadWriter(ctrl *gomock.Controller) *MockMoegoPayCustomFeeApprovalReadWriter {
	mock := &MockMoegoPayCustomFeeApprovalReadWriter{ctrl: ctrl}
	mock.recorder = &MockMoegoPayCustomFeeApprovalReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMoegoPayCustomFeeApprovalReadWriter) EXPECT() *MockMoegoPayCustomFeeApprovalReadWriterMockRecorder {
	return m.recorder
}

// Count mocks base method.
func (m *MockMoegoPayCustomFeeApprovalReadWriter) Count(ctx context.Context, filters ...sales.MoegoPayCustomFeeApprovalQueryFilter) (int64, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx}
	for _, a := range filters {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Count", varargs...)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Count indicates an expected call of Count.
func (mr *MockMoegoPayCustomFeeApprovalReadWriterMockRecorder) Count(ctx any, filters ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx}, filters...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockMoegoPayCustomFeeApprovalReadWriter)(nil).Count), varargs...)
}

// Get mocks base method.
func (m *MockMoegoPayCustomFeeApprovalReadWriter) Get(ctx context.Context, id string) (*sales.MoegoPayCustomFeeApproval, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*sales.MoegoPayCustomFeeApproval)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockMoegoPayCustomFeeApprovalReadWriterMockRecorder) Get(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockMoegoPayCustomFeeApprovalReadWriter)(nil).Get), ctx, id)
}

// List mocks base method.
func (m *MockMoegoPayCustomFeeApprovalReadWriter) List(ctx context.Context, page sales.Page[*sales.MoegoPayCustomFeeApproval], filters ...sales.MoegoPayCustomFeeApprovalQueryFilter) ([]*sales.MoegoPayCustomFeeApproval, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, page}
	for _, a := range filters {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "List", varargs...)
	ret0, _ := ret[0].([]*sales.MoegoPayCustomFeeApproval)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockMoegoPayCustomFeeApprovalReadWriterMockRecorder) List(ctx, page any, filters ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, page}, filters...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockMoegoPayCustomFeeApprovalReadWriter)(nil).List), varargs...)
}

// Save mocks base method.
func (m *MockMoegoPayCustomFeeApprovalReadWriter) Save(ctx context.Context, approval *sales.MoegoPayCustomFeeApproval) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", ctx, approval)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockMoegoPayCustomFeeApprovalReadWriterMockRecorder) Save(ctx, approval any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockMoegoPayCustomFeeApprovalReadWriter)(nil).Save), ctx, approval)
}
