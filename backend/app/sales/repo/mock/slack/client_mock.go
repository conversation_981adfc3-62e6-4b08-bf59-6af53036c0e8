// Code generated by MockGen. DO NOT EDIT.
// Source: ./slack/client.go
//
// Generated by this command:
//
//	mockgen -source=./slack/client.go -destination=mock/./slack/client_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	slack "github.com/slack-go/slack"
	gomock "go.uber.org/mock/gomock"
)

// MockWebhookClient is a mock of WebhookClient interface.
type MockWebhookClient struct {
	ctrl     *gomock.Controller
	recorder *MockWebhookClientMockRecorder
	isgomock struct{}
}

// MockWebhookClientMockRecorder is the mock recorder for MockWebhookClient.
type MockWebhookClientMockRecorder struct {
	mock *MockWebhookClient
}

// NewMockWebhookClient creates a new mock instance.
func NewMockWebhookClient(ctrl *gomock.Controller) *MockWebhookClient {
	mock := &MockWebhookClient{ctrl: ctrl}
	mock.recorder = &MockWebhookClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWebhookClient) EXPECT() *MockWebhookClientMockRecorder {
	return m.recorder
}

// SendMessage mocks base method.
func (m *MockWebhookClient) SendMessage(ctx context.Context, webhookPath string, msg *slack.Message) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMessage", ctx, webhookPath, msg)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMessage indicates an expected call of SendMessage.
func (mr *MockWebhookClientMockRecorder) SendMessage(ctx, webhookPath, msg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMessage", reflect.TypeOf((*MockWebhookClient)(nil).SendMessage), ctx, webhookPath, msg)
}
