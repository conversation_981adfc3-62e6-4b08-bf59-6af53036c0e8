package salesforce

import (
	"context"
	"fmt"
	"strings"

	"github.com/MoeGolibrary/moego/backend/app/sales/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Product struct {
	ID        string   `json:"Id,omitempty"`
	Name      *string  `json:"Name,omitempty"`
	Family    *string  `json:"Family,omitempty"`
	Type      *string  `json:"Product_Type__c,omitempty"`
	ListPrice *float64 `json:"List_Price__c,omitempty"`
}

func (i *impl) listProducts(ctx context.Context) ([]*Product, error) {
	query := fmt.Sprintf("q=SELECT+%s+FROM+Product2", strings.Join(utils.ParseStructJSONTagNames(Product{}), ","))
	result := &queryResult[*Product]{}

	if err := i.Query(ctx, query, result); err != nil {
		return nil, err
	}
	return result.Records, nil
}

func (i *impl) GetProduct(ctx context.Context, name string) *Product {
	product, ok := products[name]
	if !ok {
		log.WarnContextf(ctx, "Product not found: %s", name)
		return nil
	}
	return product
}
