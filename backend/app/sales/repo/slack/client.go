package slack

import (
	"context"

	"github.com/slack-go/slack"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
)

type WebhookClient interface {
	SendMessage(ctx context.Context, webhookPath string, msg *slack.Message) error
}

type impl struct {
	client http.Client
}

func New() WebhookClient {
	return &impl{
		client: http.NewClientProxy("https://hooks.slack.com"),
	}
}

func (i *impl) SendMessage(ctx context.Context, webhookPath string, msg *slack.Message) error {
	return i.client.Post(ctx, webhookPath, msg, nil)
}
