load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "organization",
    srcs = ["organization.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/sales/repo/organization",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/codec/grpc",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/organization/v1:organization",
    ],
)
