package account

import (
	"context"

	accountpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	accountsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
)

type Account interface {
	GetAccount(ctx context.Context, id int64) (*accountpb.AccountModel, error)
}

type impl struct {
	client accountsvcpb.AccountServiceClient
}

func New() Account {
	return &impl{
		client: grpc.NewClient("moego-svc-account", accountsvcpb.NewAccountServiceClient),
	}
}

func (i *impl) GetAccount(ctx context.Context, id int64) (*accountpb.AccountModel, error) {
	return i.client.GetAccount(ctx, &accountsvcpb.GetAccountRequest{
		Identifier: &accountsvcpb.GetAccountRequest_Id{Id: id},
	})
}
