load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "utils",
    srcs = [
        "convert.go",
        "env.go",
        "utils.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/sales/utils",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/proto/sales/v1:sales",
        "//third_party/googleapis/google/type:decimal_go_proto",
        "//third_party/googleapis/google/type:money_go_proto",
        "@com_github_google_uuid//:uuid",
        "@com_github_shopspring_decimal//:decimal",
        "@io_gorm_datatypes//:datatypes",
    ],
)

go_test(
    name = "utils_test",
    srcs = [
        "convert_test.go",
        "utils_test.go",
    ],
    embed = [":utils"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/proto/sales/v1:sales",
        "@com_github_stretchr_testify//require",
    ],
)
