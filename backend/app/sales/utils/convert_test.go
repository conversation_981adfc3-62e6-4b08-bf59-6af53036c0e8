package utils_test

import (
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/MoeGolibrary/moego/backend/app/sales/utils"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

func TestGetContractTerm(t *testing.T) {
	require.Equal(t, utils.ContractTermOneYear, *utils.GetContractTerm(salespb.SubscriptionTerm_ONE_YEAR))
	require.Equal(t, utils.ContractTermTwoYears, *utils.GetContractTerm(salespb.SubscriptionTerm_TWO_YEARS))
	require.Equal(t, utils.ContractTermThreeYears, *utils.GetContractTerm(salespb.SubscriptionTerm_THREE_YEARS))
	require.Nil(t, utils.GetContractTerm(salespb.SubscriptionTerm_MONTHLY))
	require.Nil(t, utils.GetContractTerm(salespb.SubscriptionTerm_SUBSCRIPTION_TERM_UNSPECIFIED))
}
