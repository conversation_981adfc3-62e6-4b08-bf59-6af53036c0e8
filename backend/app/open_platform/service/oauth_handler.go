package service

import (
	"net/http"
	"os"
	"strconv"

	"github.com/MoeGolibrary/moego/backend/app/open_platform/constdef"
	"github.com/MoeGolibrary/moego/backend/app/open_platform/logic/oauth"
)

type OAuthHandler struct {
	ol *oauth.Logic
}

func NewOAuthHandler() *OAuthHandler {
	return &OAuthHandler{
		ol: oauth.New(),
	}
}

const (
	// lint do it
	prodRedirectURL = "https://go.moego.pet/" +
		"setting/integration/googleAds?adsCallbackSource=google"
	testRedirectURL = "https://go.t2.moego.dev/" +
		"setting/integration/googleAds?adsCallbackSource=google"
)

func (h *OAuthHandler) GoogleOAuthCallBackHandler(w http.ResponseWriter, r *http.Request) {
	if err := h.ol.CreateCompanyToken(r.Context(), &oauth.CreateCompanyTokenDatum{
		Code:       r.URL.Query().Get("code"),
		CompanyID:  StringToInt64WithDefault(r.Header.Get(string(constdef.HeaderKeyCompanyID)), 0),
		BusinessID: StringToInt64WithDefault(r.Header.Get(string(constdef.HeaderKeyBusinessID)), 0),
		StaffID:    StringToInt64WithDefault(r.Header.Get(string(constdef.HeaderKeyStaffID)), 0),
	}); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	// redirect to setting
	http.Redirect(w, r, getRedirectURL(), http.StatusFound)
}

func (h *OAuthHandler) MetaOAuthCallBackHandler(w http.ResponseWriter, r *http.Request) {
	if err := h.ol.CreateMetaCompanyToken(r.Context(), &oauth.CreateCompanyTokenDatum{
		Code:       r.URL.Query().Get("code"),
		CompanyID:  StringToInt64WithDefault(r.Header.Get(string(constdef.HeaderKeyCompanyID)), 0),
		BusinessID: StringToInt64WithDefault(r.Header.Get(string(constdef.HeaderKeyBusinessID)), 0),
		StaffID:    StringToInt64WithDefault(r.Header.Get(string(constdef.HeaderKeyStaffID)), 0),
	}); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	// redirect to setting
	http.Redirect(w, r, "https://go.t2.moego.dev/", http.StatusFound)
}

func getRedirectURL() string {
	if os.Getenv("MOEGO_ENVIRONMENT") == "production" {
		return prodRedirectURL
	}
	return testRedirectURL
}

func StringToInt64WithDefault(s string, defaultValue int64) int64 {
	value, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		return defaultValue
	}
	return value
}
