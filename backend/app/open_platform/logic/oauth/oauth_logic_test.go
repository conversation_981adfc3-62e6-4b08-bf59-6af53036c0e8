package oauth

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/bytedance/sonic"
	"github.com/lib/pq"
	"github.com/shenzhencenter/google-ads-pb/resources"
	"github.com/shenzhencenter/google-ads-pb/services"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"golang.org/x/oauth2"
	oauth2api "google.golang.org/api/oauth2/v2"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	ggorm "gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/app/open_platform/repo/db"
	googleadssetting "github.com/MoeGolibrary/moego/backend/app/open_platform/repo/db/google_ads_setting"
	mockSetting "github.com/MoeGolibrary/moego/backend/app/open_platform/repo/db/google_ads_setting/mock" // 导入 google // mock
	moegoToken "github.com/MoeGolibrary/moego/backend/app/open_platform/repo/db/token"
	mockToken "github.com/MoeGolibrary/moego/backend/app/open_platform/repo/db/token/mock" // 导入 token mock
	mockGoogle "github.com/MoeGolibrary/moego/backend/app/open_platform/repo/google/mock"  // 导入 google mock
	"github.com/MoeGolibrary/moego/backend/app/open_platform/repo/meta"
	mockMeta "github.com/MoeGolibrary/moego/backend/app/open_platform/repo/meta/mock" // 导入 meta mock
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	openplatformpb "github.com/MoeGolibrary/moego/backend/proto/open_platform/v1"
)

func TestLogic_New(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		db.SetDB(&ggorm.DB{})
		logic := New()
		assert.NotNil(t, logic)
	})
}

func TestLogic_CreateCompanyToken(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	mockGoogleRepo := mockGoogle.NewMockReadWriter(ctrl)
	mockTokenRepo := mockToken.NewMockReadWriter(ctrl)

	logic := &Logic{
		googleRepo: mockGoogleRepo,
		tokenRepo:  mockTokenRepo,
	}

	validDatum := &CreateCompanyTokenDatum{
		Code:       "valid_code",
		CompanyID:  1,
		BusinessID: 2,
		StaffID:    3,
	}

	googleToken := &oauth2.Token{
		AccessToken:  "access_token_abc",
		RefreshToken: "refresh_token_xyz",
		TokenType:    "Bearer",
		Expiry:       time.Now().Add(time.Hour),
	}

	t.Run("invalid params - nil datum", func(t *testing.T) {
		err := logic.CreateCompanyToken(ctx, nil)
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("invalid params - empty code", func(t *testing.T) {
		datum := *validDatum
		datum.Code = ""
		err := logic.CreateCompanyToken(ctx, &datum)
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("invalid params - zero companyID", func(t *testing.T) {
		datum := *validDatum
		datum.CompanyID = 0
		err := logic.CreateCompanyToken(ctx, &datum)
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("exchange code error", func(t *testing.T) {
		mockGoogleRepo.EXPECT().ExchangeCode(ctx, validDatum.Code).Return(nil, fmt.Errorf("google exchange error"))

		err := logic.CreateCompanyToken(ctx, validDatum)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "google exchange error")
	})

	t.Run("create token error", func(t *testing.T) {
		mockGoogleRepo.EXPECT().ExchangeCode(ctx, validDatum.Code).Return(googleToken, nil)
		// Expect Create to be called with a token containing marshaled data and correct IDs
		mockTokenRepo.EXPECT().Create(ctx, gomock.Any()).Return(fmt.Errorf("db create error"))

		err := logic.CreateCompanyToken(ctx, validDatum)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "db create error")
	})

	t.Run("success", func(t *testing.T) {
		mockGoogleRepo.EXPECT().ExchangeCode(ctx, validDatum.Code).Return(googleToken, nil)
		// Expect Create to be called with a token containing marshaled data and correct IDs
		mockTokenRepo.EXPECT().Create(ctx, gomock.Any()).DoAndReturn(func(_ context.Context, token *moegoToken.OauthToken) error {
			// Optional: Add assertions about the token being saved
			assert.Equal(t, validDatum.CompanyID, token.CompanyID)
			assert.Equal(t, validDatum.BusinessID, token.BusinessID)
			assert.Equal(t, validDatum.StaffID, token.CreatedBy)
			assert.Equal(t, validDatum.StaffID, token.UpdatedBy)
			assert.Equal(t, moegoToken.TokenTypeGoogleOauth2Ads, token.Type)
			// We could unmarshal token.Token and check its content if needed
			return nil
		})

		err := logic.CreateCompanyToken(ctx, validDatum)
		assert.NoError(t, err)
	})
}

func TestLogic_GetGoogleAdsUserInfo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	mockGoogleRepo := mockGoogle.NewMockReadWriter(ctrl)
	mockTokenRepo := mockToken.NewMockReadWriter(ctrl)
	mockAdsSettingRepo := mockSetting.NewMockReadWriter(ctrl)

	logic := &Logic{
		googleRepo:     mockGoogleRepo,
		tokenRepo:      mockTokenRepo,
		adsSettingRepo: mockAdsSettingRepo,
	}

	validCompanyID := int64(123)
	sampleToken := &oauth2.Token{
		AccessToken: "access_token_user_info",
		Expiry:      time.Now().Add(time.Hour),
	}
	sampleTokenStr, _ := sonic.MarshalString(sampleToken)
	sampleDBToken := &moegoToken.OauthToken{
		ID:        1,
		CompanyID: validCompanyID,
		Type:      moegoToken.TokenTypeGoogleOauth2Ads,
		Token:     sampleTokenStr,
	}

	sampleUserInfo := &oauth2api.Userinfo{
		Email: "<EMAIL>",
		Name:  "Test User",
		Id:    "1234567890",
	}

	sampleSetting := &googleadssetting.GoogleAdsSetting{
		ID:             123,
		CompanyID:      123,
		BusinessID:     123,
		AdsCustomerIDs: []int64{2131, 1231},
	}

	t.Run("invalid params - zero companyID", func(t *testing.T) {
		userInfo, err := logic.GetGoogleAdsUserInfo(ctx, 0)
		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("getCompanyGoogleAdsToken error - list error", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return(nil, fmt.Errorf("db list error"))

		userInfo, err := logic.GetGoogleAdsUserInfo(ctx, validCompanyID)
		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "db list error")
	})

	t.Run("getCompanyGoogleAdsToken error - token not found", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{}, nil)

		userInfo, err := logic.GetGoogleAdsUserInfo(ctx, validCompanyID)
		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Equal(t, errs.New(openplatformpb.ErrCode_ERR_CODE_OAUTH_NOT_FOUND), err)
	})

	t.Run("GetUserInfo error", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{sampleDBToken}, nil)
		mockGoogleRepo.EXPECT().GetUserInfo(ctx, gomock.Any()).Return(nil, fmt.Errorf("google user info error"))

		userInfo, err := logic.GetGoogleAdsUserInfo(ctx, validCompanyID)
		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "google user info error")
	})

	t.Run("success", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{sampleDBToken}, nil)
		mockGoogleRepo.EXPECT().GetUserInfo(ctx, gomock.Any()).Return(sampleUserInfo, nil)
		mockAdsSettingRepo.EXPECT().List(ctx, gomock.Any()).Return([]*googleadssetting.GoogleAdsSetting{}, nil) // Mock ads setting list

		userInfo, err := logic.GetGoogleAdsUserInfo(ctx, validCompanyID)
		assert.NoError(t, err)
		assert.NotNil(t, userInfo)
		assert.NotNil(t, userInfo.UserInfo) // Ensure UserInfo struct is not nil
		assert.Equal(t, sampleUserInfo.Email, userInfo.UserInfo.Email)
		assert.Equal(t, sampleUserInfo.Name, userInfo.UserInfo.Name)
		assert.Equal(t, sampleUserInfo.Id, userInfo.UserInfo.Id)
		assert.Nil(t, userInfo.AdsSetting)
	})

	t.Run("success", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{sampleDBToken}, nil)
		mockGoogleRepo.EXPECT().GetUserInfo(ctx, gomock.Any()).Return(sampleUserInfo, nil)
		mockAdsSettingRepo.EXPECT().List(ctx, gomock.Any()).Return([]*googleadssetting.GoogleAdsSetting{sampleSetting}, nil) // Mock ads setting list

		userInfo, err := logic.GetGoogleAdsUserInfo(ctx, validCompanyID)
		assert.NoError(t, err)
		assert.NotNil(t, userInfo)
		assert.NotNil(t, userInfo.UserInfo) // Ensure UserInfo struct is not nil
		assert.Equal(t, sampleUserInfo.Email, userInfo.UserInfo.Email)
		assert.Equal(t, sampleUserInfo.Name, userInfo.UserInfo.Name)
		assert.Equal(t, sampleUserInfo.Id, userInfo.UserInfo.Id)
		assert.NotNil(t, userInfo.AdsSetting)
	})
}

func TestLogic_RevokeGoogleAdsOAuth(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	mockGoogleRepo := mockGoogle.NewMockReadWriter(ctrl) // Not used in Revoke, but included for consistency
	mockTokenRepo := mockToken.NewMockReadWriter(ctrl)

	logic := &Logic{
		googleRepo: mockGoogleRepo,
		tokenRepo:  mockTokenRepo,
	}

	validCompanyID := int64(456)
	validStaffID := int64(789)
	sampleTokenID := int64(101)
	sampleDBToken := &moegoToken.OauthToken{
		ID:        sampleTokenID,
		CompanyID: validCompanyID,
		Type:      moegoToken.TokenTypeGoogleOauth2Ads,
		Token:     `{"access_token": "abc"}`, // Token content doesn't matter for revoke logic
	}

	t.Run("invalid params - zero companyID", func(t *testing.T) {
		err := logic.RevokeGoogleAdsOAuth(ctx, 0, validStaffID)
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("list tokens error", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return(nil, fmt.Errorf("db list error"))

		err := logic.RevokeGoogleAdsOAuth(ctx, validCompanyID, validStaffID)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "db list error")
	})

	t.Run("token not found", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{}, nil)

		err := logic.RevokeGoogleAdsOAuth(ctx, validCompanyID, validStaffID)
		assert.Error(t, err)
		assert.Equal(t, errs.New(openplatformpb.ErrCode_ERR_CODE_OAUTH_NOT_FOUND), err)
	})

	t.Run("delete token error", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{sampleDBToken}, nil)
		mockTokenRepo.EXPECT().Delete(ctx, sampleTokenID, validStaffID).Return(fmt.Errorf("db delete error"))

		err := logic.RevokeGoogleAdsOAuth(ctx, validCompanyID, validStaffID)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "db delete error")
	})

	t.Run("success", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{sampleDBToken}, nil)
		mockTokenRepo.EXPECT().Delete(ctx, sampleTokenID, validStaffID).Return(nil)

		err := logic.RevokeGoogleAdsOAuth(ctx, validCompanyID, validStaffID)
		assert.NoError(t, err)
	})
}

func TestLogic_ListGoogleAdsAccounts(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	mockGoogleRepo := mockGoogle.NewMockReadWriter(ctrl)
	mockTokenRepo := mockToken.NewMockReadWriter(ctrl)

	logic := &Logic{
		googleRepo: mockGoogleRepo,
		tokenRepo:  mockTokenRepo,
	}

	validCompanyID := int64(789)
	sampleToken := &oauth2.Token{
		AccessToken: "access_token_list_accounts",
		Expiry:      time.Now().Add(time.Hour),
	}
	sampleTokenStr, _ := sonic.MarshalString(sampleToken)
	sampleDBToken := &moegoToken.OauthToken{
		ID:        2,
		CompanyID: validCompanyID,
		Type:      moegoToken.TokenTypeGoogleOauth2Ads,
		Token:     sampleTokenStr,
	}

	sampleCustomerIDs := []string{"**********", "**********", "**********"}
	sampleCustomers := []*resources.Customer{
		{
			Id:              utils.ToPointer(int64(**********)),
			DescriptiveName: utils.ToPointer("Customer One"),
			ResourceName:    "customers/**********",
		},
		{
			Id:              utils.ToPointer(int64(**********)),
			DescriptiveName: utils.ToPointer("Customer Two"),
			ResourceName:    "customers/**********",
		},
		{
			Id:              utils.ToPointer(int64(**********)),
			DescriptiveName: utils.ToPointer("Customer Three"),
			ResourceName:    "customers/**********",
		},
	}

	// Helper to create a mock Search response
	createMockSearchResponse := func(customer *resources.Customer) *services.SearchGoogleAdsResponse {
		return &services.SearchGoogleAdsResponse{
			Results: []*services.GoogleAdsRow{
				{
					Customer: customer,
				},
			},
		}
	}

	t.Run("invalid params - zero companyID", func(t *testing.T) {
		accounts, err := logic.ListGoogleAdsAccounts(ctx, 0)
		assert.Error(t, err)
		assert.Nil(t, accounts)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("getCompanyGoogleAdsToken error - list error", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return(nil, fmt.Errorf("db list error"))

		accounts, err := logic.ListGoogleAdsAccounts(ctx, validCompanyID)
		assert.Error(t, err)
		assert.Nil(t, accounts)
		assert.Contains(t, err.Error(), "db list error")
	})

	t.Run("getCompanyGoogleAdsToken error - token not found", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{}, nil)

		accounts, err := logic.ListGoogleAdsAccounts(ctx, validCompanyID)
		assert.Error(t, err)
		assert.Nil(t, accounts)
		assert.Equal(t, errs.New(openplatformpb.ErrCode_ERR_CODE_OAUTH_NOT_FOUND), err)
	})

	t.Run("ListAccessibleCustomers error", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{sampleDBToken}, nil)
		mockGoogleRepo.EXPECT().ListAccessibleCustomers(ctx, gomock.Any()).Return(nil, fmt.Errorf("google list customers error"))

		accounts, err := logic.ListGoogleAdsAccounts(ctx, validCompanyID)
		assert.Error(t, err)
		assert.Nil(t, accounts)
		assert.Contains(t, err.Error(), "google list customers error")
	})

	t.Run("ListAccessibleCustomers returns empty", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{sampleDBToken}, nil)
		mockGoogleRepo.EXPECT().ListAccessibleCustomers(ctx, gomock.Any()).Return([]string{}, nil)

		accounts, err := logic.ListGoogleAdsAccounts(ctx, validCompanyID)
		assert.NoError(t, err)
		assert.Empty(t, accounts)
	})

	t.Run("Search error for one customer", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{sampleDBToken}, nil)
		mockGoogleRepo.EXPECT().ListAccessibleCustomers(ctx, gomock.Any()).Return(sampleCustomerIDs, nil)

		// Mock Search for each customer ID. Make one fail.
		mockGoogleRepo.EXPECT().Search(ctx, gomock.Any(), sampleCustomerIDs[0], CustomerInfoSearchQuery).Return(createMockSearchResponse(sampleCustomers[0]), nil)
		mockGoogleRepo.EXPECT().Search(ctx, gomock.Any(), sampleCustomerIDs[1], CustomerInfoSearchQuery).Return(nil, fmt.Errorf("google search error for customer 2")) // This one fails
		mockGoogleRepo.EXPECT().Search(ctx, gomock.Any(), sampleCustomerIDs[2], CustomerInfoSearchQuery).Return(createMockSearchResponse(sampleCustomers[2]), nil)

		accounts, err := logic.ListGoogleAdsAccounts(ctx, validCompanyID)
		assert.Error(t, err)
		assert.Nil(t, accounts) // The function returns nil on any search error
		assert.Contains(t, err.Error(), "google search error for customer 2")
	})
}

func TestLogic_GetLinkSetting(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	mockGoogleRepo := mockGoogle.NewMockReadWriter(ctrl)
	// mockTokenRepo and mockAdsSettingRepo are not used in this function, but included for consistency
	mockTokenRepo := mockToken.NewMockReadWriter(ctrl)
	mockAdsSettingRepo := mockSetting.NewMockReadWriter(ctrl)

	logic := &Logic{
		googleRepo:     mockGoogleRepo,
		tokenRepo:      mockTokenRepo,
		adsSettingRepo: mockAdsSettingRepo,
	}

	t.Run("invalid params - unknown type", func(t *testing.T) {
		link, err := logic.GetLinkSetting(ctx, openplatformpb.GetLinkSettingRequest_LINK_TYPE_UNSPECIFIED)
		assert.Error(t, err)
		assert.Empty(t, link)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("success - google ads oauth2", func(t *testing.T) {
		expectedLink := "https://accounts.google.com/o/oauth2/auth?..."
		mockGoogleRepo.EXPECT().GetGoogleAdsOAuth2Link(ctx).Return(expectedLink, nil)

		link, err := logic.GetLinkSetting(ctx, openplatformpb.GetLinkSettingRequest_LINK_TYPE_GOOGLE_ADS_OAUTH2)
		assert.NoError(t, err)
		assert.Equal(t, expectedLink, link)
	})

	t.Run("google repo error - google ads oauth2", func(t *testing.T) {
		mockGoogleRepo.EXPECT().GetGoogleAdsOAuth2Link(ctx).Return("", fmt.Errorf("google link error"))

		link, err := logic.GetLinkSetting(ctx, openplatformpb.GetLinkSettingRequest_LINK_TYPE_GOOGLE_ADS_OAUTH2)
		assert.Error(t, err)
		assert.Empty(t, link)
		assert.Contains(t, err.Error(), "google link error")
	})
}

func TestLogic_LinkGoogleAdsAccounts(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	// mockGoogleRepo and mockTokenRepo are not used directly in LinkGoogleAdsAccounts,
	// but included for consistency with the Logic struct definition.
	mockGoogleRepo := mockGoogle.NewMockReadWriter(ctrl)
	mockTokenRepo := mockToken.NewMockReadWriter(ctrl)
	mockAdsSettingRepo := mockSetting.NewMockReadWriter(ctrl)

	logic := &Logic{
		googleRepo:     mockGoogleRepo,
		tokenRepo:      mockTokenRepo,
		adsSettingRepo: mockAdsSettingRepo,
	}

	validDatum := &LinkGoogleAdsAccountsDatum{
		CompanyID:           1,
		BusinessID:          2,
		StaffID:             3,
		GoogleAdsAccountIDs: []int64{111, 222},
	}

	existingSetting := &googleadssetting.GoogleAdsSetting{
		ID:             99,
		CompanyID:      validDatum.CompanyID,
		BusinessID:     validDatum.BusinessID,
		AdsCustomerIDs: []int64{333}, // Existing IDs
	}

	t.Run("invalid params - nil datum", func(t *testing.T) {
		err := logic.LinkGoogleAdsAccounts(ctx, nil)
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("invalid params - zero companyID", func(t *testing.T) {
		datum := *validDatum
		datum.CompanyID = 0
		err := logic.LinkGoogleAdsAccounts(ctx, &datum)
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("invalid params - zero businessID", func(t *testing.T) {
		datum := *validDatum
		datum.BusinessID = 0
		err := logic.LinkGoogleAdsAccounts(ctx, &datum)
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("list settings error", func(t *testing.T) {
		mockAdsSettingRepo.EXPECT().List(ctx, gomock.Any()).Return(nil, fmt.Errorf("db list error"))

		err := logic.LinkGoogleAdsAccounts(ctx, validDatum)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "db list error")
	})

	t.Run("create setting success - no existing setting", func(t *testing.T) {
		mockAdsSettingRepo.EXPECT().List(ctx, gomock.Any()).Return([]*googleadssetting.GoogleAdsSetting{}, nil) // No existing setting
		mockAdsSettingRepo.EXPECT().Create(ctx, gomock.Any()).DoAndReturn(func(_ context.Context, setting *googleadssetting.GoogleAdsSetting) error {
			assert.Equal(t, validDatum.CompanyID, setting.CompanyID)
			assert.Equal(t, validDatum.BusinessID, setting.BusinessID)
			assert.Equal(t, pq.Int64Array(validDatum.GoogleAdsAccountIDs), setting.AdsCustomerIDs)
			assert.Equal(t, validDatum.StaffID, setting.CreatedBy)
			assert.Equal(t, validDatum.StaffID, setting.UpdatedBy)
			// Check CreatedAt and UpdatedAt are recent, but not exact time
			assert.WithinDuration(t, time.Now(), setting.CreatedAt, time.Second)
			assert.WithinDuration(t, time.Now(), setting.UpdatedAt, time.Second)
			return nil
		})

		err := logic.LinkGoogleAdsAccounts(ctx, validDatum)
		assert.NoError(t, err)
	})

	t.Run("create setting error - no existing setting", func(t *testing.T) {
		mockAdsSettingRepo.EXPECT().List(ctx, gomock.Any()).Return([]*googleadssetting.GoogleAdsSetting{}, nil) // No existing setting
		mockAdsSettingRepo.EXPECT().Create(ctx, gomock.Any()).Return(fmt.Errorf("db create error"))

		err := logic.LinkGoogleAdsAccounts(ctx, validDatum)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "db create error")
	})

	t.Run("update setting error - existing setting", func(t *testing.T) {
		mockAdsSettingRepo.EXPECT().List(ctx, gomock.Any()).Return([]*googleadssetting.GoogleAdsSetting{existingSetting}, nil) // Existing setting
		mockAdsSettingRepo.EXPECT().Update(ctx, gomock.Any()).Return(fmt.Errorf("db update error"))

		err := logic.LinkGoogleAdsAccounts(ctx, validDatum)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "db update error")
	})
}

func TestLogic_CreateMetaCompanyToken(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	mockMetaRepo := mockMeta.NewMockReadWriter(ctrl)
	mockTokenRepo := mockToken.NewMockReadWriter(ctrl)

	logic := &Logic{
		metaRepo:  mockMetaRepo,
		tokenRepo: mockTokenRepo,
	}

	validDatum := &CreateCompanyTokenDatum{
		Code:       "valid_code",
		CompanyID:  1,
		BusinessID: 2,
		StaffID:    3,
	}

	metaToken := &oauth2.Token{
		AccessToken:  "access_token_meta",
		RefreshToken: "refresh_token_meta",
		TokenType:    "Bearer",
		Expiry:       time.Now().Add(time.Hour),
	}

	t.Run("invalid params - nil datum", func(t *testing.T) {
		err := logic.CreateMetaCompanyToken(ctx, nil)
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("invalid params - empty code", func(t *testing.T) {
		datum := *validDatum
		datum.Code = ""
		err := logic.CreateMetaCompanyToken(ctx, &datum)
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("invalid params - zero companyID", func(t *testing.T) {
		datum := *validDatum
		datum.CompanyID = 0
		err := logic.CreateMetaCompanyToken(ctx, &datum)
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("exchange code error", func(t *testing.T) {
		mockMetaRepo.EXPECT().ExchangeCode(ctx, validDatum.Code).Return(nil, fmt.Errorf("meta exchange error"))

		err := logic.CreateMetaCompanyToken(ctx, validDatum)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "meta exchange error")
	})

	t.Run("create token error", func(t *testing.T) {
		mockMetaRepo.EXPECT().ExchangeCode(ctx, validDatum.Code).Return(metaToken, nil)
		mockTokenRepo.EXPECT().Create(ctx, gomock.Any()).Return(fmt.Errorf("db create error"))

		err := logic.CreateMetaCompanyToken(ctx, validDatum)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "db create error")
	})

	t.Run("success", func(t *testing.T) {
		mockMetaRepo.EXPECT().ExchangeCode(ctx, validDatum.Code).Return(metaToken, nil)
		mockTokenRepo.EXPECT().Create(ctx, gomock.Any()).DoAndReturn(func(_ context.Context, token *moegoToken.OauthToken) error {
			assert.Equal(t, validDatum.CompanyID, token.CompanyID)
			assert.Equal(t, validDatum.BusinessID, token.BusinessID)
			assert.Equal(t, validDatum.StaffID, token.CreatedBy)
			assert.Equal(t, validDatum.StaffID, token.UpdatedBy)
			assert.Equal(t, moegoToken.TokenTypeMetaOauth2Ads, token.Type)
			return nil
		})

		err := logic.CreateMetaCompanyToken(ctx, validDatum)
		assert.NoError(t, err)
	})
}

func TestLogic_GetMetaAdsUserInfo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	mockMetaRepo := mockMeta.NewMockReadWriter(ctrl)
	mockTokenRepo := mockToken.NewMockReadWriter(ctrl)

	logic := &Logic{
		metaRepo:  mockMetaRepo,
		tokenRepo: mockTokenRepo,
	}

	validCompanyID := int64(123)
	sampleToken := &oauth2.Token{
		AccessToken: "access_token_meta",
		Expiry:      time.Now().Add(time.Hour),
	}
	sampleTokenStr, _ := sonic.MarshalString(sampleToken)
	sampleDBToken := &moegoToken.OauthToken{
		ID:        1,
		CompanyID: validCompanyID,
		Type:      moegoToken.TokenTypeMetaOauth2Ads,
		Token:     sampleTokenStr,
	}

	sampleUserInfo := &meta.UserInfo{
		Email: "<EMAIL>",
		Name:  "Meta User",
		ID:    "meta_123456",
	}

	t.Run("invalid params - zero companyID", func(t *testing.T) {
		userInfo, err := logic.GetMetaAdsUserInfo(ctx, 0)
		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("getCompanyMetaAdsToken error - list error", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return(nil, fmt.Errorf("db list error"))

		userInfo, err := logic.GetMetaAdsUserInfo(ctx, validCompanyID)
		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "db list error")
	})

	t.Run("getCompanyMetaAdsToken error - token not found", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{}, nil)

		userInfo, err := logic.GetMetaAdsUserInfo(ctx, validCompanyID)
		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Equal(t, errs.New(openplatformpb.ErrCode_ERR_CODE_OAUTH_NOT_FOUND), err)
	})

	t.Run("GetUserInfo error", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{sampleDBToken}, nil)
		mockMetaRepo.EXPECT().GetUserInfo(ctx, gomock.Any()).Return(nil, fmt.Errorf("meta user info error"))

		userInfo, err := logic.GetMetaAdsUserInfo(ctx, validCompanyID)
		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "meta user info error")
	})

	t.Run("success", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{sampleDBToken}, nil)
		mockMetaRepo.EXPECT().GetUserInfo(ctx, gomock.Any()).Return(sampleUserInfo, nil)

		userInfo, err := logic.GetMetaAdsUserInfo(ctx, validCompanyID)
		assert.NoError(t, err)
		assert.NotNil(t, userInfo)
		assert.Equal(t, sampleUserInfo.Email, userInfo.Email)
		assert.Equal(t, sampleUserInfo.Name, userInfo.Name)
		assert.Equal(t, sampleUserInfo.ID, userInfo.Id)
	})
}

func TestLogic_RevokeMetaAdsOAuth(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	mockMetaRepo := mockMeta.NewMockReadWriter(ctrl) // Not used in Revoke, but included for consistency
	mockTokenRepo := mockToken.NewMockReadWriter(ctrl)

	logic := &Logic{
		metaRepo:  mockMetaRepo,
		tokenRepo: mockTokenRepo,
	}

	validCompanyID := int64(456)
	validStaffID := int64(789)
	sampleTokenID := int64(101)
	sampleDBToken := &moegoToken.OauthToken{
		ID:        sampleTokenID,
		CompanyID: validCompanyID,
		Type:      moegoToken.TokenTypeMetaOauth2Ads,
		Token:     `{"access_token": "abc"}`,
	}

	t.Run("invalid params - zero companyID", func(t *testing.T) {
		err := logic.RevokeMetaAdsOAuth(ctx, 0, validStaffID)
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("list tokens error", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return(nil, fmt.Errorf("db list error"))

		err := logic.RevokeMetaAdsOAuth(ctx, validCompanyID, validStaffID)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "db list error")
	})

	t.Run("token not found", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{}, nil)

		err := logic.RevokeMetaAdsOAuth(ctx, validCompanyID, validStaffID)
		assert.Error(t, err)
		assert.Equal(t, errs.New(openplatformpb.ErrCode_ERR_CODE_OAUTH_NOT_FOUND), err)
	})

	t.Run("delete token error", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{sampleDBToken}, nil)
		mockTokenRepo.EXPECT().Delete(ctx, sampleTokenID, validStaffID).Return(fmt.Errorf("db delete error"))

		err := logic.RevokeMetaAdsOAuth(ctx, validCompanyID, validStaffID)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "db delete error")
	})

	t.Run("success", func(t *testing.T) {
		mockTokenRepo.EXPECT().List(ctx, gomock.Any()).Return([]*moegoToken.OauthToken{sampleDBToken}, nil)
		mockTokenRepo.EXPECT().Delete(ctx, sampleTokenID, validStaffID).Return(nil)

		err := logic.RevokeMetaAdsOAuth(ctx, validCompanyID, validStaffID)
		assert.NoError(t, err)
	})
}
