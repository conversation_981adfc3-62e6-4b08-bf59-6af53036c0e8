package oauth

import (
	"context"
	"sync"
	"time"

	"github.com/bytedance/sonic"
	"github.com/shenzhencenter/google-ads-pb/resources"
	"golang.org/x/oauth2"
	"golang.org/x/sync/errgroup"
	oauth2api "google.golang.org/api/oauth2/v2"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/app/customer/utils"
	googleadssetting "github.com/MoeGolibrary/moego/backend/app/open_platform/repo/db/google_ads_setting"
	moegoToken "github.com/MoeGolibrary/moego/backend/app/open_platform/repo/db/token"
	"github.com/MoeGolibrary/moego/backend/app/open_platform/repo/google"
	"github.com/MoeGolibrary/moego/backend/app/open_platform/repo/meta"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	openplatformpb "github.com/MoeGolibrary/moego/backend/proto/open_platform/v1"
)

type Logic struct {
	googleRepo     google.ReadWriter
	metaRepo       meta.ReadWriter
	tokenRepo      moegoToken.ReadWriter
	adsSettingRepo googleadssetting.ReadWriter
}

func New() *Logic {
	return &Logic{
		googleRepo:     google.New(),
		metaRepo:       meta.New(),
		tokenRepo:      moegoToken.New(),
		adsSettingRepo: googleadssetting.New(),
	}
}

func (l *Logic) CreateCompanyToken(ctx context.Context, datum *CreateCompanyTokenDatum) error {
	if datum == nil || datum.Code == "" || datum.CompanyID == 0 || datum.BusinessID == 0 || datum.StaffID == 0 {
		log.InfoContextf(ctx, "CreateCompanyToken params is invalid, datum:%+v", datum)
		return status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// exchange code for access and refresh tokens
	token, err := l.googleRepo.ExchangeCode(ctx, datum.Code)
	if err != nil {
		return err
	}

	// save token
	tokenStr, err := sonic.MarshalString(token)
	if err != nil {
		log.ErrorContextf(ctx, "CreateCompanyToken MarshalString token err, err:%v", err)
		return err
	}
	if err := l.tokenRepo.Create(ctx, &moegoToken.OauthToken{
		CompanyID:  datum.CompanyID,
		BusinessID: datum.BusinessID,
		Type:       moegoToken.TokenTypeGoogleOauth2Ads,
		Token:      tokenStr,
		CreatedBy:  datum.StaffID,
		UpdatedBy:  datum.StaffID,
	}); err != nil {
		log.ErrorContextf(ctx, "CreateCompanyToken Create token err, err:%v", err)
		return err
	}

	log.InfoContextf(ctx, "CreateCompanyToken success, companyID:%d, token:%s", datum.CompanyID, tokenStr)
	return nil
}

func (l *Logic) CreateMetaCompanyToken(ctx context.Context, datum *CreateCompanyTokenDatum) error {
	if datum == nil || datum.Code == "" || datum.CompanyID == 0 || datum.BusinessID == 0 || datum.StaffID == 0 {
		log.InfoContextf(ctx, "CreateMetaCompanyToken params is invalid, datum:%+v", datum)
		return status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// exchange code for access and refresh tokens
	token, err := l.metaRepo.ExchangeCode(ctx, datum.Code)
	if err != nil {
		return err
	}

	// save token
	tokenStr, err := sonic.MarshalString(token)
	if err != nil {
		log.ErrorContextf(ctx, "CreateMetaCompanyToken MarshalString token err, err:%v", err)
		return err
	}
	if err := l.tokenRepo.Create(ctx, &moegoToken.OauthToken{
		CompanyID:  datum.CompanyID,
		BusinessID: datum.BusinessID,
		Type:       moegoToken.TokenTypeMetaOauth2Ads,
		Token:      tokenStr,
		CreatedBy:  datum.StaffID,
		UpdatedBy:  datum.StaffID,
	}); err != nil {
		log.ErrorContextf(ctx, "CreateMetaCompanyToken Create token err, err:%v", err)
		return err
	}

	log.InfoContextf(ctx, "CreateMetaCompanyToken success, companyID:%d, token:%s", datum.CompanyID, tokenStr)
	return nil
}

func (l *Logic) GetGoogleAdsUserInfo(ctx context.Context, companyID int64) (
	*GoogleUserInfoDatum, error) {
	// check
	if companyID <= 0 {
		log.InfoContextf(ctx, "GetGoogleAdsUserInfo params is invalid, companyID:%d", companyID)
		return nil, status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// get company token
	token, err := l.getCompanyGoogleAdsToken(ctx, companyID)
	if err != nil {
		return nil, err
	}

	// get token user info
	userInfo, err := l.googleRepo.GetUserInfo(ctx, token)
	if err != nil {
		log.ErrorContextf(ctx, "GetGoogleAdsUserInfo GetUserInfo err, err:%v, companyID:%d", err, companyID)
		return nil, err
	}

	// get google ads setting
	settings, err := l.adsSettingRepo.List(ctx, &googleadssetting.ListDatum{CompanyID: &companyID})
	if err != nil {
		return nil, err
	}
	var setting *googleadssetting.GoogleAdsSetting
	if len(settings) > 0 {
		setting = settings[0]
	}

	return convGoogleUserInfoDatum(userInfo, setting), nil
}

func (l *Logic) getCompanyGoogleAdsToken(ctx context.Context, companyID int64) (*oauth2.Token, error) {
	// get newest token
	tokens, err := l.tokenRepo.List(ctx, &moegoToken.ListDatum{
		CompanyID: &companyID,
		Type:      utils.ToPointer(moegoToken.TokenTypeGoogleOauth2Ads),
	})
	if err != nil {
		return nil, err
	}
	if len(tokens) == 0 {
		log.InfoContextf(ctx, "getCompanyGoogleAdsToken list tokens is empty, companyID:%d", companyID)
		return nil, errs.New(openplatformpb.ErrCode_ERR_CODE_OAUTH_NOT_FOUND)
	}

	// parse token
	var token *oauth2.Token
	if err := sonic.UnmarshalString(tokens[0].Token, &token); err != nil {
		log.ErrorContextf(ctx, "getCompanyGoogleAdsToken UnmarshalString token err, err:%v, token:%s",
			err, tokens[0].Token)
		return nil, err
	}
	return token, nil
}

func convGoogleUserInfoDatum(userInfo *oauth2api.Userinfo,
	setting *googleadssetting.GoogleAdsSetting) *GoogleUserInfoDatum {
	return &GoogleUserInfoDatum{
		UserInfo:   convGoogleUserInfo(userInfo),
		AdsSetting: convGoogleAdsSetting(setting),
	}
}

func convGoogleAdsSetting(setting *googleadssetting.GoogleAdsSetting) *openplatformpb.GoogleAdsSetting {
	if setting == nil {
		return nil
	}
	return &openplatformpb.GoogleAdsSetting{
		LinkGoogleAdsCustomerIds: setting.AdsCustomerIDs,
	}
}

func convGoogleUserInfo(userInfo *oauth2api.Userinfo) *openplatformpb.GoogleOAuthUserInfo {
	if userInfo == nil {
		return nil
	}
	return &openplatformpb.GoogleOAuthUserInfo{
		Email:         userInfo.Email,
		FamilyName:    userInfo.FamilyName,
		Gender:        userInfo.Gender,
		GivenName:     userInfo.GivenName,
		Hd:            userInfo.Hd,
		Id:            userInfo.Id,
		Link:          userInfo.Link,
		Locale:        userInfo.Locale,
		Name:          userInfo.Name,
		Picture:       userInfo.Picture,
		VerifiedEmail: userInfo.VerifiedEmail,
	}
}

func (l *Logic) RevokeGoogleAdsOAuth(ctx context.Context, companyID, staffID int64) error {
	// check
	if companyID <= 0 {
		log.InfoContextf(ctx, "RevokeGoogleAdsOAuth params is invalid, companyID:%d", companyID)
		return status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// get newest token
	tokens, err := l.tokenRepo.List(ctx, &moegoToken.ListDatum{
		CompanyID: &companyID,
		Type:      utils.ToPointer(moegoToken.TokenTypeGoogleOauth2Ads),
	})
	if err != nil {
		return err
	}
	if len(tokens) == 0 {
		log.InfoContextf(ctx, "RevokeGoogleAdsOAuth list tokens is empty, companyID:%d", companyID)
		return errs.New(openplatformpb.ErrCode_ERR_CODE_OAUTH_NOT_FOUND)
	}

	// delete token TODO:google侧也可以解绑
	return l.tokenRepo.Delete(ctx, tokens[0].ID, staffID)
}

func (l *Logic) ListGoogleAdsAccounts(ctx context.Context, companyID int64) (
	[]*openplatformpb.GoogleAdsCustomer, error) {
	// check
	if companyID <= 0 {
		log.InfoContextf(ctx, "ListGoogleAdsAccounts params is invalid, companyID:%d", companyID)
		return nil, status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// get company token
	token, err := l.getCompanyGoogleAdsToken(ctx, companyID)
	if err != nil {
		return nil, err
	}

	// list customerIDs
	customerIDs, err := l.googleRepo.ListAccessibleCustomers(ctx, token)
	if err != nil {
		return nil, err
	}
	if len(customerIDs) == 0 {
		return nil, nil
	}
	log.InfoContextf(ctx, "ListGoogleAdsAccounts ListAccessibleCustomers success, companyID:%d, customerIDs:%v",
		companyID, customerIDs)

	// get customer info
	var (
		customers = make([]*resources.Customer, 0, len(customerIDs))
		g         errgroup.Group
		mu        sync.Mutex
	)
	for _, customerID := range customerIDs {
		id := customerID
		g.Go(func() error {
			// get info
			searchResp, err := l.googleRepo.Search(ctx, token, id, CustomerInfoSearchQuery)
			if err != nil {
				log.ErrorContextf(ctx, "ListGoogleAdsAccounts Search err, err:%v, customerID:%s", err, id)
				return err
			}

			// add map
			mu.Lock()
			defer mu.Unlock()
			for _, result := range searchResp.GetResults() {
				if result == nil || result.GetCustomer() == nil {
					continue
				}
				customers = append(customers, result.GetCustomer())
			}
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		log.ErrorContextf(ctx, "ListGoogleAdsAccounts Search err, err:%v", err)
		return nil, err
	}

	// convGoogleAdsAccountsPB
	return convGoogleAdsCustomersPB(customers), nil
}

func (l *Logic) GetLinkSetting(ctx context.Context,
	getType openplatformpb.GetLinkSettingRequest_LinkType) (string, error) {
	switch getType {
	case openplatformpb.GetLinkSettingRequest_LINK_TYPE_GOOGLE_ADS_OAUTH2:
		return l.googleRepo.GetGoogleAdsOAuth2Link(ctx)
	case openplatformpb.GetLinkSettingRequest_LINK_TYPE_META_ADS_OAUTH2:
		return l.metaRepo.GetMetaAdsOAuth2Link(ctx)
	default:
		log.InfoContextf(ctx, "GetLinkSetting params is invalid, type:%d", getType)
		return "", status.Errorf(codes.InvalidArgument, "params is invalid")
	}
}

func (l *Logic) LinkGoogleAdsAccounts(ctx context.Context, datum *LinkGoogleAdsAccountsDatum) error {
	// check
	if datum == nil || datum.CompanyID <= 0 || datum.BusinessID <= 0 {
		log.InfoContextf(ctx, "LinkGoogleAdsAccounts params is invalid, datum:%+v", datum)
		return status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// conv ids
	if len(datum.GoogleAdsAccountIDs) == 0 {
		datum.GoogleAdsAccountIDs = make([]int64, 0)
	}

	// get company ads setting
	settings, err := l.adsSettingRepo.List(ctx, &googleadssetting.ListDatum{CompanyID: &datum.CompanyID})
	if err != nil {
		return err
	}

	// create or update
	if len(settings) == 0 {
		return l.adsSettingRepo.Create(ctx, &googleadssetting.GoogleAdsSetting{
			CompanyID:      datum.CompanyID,
			BusinessID:     datum.BusinessID,
			AdsCustomerIDs: datum.GoogleAdsAccountIDs,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
			CreatedBy:      datum.StaffID,
			UpdatedBy:      datum.StaffID,
		})
	}
	return l.adsSettingRepo.Update(ctx, &googleadssetting.GoogleAdsSetting{
		ID:             settings[0].ID,
		AdsCustomerIDs: datum.GoogleAdsAccountIDs,
	})
}

func (l *Logic) GetMetaAdsUserInfo(ctx context.Context, companyID int64) (*openplatformpb.MetaOAuthUserInfo, error) {
	// check
	if companyID <= 0 {
		log.InfoContextf(ctx, "GetMetaAdsUserInfo params is invalid, companyID:%d", companyID)
		return nil, status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// get company token
	token, err := l.getCompanyMetaAdsToken(ctx, companyID)
	if err != nil {
		return nil, err
	}

	// get token user info
	userInfo, err := l.metaRepo.GetUserInfo(ctx, token)
	if err != nil {
		log.ErrorContextf(ctx, "GetMetaAdsUserInfo GetUserInfo err, err:%v, companyID:%d", err, companyID)
		return nil, err
	}

	return &openplatformpb.MetaOAuthUserInfo{
		Email: userInfo.Email,
		Name:  userInfo.Name,
		Id:    userInfo.ID,
	}, nil
}

func (l *Logic) getCompanyMetaAdsToken(ctx context.Context, companyID int64) (*oauth2.Token, error) {
	// get newest token
	tokens, err := l.tokenRepo.List(ctx, &moegoToken.ListDatum{
		CompanyID: &companyID,
		Type:      utils.ToPointer(moegoToken.TokenTypeMetaOauth2Ads),
	})
	if err != nil {
		return nil, err
	}
	if len(tokens) == 0 {
		log.InfoContextf(ctx, "getCompanyMetaAdsToken list tokens is empty, companyID:%d", companyID)
		return nil, errs.New(openplatformpb.ErrCode_ERR_CODE_OAUTH_NOT_FOUND)
	}

	// parse token
	var token *oauth2.Token
	if err := sonic.UnmarshalString(tokens[0].Token, &token); err != nil {
		log.ErrorContextf(ctx, "getCompanyMetaAdsToken UnmarshalString token err, err:%v, token:%s",
			err, tokens[0].Token)
		return nil, err
	}
	return token, nil
}

func (l *Logic) RevokeMetaAdsOAuth(ctx context.Context, companyID int64, staffID int64) error {
	// check
	if companyID <= 0 {
		log.InfoContextf(ctx, "RevokeMetaAdsOAuth params is invalid, companyID:%d", companyID)
		return status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// get newest token
	tokens, err := l.tokenRepo.List(ctx, &moegoToken.ListDatum{
		CompanyID: &companyID,
		Type:      utils.ToPointer(moegoToken.TokenTypeMetaOauth2Ads),
	})
	if err != nil {
		return err
	}
	if len(tokens) == 0 {
		log.InfoContextf(ctx, "RevokeMetaAdsOAuth list tokens is empty, companyID:%d", companyID)
		return errs.New(openplatformpb.ErrCode_ERR_CODE_OAUTH_NOT_FOUND)
	}

	// delete token TODO:meta侧也可以解绑
	return l.tokenRepo.Delete(ctx, tokens[0].ID, staffID)
}

func convGoogleAdsCustomersPB(customers []*resources.Customer) []*openplatformpb.GoogleAdsCustomer {
	res := make([]*openplatformpb.GoogleAdsCustomer, 0, len(customers))
	for _, customer := range customers {
		if customer == nil {
			continue
		}
		res = append(res, convGoogleAdsCustomerPB(customer))
	}
	return res
}

func convGoogleAdsCustomerPB(customer *resources.Customer) *openplatformpb.GoogleAdsCustomer {
	return &openplatformpb.GoogleAdsCustomer{
		ResourceName:        customer.ResourceName,
		Id:                  customer.Id,
		DescriptiveName:     customer.DescriptiveName,
		CurrencyCode:        customer.CurrencyCode,
		TimeZone:            customer.TimeZone,
		TrackingUrlTemplate: customer.TrackingUrlTemplate,
		FinalUrlSuffix:      customer.FinalUrlSuffix,
		AutoTaggingEnabled:  customer.AutoTaggingEnabled,
		HasPartnersBadge:    customer.HasPartnersBadge,
		Manager:             customer.Manager,
		TestAccount:         customer.TestAccount,
		OptimizationScore:   customer.OptimizationScore,
	}
}
