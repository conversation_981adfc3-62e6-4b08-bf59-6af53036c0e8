load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "oauth",
    srcs = [
        "constdef.go",
        "entity.go",
        "oauth_logic.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/open_platform/logic/oauth",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/utils",
        "//backend/app/open_platform/repo/db/google_ads_setting",
        "//backend/app/open_platform/repo/db/token",
        "//backend/app/open_platform/repo/google",
        "//backend/app/open_platform/repo/meta",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/proto/open_platform/v1:open_platform",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_shenzhencenter_google_ads_pb//resources",
        "@org_golang_google_api//oauth2/v2:oauth2",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_x_oauth2//:oauth2",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "oauth_test",
    srcs = ["oauth_logic_test.go"],
    embed = [":oauth"],
    deps = [
        "//backend/app/customer/utils",
        "//backend/app/open_platform/repo/db",
        "//backend/app/open_platform/repo/db/google_ads_setting",
        "//backend/app/open_platform/repo/db/google_ads_setting/mock",
        "//backend/app/open_platform/repo/db/token",
        "//backend/app/open_platform/repo/db/token/mock",
        "//backend/app/open_platform/repo/google/mock",
        "//backend/app/open_platform/repo/meta",
        "//backend/app/open_platform/repo/meta/mock",
        "//backend/common/rpc/framework/errs",
        "//backend/proto/open_platform/v1:open_platform",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_lib_pq//:pq",
        "@com_github_shenzhencenter_google_ads_pb//resources",
        "@com_github_shenzhencenter_google_ads_pb//services",
        "@com_github_stretchr_testify//assert",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_api//oauth2/v2:oauth2",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_x_oauth2//:oauth2",
        "@org_uber_go_mock//gomock",
    ],
)
