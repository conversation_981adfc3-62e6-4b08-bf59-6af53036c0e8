package meta

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"

	"github.com/bytedance/sonic"
	"golang.org/x/oauth2"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	openplatformpb "github.com/MoeGolibrary/moego/backend/proto/open_platform/v1"
)

type ReadWriter interface {
	ExchangeCode(ctx context.Context, code string) (*oauth2.Token, error)
	GetMetaAdsOAuth2Link(ctx context.Context) (string, error)
	GetUserInfo(ctx context.Context, token *oauth2.Token) (*UserInfo, error)
}

type UserInfo struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
}

type impl struct {
	oauth2Config *oauth2.Config
}

var (
	metaAuthURL = "https://www.facebook.com/v23.0/dialog/oauth"

	prodRedirectURL = "https://go.moego.pet/open/platform/meta/oauth/callback"
	testRedirectURL = "https://go.t2.moego.dev/open/platform/meta/oauth/callback"
)

func New() ReadWriter {
	return &impl{
		oauth2Config: &oauth2.Config{
			ClientID:     "1303149471143080",
			ClientSecret: "26ed6dab565e15953d80653398a5acb1",
			RedirectURL:  getRedirectURL(),
			Scopes: []string{
				"public_profile",
				"email",
				"ads_read",
			},
			Endpoint: oauth2.Endpoint{
				AuthURL:  "https://www.facebook.com/v23.0/dialog/oauth",
				TokenURL: "https://graph.facebook.com/v23.0/oauth/access_token",
			},
		},
	}
}

func getRedirectURL() string {
	if os.Getenv("MOEGO_ENVIRONMENT") == "production" {
		return prodRedirectURL
	}
	return testRedirectURL
}

func (i *impl) ExchangeCode(ctx context.Context, code string) (*oauth2.Token, error) {
	token, err := i.oauth2Config.Exchange(ctx, code)
	if err != nil {
		log.ErrorContextf(ctx, "ExchangeCode err, err:%v", err)
		return nil, err
	}
	return token, nil
}

func (i *impl) GetMetaAdsOAuth2Link(ctx context.Context) (string, error) {
	// Generate a secure state parameter (e.g., 32 bytes -> ~44 char base64)
	state, err := generateSecureRandomString(32)
	if err != nil {
		log.ErrorContext(ctx, "GetMetaAdsOAuth2Link generateSecureRandomString err, err:%v", err)
		return "", err
	}

	// Build the URL using net/url package
	u, err := url.Parse(metaAuthURL)
	if err != nil {
		log.ErrorContextf(ctx, "GetMetaAdsOAuth2Link Parse googleAuthURL err, err:%v", err)
		return "", err
	}

	// Add query parameters
	q := u.Query()
	q.Set("client_id", i.oauth2Config.ClientID)
	q.Set("redirect_uri", i.oauth2Config.RedirectURL)
	q.Set("scope", strings.Join(i.oauth2Config.Scopes, " ")) // url.Values.Set automatically encodes the value
	q.Set("state", state)
	u.RawQuery = q.Encode()

	return u.String(), nil
}

func (i *impl) GetUserInfo(ctx context.Context, token *oauth2.Token) (*UserInfo, error) {
	// 构造请求URL
	url := fmt.Sprintf("https://graph.facebook.com/v23.0/me?fields=id,name,email&access_token=%s", token.AccessToken)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		log.ErrorContextf(ctx, "GetUserInfo NewRequest error: %v", err)
		return nil, err
	}

	// 发起请求
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, convHTTPErr(ctx, err)
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.ErrorContextf(ctx, "GetUserInfo read body error: %v", err)
		return nil, err
	}

	var user *UserInfo
	if err := sonic.Unmarshal(bodyBytes, &user); err != nil {
		log.ErrorContextf(ctx, "GetUserInfo decode error: %v", err)
		return nil, err
	}

	return user, nil
}

var (
	unauthorizedHTTPCodeMap = map[int]bool{
		http.StatusUnauthorized: true,
		http.StatusForbidden:    true,
	}
)

func convHTTPErr(ctx context.Context, err error) error {
	// 1. 检查错误是否是 *oauth2.RetrieveError 类型
	// 这是判断令牌失效（invalid_grant）最常见且准确的方式
	var retrieveErr *oauth2.RetrieveError
	if errors.As(err, &retrieveErr) {
		log.ErrorContextf(ctx, "convHTTPErr retrieveErr err, err:%v", retrieveErr)
		if retrieveErr.Response != nil &&
			unauthorizedHTTPCodeMap[retrieveErr.Response.StatusCode] {
			log.ErrorContextf(ctx, "convHTTPErr token is expired or revoked.")
			return errs.New(openplatformpb.ErrCode_ERR_CODE_OAUTH_EXPIRED)
		}
	}

	// 其他错误
	log.ErrorContextf(ctx, "convGoogleHTTPErr common err, err:%v", err)
	return err
}

// generateSecureRandomString generates a cryptographically secure random string.
// It's suitable for use as an OAuth state parameter to prevent CSRF attacks.
// We generate random bytes and encode them using URL-safe base64.
// A 32-byte random string is common, resulting in a ~44 character base64 string.
func generateSecureRandomString(byteLength int) (string, error) {
	b := make([]byte, byteLength)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	// Use URLEncoding to ensure the string is safe for URL parameters
	return base64.URLEncoding.EncodeToString(b), nil
}
