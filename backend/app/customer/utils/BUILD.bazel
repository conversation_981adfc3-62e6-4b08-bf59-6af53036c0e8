load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "utils",
    srcs = [
        "conv.go",
        "cursor.go",
        "json.go",
        "ptr.go",
        "utils.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/utils",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "@com_github_bytedance_sonic//:sonic",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
    ],
)
