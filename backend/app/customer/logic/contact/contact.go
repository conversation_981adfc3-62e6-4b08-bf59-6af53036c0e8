package contact

import (
	"context"
	"errors"
	"time"

	"google.golang.org/grpc/codes"
	"gorm.io/gorm"

	contacttag "github.com/MoeGolibrary/moego/backend/app/customer/logic/contact_tag"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	contactrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact"
	contacttagrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag"
	contacttagrel "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag_rel"
	"github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Logic struct {
	contactRepo       contactrepo.Repository
	contactTagRepo    contacttagrepo.Repository
	contactTagRelRepo contacttagrel.Repository
}

func New() *Logic {
	return &Logic{
		contactRepo:       contactrepo.New(),
		contactTagRepo:    contacttagrepo.New(),
		contactTagRelRepo: contacttagrel.New(),
	}
}

func NewByParams(
	contactRepo contactrepo.Repository,
	contactTagRepo contacttagrepo.Repository,
	contactTagRelRepo contacttagrel.Repository,
) *Logic {
	return &Logic{
		contactRepo:       contactRepo,
		contactTagRepo:    contactTagRepo,
		contactTagRelRepo: contactTagRelRepo,
	}
}

func (l *Logic) Create(ctx context.Context, contact *Contact) (*Contact, error) {
	// todo: check if the contact already exists, wait tag develop
	now := time.Now().UTC()
	contact.State = customerpb.Contact_ACTIVE
	contact.CreatedTime = now
	contact.UpdatedTime = now
	dbContact := contact.ToDB()
	dbContact, err := l.contactRepo.Create(ctx, dbContact)
	if err != nil {
		return nil, err
	}
	resultContact := convertToContact(dbContact)
	resultContact.Tags, err = l.associateTags(ctx, resultContact)
	if err != nil {
		return nil, err
	}
	return resultContact, nil
}

func (l *Logic) Get(ctx context.Context, id int64) (*Contact, error) {
	dbContact, err := l.contactRepo.Get(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_CONTACT_NOT_FOUND)
		}
		return nil, err
	}
	contact := convertToContact(dbContact)
	tags, err := l.associateTags(ctx, contact)
	if err != nil {
		return nil, err
	}
	contact.Tags = tags
	return contact, nil
}

func (l *Logic) List(ctx context.Context, req *ListContactsRequest) (*ListContactsResponse, error) {
	if req.Filter == nil {
		return nil, errs.Newm(codes.InvalidArgument, "filter is required")
	}
	if req.OrderBy == nil {
		req.OrderBy = &ListContactsOrderBy{
			Field:     customerpb.ListContactsRequest_Sorting_CREATED_TIME,
			Direction: customerpb.ListContactsRequest_Sorting_DESC,
		}
	}
	dbContacts, err := l.contactRepo.ListByCursor(ctx, &contactrepo.ListFilter{
		IDs:         req.Filter.IDs,
		CustomerIDs: req.Filter.CustomerIDs,
		States:      req.Filter.States,
	}, &contactrepo.Pagination{
		PageSize:        req.Pagination.PageSize,
		Cursor:          utils.DecodeCursor(req.Pagination.Cursor),
		ReturnTotalSize: req.Pagination.ReturnTotalSize,
	}, &contactrepo.OrderBy{
		Field:     req.OrderBy.Field,
		Direction: req.OrderBy.Direction,
	})
	if err != nil {
		return nil, err
	}
	contacts := convertToContacts(dbContacts.Data)
	// 组装tag 到contact
	for _, c := range contacts {
		tags, err := l.associateTags(ctx, c)
		if err != nil {
			return nil, err
		}
		c.Tags = tags
	}

	result := &ListContactsResponse{
		Contacts: contacts,
		HasNext:  dbContacts.HasNext,
	}
	if dbContacts.TotalCount != nil {
		result.TotalSize = dbContacts.TotalCount
	}

	if dbContacts.HasNext && len(dbContacts.Data) > 0 {
		lastContact := dbContacts.Data[len(dbContacts.Data)-1]
		cursor := postgres.Cursor{
			ID:        lastContact.ID,
			CreatedAt: lastContact.CreatedTime,
		}
		result.NextToken = cursor.EncodeCursor()
	}
	return result, nil
}

func (l *Logic) Update(ctx context.Context, updateRef *UpdateContactRequest) (*Contact, error) {
	dbContact, err := l.Get(ctx, updateRef.ID)
	if err != nil {
		return nil, err
	}

	now := time.Now().UTC()
	dbContact.Email = updateRef.Email
	dbContact.Phone = updateRef.Phone
	dbContact.GivenName = updateRef.GivenName
	dbContact.FamilyName = updateRef.FamilyName
	dbContact.UpdatedTime = now
	dbContact.Tags = make([]*contacttag.ContactTag, 0, len(updateRef.TagIDs))
	for _, tagID := range updateRef.TagIDs {
		tag, err := l.contactTagRepo.Get(ctx, tagID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errs.New(customerpb.ErrCode_ERR_CODE_CONTACT_TAG_NOT_FOUND)
			}
			return nil, err
		}
		t := &contacttag.ContactTag{}
		dbContact.Tags = append(dbContact.Tags, t.Load(tag))
	}
	// update contact
	updatedContact, err := l.contactRepo.Update(ctx, dbContact.ToDB())
	if err != nil {
		return nil, err
	}
	result := convertToContact(updatedContact)
	result.Tags, err = l.associateTags(ctx, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (l *Logic) Delete(ctx context.Context, id int64, inactivate bool) error {
	dbContact, err := l.Get(ctx, id)
	if err != nil {
		return err
	}
	now := time.Now().UTC()
	dbContact.UpdatedTime = now
	dbContact.State = customerpb.Contact_INACTIVE
	if !inactivate {
		deletedTime := now
		dbContact.DeletedTime = &deletedTime
		dbContact.State = customerpb.Contact_DELETED
	}

	_, err = l.contactRepo.Update(ctx, dbContact.ToDB())
	if err != nil {
		return err
	}

	return nil
}

func convertToContacts(dbContacts []*contactrepo.Contact) []*Contact {
	contacts := make([]*Contact, 0, len(dbContacts))
	for _, dbContact := range dbContacts {
		contacts = append(contacts, convertToContact(dbContact))
	}
	return contacts
}

func convertToContact(dbContact *contactrepo.Contact) *Contact {
	return &Contact{
		ID:           dbContact.ID,
		CustomerID:   dbContact.CustomerID,
		GivenName:    dbContact.GivenName,
		FamilyName:   dbContact.FamilyName,
		Email:        dbContact.Email,
		Phone:        dbContact.Phone,
		IsSelf:       dbContact.IsSelf,
		State:        dbContact.State,
		CustomFields: dbContact.CustomFields,
		DeletedTime:  dbContact.DeletedTime,
		CreatedTime:  dbContact.CreatedTime,
		UpdatedTime:  dbContact.UpdatedTime,
	}
}

func (l *Logic) associateTags(ctx context.Context, contact *Contact) ([]*contacttag.ContactTag, error) {
	contactID := contact.ID
	tagRels, err := l.contactTagRelRepo.List(ctx, &contacttagrel.ListFilter{
		ContactID: contactID,
	})
	if err != nil {
		return nil, err
	}
	tagIDs := make([]int64, 0, len(tagRels))
	for _, tagRel := range tagRels {
		tagIDs = append(tagIDs, tagRel.TagID)
	}
	dbTags, err := l.contactTagRepo.ListByCursor(ctx, &contacttagrepo.ListFilter{
		IDs: tagIDs,
	}, &contacttagrepo.Pagination{
		PageSize: int32(len(tagRels)),
	}, &contacttagrepo.OrderBy{
		Field:     customerpb.ListContactTagsRequest_Sorting_CREATED_TIME,
		Direction: customerpb.ListContactTagsRequest_Sorting_ASC,
	})
	if err != nil {
		return nil, err
	}

	tags := make([]*contacttag.ContactTag, 0, len(dbTags.Data))
	for _, tag := range dbTags.Data {
		t := &contacttag.ContactTag{}
		tags = append(tags, t.Load(tag))
	}
	return tags, nil
}
