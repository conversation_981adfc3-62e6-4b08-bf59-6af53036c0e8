package contact

import (
	"encoding/base64"
	"encoding/json"
	"time"

	"google.golang.org/genproto/googleapis/type/phone_number"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/datatypes"

	contacttag "github.com/MoeGolibrary/moego/backend/app/customer/logic/contact_tag"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	contactrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Contact struct {
	ID           int64                    `gorm:"primaryKey;column:id"`
	CustomerID   int64                    `gorm:"column:customer_id"`
	GivenName    string                   `gorm:"column:given_name"`
	FamilyName   string                   `gorm:"column:family_name"`
	Email        string                   `gorm:"column:email"`
	Phone        string                   `gorm:"column:phone"`
	IsSelf       bool                     `gorm:"column:is_self"`
	State        customerpb.Contact_State `gorm:"column:state;serializer:proto_enum"`
	CustomFields datatypes.JSON           `gorm:"column:custom_fields;type:jsonb;default:'{}'"`
	DeletedTime  *time.Time               `gorm:"column:deleted_time"`
	CreatedTime  time.Time                `gorm:"column:created_time;autoCreateTime"`
	UpdatedTime  time.Time                `gorm:"column:updated_time;autoUpdateTime"`
	// 额外字段
	// 这里的Tag 只是关联字段, 不是contact 模型上
	Tags []*contacttag.ContactTag `gorm:"-"`
}

func (c *Contact) ToDB() *contactrepo.Contact {
	dbContact := &contactrepo.Contact{
		ID:           c.ID,
		CustomerID:   c.CustomerID,
		GivenName:    c.GivenName,
		FamilyName:   c.FamilyName,
		Email:        c.Email,
		Phone:        c.Phone,
		IsSelf:       c.IsSelf,
		State:        c.State,
		CustomFields: c.CustomFields,
		DeletedTime:  c.DeletedTime,
		CreatedTime:  c.CreatedTime,
		UpdatedTime:  c.UpdatedTime,
	}
	for _, tag := range c.Tags {
		t := tag.ToDB()
		t.CreatedTime = c.CreatedTime
		t.UpdatedTime = c.UpdatedTime
		t.State = customerpb.ContactTag_ACTIVE
		dbContact.Tags = append(dbContact.Tags, t)
	}
	return dbContact
}

func (c *Contact) ToPB() *customerpb.Contact {
	if c == nil {
		return nil
	}
	contact := &customerpb.Contact{
		Id:         c.ID,
		CustomerId: c.CustomerID,
		GivenName:  c.GivenName,
		FamilyName: c.FamilyName,
		State:      c.State,
		IsSelf:     c.IsSelf,
		Email:      c.Email,
		Phone: &phone_number.PhoneNumber{
			Kind: &phone_number.PhoneNumber_E164Number{
				E164Number: c.Phone,
			},
		},
		CreateTime: &timestamppb.Timestamp{
			Seconds: c.CreatedTime.Unix(),
			Nanos:   int32(c.CreatedTime.Nanosecond()),
		},
		UpdateTime: &timestamppb.Timestamp{
			Seconds: c.UpdatedTime.Unix(),
			Nanos:   int32(c.UpdatedTime.Nanosecond()),
		},
	}

	if c.DeletedTime != nil {
		contact.DeleteTime = &timestamppb.Timestamp{
			Seconds: c.DeletedTime.Unix(),
			Nanos:   int32(c.DeletedTime.Nanosecond()),
		}
	}
	for _, tag := range c.Tags {
		contact.Tags = append(contact.Tags, tag.ToPB())
	}
	return contact
}

// ListContactsRequest
type ListContactsRequest struct {
	Filter     *ListContactsFilter     `json:"filter"`
	OrderBy    *ListContactsOrderBy    `json:"order_by"`
	Pagination *ListContactsPagination `json:"pagination"`
}

// ListContactsFilter
type ListContactsFilter struct {
	IDs           []int64                    `json:"ids"`
	CustomerIDs   []int64                    `json:"customer_ids"`
	States        []customerpb.Contact_State `json:"states"`
	OwnerStaffIDs []int64                    `json:"owner_staff_ids"`
	LifecycleIDs  []int64                    `json:"lifecycle_ids"`
}

// ListContactsOrderBy
type ListContactsOrderBy struct {
	Field     customerpb.ListContactsRequest_Sorting_Field     `json:"field"`
	Direction customerpb.ListContactsRequest_Sorting_Direction `json:"direction"`
}

// ListContactsPagination
type ListContactsPagination struct {
	PageSize        int32  `json:"page_size"`
	Cursor          string `json:"cursor"`
	ReturnTotalSize bool   `json:"return_total_size"`
}

func (p *ListContactsPagination) DecodeCursor() *postgres.Cursor {
	if p.Cursor == "" {
		return nil
	}
	bytes, err := base64.StdEncoding.DecodeString(p.Cursor)
	if err != nil {
		return nil
	}
	cursor := &postgres.Cursor{}
	_ = json.Unmarshal(bytes, cursor)
	return cursor
}

type ListContactsResponse struct {
	Contacts  []*Contact `json:"contacts"`
	HasNext   bool       `json:"has_next"`
	NextToken string     `json:"next_token"`
	TotalSize *int64     `json:"total_size"`
}

// UpdateContactRequest
type UpdateContactRequest struct {
	ID         int64   `json:"id"`
	GivenName  string  `json:"given_name"`
	FamilyName string  `json:"family_name"`
	Email      string  `json:"email"`
	Phone      string  `json:"phone"`
	TagIDs     []int64 `json:"tag_ids"`
}
