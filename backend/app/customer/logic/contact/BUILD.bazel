load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "contact",
    srcs = [
        "contact.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/contact",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/logic/contact_tag",
        "//backend/app/customer/repo/postgres",
        "//backend/app/customer/repo/postgres/contact",
        "//backend/app/customer/repo/postgres/contact_tag",
        "//backend/app/customer/repo/postgres/contact_tag_rel",
        "//backend/app/customer/utils",
        "//backend/common/rpc/framework/errs",
        "//backend/proto/customer/v2:customer",
        "@io_gorm_datatypes//:datatypes",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_genproto//googleapis/type/phone_number",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "contact_test",
    srcs = ["contact_test.go"],
    embed = [":contact"],
    deps = [
        "//backend/app/customer/logic/contact_tag",
        "//backend/app/customer/repo/postgres",
        "//backend/app/customer/repo/postgres/contact",
        "//backend/app/customer/repo/postgres/contact/mock",
        "//backend/app/customer/repo/postgres/contact_tag",
        "//backend/app/customer/repo/postgres/contact_tag/mock",
        "//backend/app/customer/repo/postgres/contact_tag_rel",
        "//backend/app/customer/repo/postgres/contact_tag_rel/mock",
        "//backend/app/customer/utils",
        "//backend/proto/customer/v2:customer",
        "@com_github_stretchr_testify//require",
        "@io_gorm_datatypes//:datatypes",
        "@io_gorm_gorm//:gorm",
        "@org_uber_go_mock//gomock",
    ],
)
