package customersearch

import (
	"context"
	"fmt"
	"strconv"

	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/pet"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/search"
	"github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	structutil "github.com/MoeGolibrary/moego/backend/common/utils/struct"
	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

type Logic struct {
	customerRepo customerrepo.Repository
	searchRPC    search.ReadWriter
	petRPC       pet.ReadWriter
}

func New() *Logic {
	return &Logic{
		customerRepo: customerrepo.New(),
		searchRPC:    search.New(),
		petRPC:       pet.New(),
	}
}

func (l *Logic) SyncES(ctx context.Context, customerID int64, optType searchpb.OperationType) error {
	customer, err := l.customerRepo.GetCustomer(ctx, &customerrepo.GetCustomerParams{ID: customerID})
	if err != nil {
		log.ErrorContextf(ctx, "SyncES GetCustomer err:%+v, customerID:%d", err, customerID)
		return err
	}

	pets, err := l.petRPC.ListCustomerPet(ctx, customerID)
	if err != nil {
		log.ErrorContextf(ctx, "SyncES ListCustomerPet err:%+v, customerID:%d", err, customerID)
		return err
	}

	searchDatum := convToCustomerSearchDatum(customer, pets)
	st, err := structutil.ConvertToProtoStruct(searchDatum)
	if err != nil {
		log.ErrorContextf(ctx, "SyncES ConvertToProtoStruct err:%+v, searchDatum:%+v", err,
			utils.JSONMarshalNoErr(searchDatum))
		return err
	}
	_, err = l.searchRPC.BulkDocument(ctx, search.CustomerIndexName, strconv.FormatInt(customerID, 10), st, optType)
	if err != nil {
		log.ErrorContextf(ctx, "SyncES searchRPC Create err:%+v", err)
		return err
	}

	log.InfoContextf(ctx, "SyncES success, customerID:%d", customerID)
	return nil
}

func (l *Logic) SearchLead(ctx context.Context, companyID int64, keyword string) ([]int64, error) {
	req := search.BuildLeadSearchDocumentRequest(int(companyID), 1, keyword, 100)
	hits, err := l.searchRPC.SearchDocument(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "SearchLead SearchDocument err:%+v, req:%+v", err, utils.JSONMarshalNoErr(req))
		return nil, err
	}
	if len(hits) == 0 {
		log.ErrorContextf(ctx, "SearchLead SearchDocument hits is empty")
		return []int64{}, nil
	}

	// parse ids
	res := make([]int64, 0, len(hits))
	for _, hit := range hits {
		id := utils.StringToInt64WithDefault(hit.GetId(), 0)
		if id == 0 {
			continue
		}
		res = append(res, id)
	}

	return res, nil
}

func convToCustomerSearchDatum(customer *customerrepo.BusinessCustomer, pets []*petpb.Pet) *search.CustomerSearchDatum {
	res := &search.CustomerSearchDatum{
		ID:         customer.ID,
		BusinessID: int64(customer.BusinessID),
		CompanyID:  customer.CompanyID,
		Email:      utils.ToValue(customer.Email),
		FirstName:  customer.FirstName,
		LastName:   customer.LastName,
		Name:       fmt.Sprintf("%s %s", customer.FirstName, customer.LastName),
		Status:     customer.Status,
		Address:    convToAddressSearchDatums(customer.Addresses),
		Contact:    convToContactSearchDatums(customer.Contacts),
		Pet:        convToPetSearchDatums(pets),
	}
	return res
}

func convToPetSearchDatums(pets []*petpb.Pet) []*search.CustomerPetDatum {
	res := make([]*search.CustomerPetDatum, 0, len(pets))
	for _, pet := range pets {
		if pet == nil {
			continue
		}
		res = append(res, &search.CustomerPetDatum{
			Name:  pet.Name,
			State: int8(pet.State),
		})
	}
	return res
}

func convToContactSearchDatums(contacts []*customerrepo.Contact) []*search.CustomerContactDatum {
	res := make([]*search.CustomerContactDatum, 0, len(contacts))
	for _, contact := range contacts {
		if contact == nil {
			continue
		}
		res = append(res, &search.CustomerContactDatum{
			FirstName:       contact.FirstName,
			LastName:        contact.LastName,
			PhoneNumber:     contact.PhoneNumber,
			Email:           contact.Email,
			Title:           contact.Title,
			Status:          contact.Status,
			E164PhoneNumber: contact.E164PhoneNumber,
		})
	}
	return res
}

func convToAddressSearchDatums(addresses []*customerrepo.Address) []*search.CustomerAddressDatum {
	res := make([]*search.CustomerAddressDatum, 0, len(addresses))
	for _, address := range addresses {
		if address == nil {
			continue
		}
		res = append(res, &search.CustomerAddressDatum{
			Address1: address.Address1,
			Address2: address.Address2,
			City:     address.City,
			State:    address.State,
			Zipcode:  address.Zipcode,
			Country:  address.Country,
			Status:   address.Status,
		})
	}
	return res
}
