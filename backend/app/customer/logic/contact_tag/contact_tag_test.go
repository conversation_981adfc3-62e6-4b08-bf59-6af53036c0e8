package contacttag

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	contacttagrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag"
	contacttagmock "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag/mock"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

func TestNew(t *testing.T) {
	t.Run("测试创建新的Logic实例", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)
		require.NotNil(t, logic)
	})

	t.Run("New方法", func(t *testing.T) {
		postgres.SetDB(&gorm.DB{})
		logic := New()
		require.NotNil(t, logic)
	})
}

func TestCreate(t *testing.T) {
	t.Run("测试创建联系人标签成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		contactTag := &ContactTag{
			OrganizationType: customerpb.OrganizationRef_COMPANY,
			OrganizationID:   123,
			Name:             "VIP客户",
			Color:            "#FF0000",
			SortOrder:        1,
			Description:      "重要客户标签",
		}

		// 设置mock期望
		mockContactTag := &contacttagrepo.ContactTag{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_COMPANY,
			OrganizationID:   123,
			Name:             "VIP客户",
			Color:            "#FF0000",
			SortOrder:        1,
			Description:      "重要客户标签",
			State:            customerpb.ContactTag_ACTIVE,
			CreatedTime:      time.Now(),
			UpdatedTime:      time.Now(),
		}
		contactTagRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(mockContactTag, nil)

		// 执行测试
		result, err := logic.Create(context.Background(), contactTag)
		require.NoError(t, err, "创建联系人标签时不应返回错误")
		require.NotNil(t, result, "返回的联系人标签结果不应为nil")
		require.Equal(t, int64(1), result.ID, "联系人标签ID应为1")
		require.Equal(t, customerpb.ContactTag_ACTIVE, result.State, "联系人标签状态应为ACTIVE")
		require.Equal(t, "VIP客户", result.Name, "名称应正确设置")
		require.Equal(t, "#FF0000", result.Color, "颜色应正确设置")
	})

	t.Run("测试创建联系人标签失败-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		contactTag := &ContactTag{
			OrganizationType: customerpb.OrganizationRef_COMPANY,
			OrganizationID:   123,
			Name:             "VIP客户",
			Color:            "#FF0000",
		}

		// 设置mock期望
		expectedErr := errors.New("数据库错误")
		contactTagRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		result, err := logic.Create(context.Background(), contactTag)
		require.Error(t, err, "应返回错误")
		require.Nil(t, result, "返回结果应为nil")
		require.Equal(t, expectedErr, err)
	})
}

func TestGet(t *testing.T) {
	t.Run("测试获取联系人标签成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		tagID := int64(1)

		// 设置mock期望
		expectedContactTag := &contacttagrepo.ContactTag{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_COMPANY,
			OrganizationID:   123,
			Name:             "VIP客户",
			Color:            "#FF0000",
			SortOrder:        1,
			Description:      "重要客户标签",
			State:            customerpb.ContactTag_ACTIVE,
			CreatedTime:      time.Now(),
			UpdatedTime:      time.Now(),
		}
		contactTagRepo.EXPECT().Get(gomock.Any(), tagID).Return(expectedContactTag, nil)

		// 执行测试
		result, err := logic.Get(context.Background(), tagID)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, int64(1), result.ID)
		require.Equal(t, "VIP客户", result.Name)
		require.Equal(t, "#FF0000", result.Color)
		require.Equal(t, customerpb.OrganizationRef_COMPANY, result.OrganizationType)
		require.Equal(t, int64(123), result.OrganizationID)
	})

	t.Run("测试获取联系人标签失败-标签不存在", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		tagID := int64(999)

		// 设置mock期望
		contactTagRepo.EXPECT().Get(gomock.Any(), tagID).Return(nil, gorm.ErrRecordNotFound)

		// 执行测试
		result, err := logic.Get(context.Background(), tagID)
		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "120001", "error code is not 120001")
	})

	t.Run("测试获取联系人标签失败-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		tagID := int64(1)

		// 设置mock期望
		expectedErr := errors.New("数据库错误")
		contactTagRepo.EXPECT().Get(gomock.Any(), tagID).Return(nil, expectedErr)

		// 执行测试
		result, err := logic.Get(context.Background(), tagID)
		require.Error(t, err)
		require.Nil(t, result)
		require.Equal(t, expectedErr, err)
	})
}

func TestList(t *testing.T) {
	t.Run("测试获取联系人标签列表成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		req := &ListContactTagsRequest{
			Filter: &ListContactTagsFilter{
				OrganizationType: customerpb.OrganizationRef_COMPANY,
				OrganizationID:   123,
				States:           []customerpb.ContactTag_State{customerpb.ContactTag_ACTIVE},
			},
			Pagination: &ListContactTagsPagination{
				PageSize:        10,
				ReturnTotalSize: true,
			},
			OrderBy: &ListContactTagsOrderBy{
				Field:     customerpb.ListContactTagsRequest_Sorting_CREATED_TIME,
				Direction: customerpb.ListContactTagsRequest_Sorting_DESC,
			},
		}

		// 设置mock期望
		expectedContactTags := []*contacttagrepo.ContactTag{
			{
				ID:               1,
				OrganizationType: customerpb.OrganizationRef_COMPANY,
				OrganizationID:   123,
				Name:             "VIP客户",
				Color:            "#FF0000",
				SortOrder:        1,
				State:            customerpb.ContactTag_ACTIVE,
				CreatedTime:      time.Now(),
				UpdatedTime:      time.Now(),
			},
			{
				ID:               2,
				OrganizationType: customerpb.OrganizationRef_COMPANY,
				OrganizationID:   123,
				Name:             "重要客户",
				Color:            "#00FF00",
				SortOrder:        2,
				State:            customerpb.ContactTag_ACTIVE,
				CreatedTime:      time.Now().Add(-time.Hour),
				UpdatedTime:      time.Now().Add(-time.Hour),
			},
		}

		contactTagRepo.EXPECT().ListByCursor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&contacttagrepo.CursorResult{
			Data:       expectedContactTags,
			HasNext:    true,
			TotalCount: func() *int64 { count := int64(2); return &count }(),
		}, nil)

		// 执行测试
		result, err := logic.List(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Len(t, result.ContactTags, 2)
		require.True(t, result.HasNext)
		require.NotEmpty(t, result.NextToken)
		require.Equal(t, int64(2), *result.TotalSize)
	})

	t.Run("测试获取联系人标签列表-空结果", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		req := &ListContactTagsRequest{
			Filter: &ListContactTagsFilter{
				OrganizationType: customerpb.OrganizationRef_COMPANY,
				OrganizationID:   123,
			},
			Pagination: &ListContactTagsPagination{
				PageSize:        10,
				ReturnTotalSize: true,
			},
			OrderBy: &ListContactTagsOrderBy{
				Field:     customerpb.ListContactTagsRequest_Sorting_CREATED_TIME,
				Direction: customerpb.ListContactTagsRequest_Sorting_DESC,
			},
		}

		// 设置mock期望
		contactTagRepo.EXPECT().ListByCursor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&contacttagrepo.CursorResult{
			Data:       []*contacttagrepo.ContactTag{},
			HasNext:    false,
			TotalCount: func() *int64 { count := int64(0); return &count }(),
		}, nil)

		// 执行测试
		result, err := logic.List(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Empty(t, result.ContactTags)
		require.False(t, result.HasNext)
		require.Empty(t, result.NextToken)
		require.Equal(t, int64(0), *result.TotalSize)
	})

	t.Run("测试获取联系人标签列表-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		req := &ListContactTagsRequest{
			Filter: &ListContactTagsFilter{
				OrganizationType: customerpb.OrganizationRef_COMPANY,
				OrganizationID:   123,
			},
			Pagination: &ListContactTagsPagination{
				PageSize: 10,
			},
		}

		// 设置mock期望
		expectedErr := errors.New("数据库错误")
		contactTagRepo.EXPECT().ListByCursor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		result, err := logic.List(context.Background(), req)
		require.Error(t, err)
		require.Nil(t, result)
		require.Equal(t, expectedErr, err)
	})

	t.Run("测试获取联系人标签列表-Filter为空", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		req := &ListContactTagsRequest{
			Pagination: &ListContactTagsPagination{
				PageSize: 10,
			},
		}

		// 执行测试
		result, err := logic.List(context.Background(), req)
		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "filter is required")
	})

	t.Run("测试获取联系人标签列表-OrderBy为空时设置默认值", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		req := &ListContactTagsRequest{
			Filter: &ListContactTagsFilter{
				OrganizationType: customerpb.OrganizationRef_COMPANY,
				OrganizationID:   123,
			},
			Pagination: &ListContactTagsPagination{
				PageSize: 10,
			},
			// OrderBy 为 nil，应该设置默认值
		}

		// 设置mock期望
		contactTagRepo.EXPECT().ListByCursor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&contacttagrepo.CursorResult{
			Data:    []*contacttagrepo.ContactTag{},
			HasNext: false,
		}, nil)

		// 执行测试
		result, err := logic.List(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Empty(t, result.ContactTags)
		require.False(t, result.HasNext)
	})
}

func TestUpdate(t *testing.T) {
	t.Run("测试更新联系人标签成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		tagID := int64(1)
		updateReq := &UpdateContactTagRequest{
			ID:          tagID,
			Name:        "更新后的VIP客户",
			Color:       "#00FF00",
			SortOrder:   2,
			Description: "更新后的描述",
		}

		// 设置mock期望 - Get
		existingContactTag := &ContactTag{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_COMPANY,
			OrganizationID:   123,
			Name:             "原VIP客户",
			Color:            "#FF0000",
			SortOrder:        1,
			Description:      "原描述",
			State:            customerpb.ContactTag_ACTIVE,
			CreatedTime:      time.Now().Add(-24 * time.Hour),
			UpdatedTime:      time.Now().Add(-24 * time.Hour),
		}
		contactTagRepo.EXPECT().Get(gomock.Any(), tagID).Return(existingContactTag.ToDB(), nil)

		// 设置mock期望 - Update
		updatedContactTag := &contacttagrepo.ContactTag{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_COMPANY,
			OrganizationID:   123,
			Name:             "更新后的VIP客户",
			Color:            "#00FF00",
			SortOrder:        2,
			Description:      "更新后的描述",
			State:            customerpb.ContactTag_ACTIVE,
			CreatedTime:      time.Now().Add(-24 * time.Hour),
			UpdatedTime:      time.Now(),
		}
		contactTagRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(updatedContactTag, nil)

		// 执行测试
		result, err := logic.Update(context.Background(), updateReq)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, int64(1), result.ID)
		require.Equal(t, "更新后的VIP客户", result.Name)
		require.Equal(t, "#00FF00", result.Color)
		require.Equal(t, int32(2), result.SortOrder)
		require.Equal(t, "更新后的描述", result.Description)
	})

	t.Run("测试更新联系人标签失败-标签不存在", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		tagID := int64(999)
		updateReq := &UpdateContactTagRequest{
			ID:   tagID,
			Name: "更新后的VIP客户",
		}

		// 设置mock期望
		contactTagRepo.EXPECT().Get(gomock.Any(), tagID).Return(nil, gorm.ErrRecordNotFound)

		// 执行测试
		result, err := logic.Update(context.Background(), updateReq)
		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "120001", "error code is not 120001")
	})

	t.Run("测试更新联系人标签失败-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		tagID := int64(1)
		updateReq := &UpdateContactTagRequest{
			ID:   tagID,
			Name: "更新后的VIP客户",
		}

		// 设置mock期望 - Get
		existingContactTag := &ContactTag{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_COMPANY,
			OrganizationID:   123,
			Name:             "原VIP客户",
			State:            customerpb.ContactTag_ACTIVE,
			CreatedTime:      time.Now().Add(-24 * time.Hour),
			UpdatedTime:      time.Now().Add(-24 * time.Hour),
		}
		contactTagRepo.EXPECT().Get(gomock.Any(), tagID).Return(existingContactTag.ToDB(), nil)

		// 设置mock期望 - Update
		expectedErr := errors.New("数据库错误")
		contactTagRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		result, err := logic.Update(context.Background(), updateReq)
		require.Error(t, err)
		require.Nil(t, result)
		require.Equal(t, expectedErr, err)
	})
}

func TestDelete(t *testing.T) {
	t.Run("测试删除联系人标签成功-软删除", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		tagID := int64(1)
		inactivate := true

		// 设置mock期望 - Get
		existingContactTag := &ContactTag{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_COMPANY,
			OrganizationID:   123,
			Name:             "VIP客户",
			State:            customerpb.ContactTag_ACTIVE,
			CreatedTime:      time.Now().Add(-24 * time.Hour),
			UpdatedTime:      time.Now().Add(-24 * time.Hour),
		}
		contactTagRepo.EXPECT().Get(gomock.Any(), tagID).Return(existingContactTag.ToDB(), nil)

		// 设置mock期望 - Update
		contactTagRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(&contacttagrepo.ContactTag{}, nil)

		// 执行测试
		err := logic.Delete(context.Background(), tagID, inactivate)
		require.NoError(t, err)
	})

	t.Run("测试删除联系人标签成功-硬删除", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		tagID := int64(1)
		inactivate := false

		// 设置mock期望 - Get
		existingContactTag := &ContactTag{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_COMPANY,
			OrganizationID:   123,
			Name:             "VIP客户",
			State:            customerpb.ContactTag_ACTIVE,
			CreatedTime:      time.Now().Add(-24 * time.Hour),
			UpdatedTime:      time.Now().Add(-24 * time.Hour),
		}
		contactTagRepo.EXPECT().Get(gomock.Any(), tagID).Return(existingContactTag.ToDB(), nil)

		// 设置mock期望 - Update
		contactTagRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(&contacttagrepo.ContactTag{}, nil)

		// 执行测试
		err := logic.Delete(context.Background(), tagID, inactivate)
		require.NoError(t, err)
	})

	t.Run("测试删除联系人标签失败-标签不存在", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		tagID := int64(999)
		inactivate := true

		// 设置mock期望
		contactTagRepo.EXPECT().Get(gomock.Any(), tagID).Return(nil, gorm.ErrRecordNotFound)

		// 执行测试
		err := logic.Delete(context.Background(), tagID, inactivate)
		require.Error(t, err)
		require.Contains(t, err.Error(), "120001", "error code is not 120001")
	})

	t.Run("测试删除联系人标签失败-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		contactTagRepo := contacttagmock.NewMockRepository(ctrl)
		logic := NewByParams(contactTagRepo)

		// 准备测试数据
		tagID := int64(1)
		inactivate := true

		// 设置mock期望 - Get
		existingContactTag := &ContactTag{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_COMPANY,
			OrganizationID:   123,
			Name:             "VIP客户",
			State:            customerpb.ContactTag_ACTIVE,
			CreatedTime:      time.Now().Add(-24 * time.Hour),
			UpdatedTime:      time.Now().Add(-24 * time.Hour),
		}
		contactTagRepo.EXPECT().Get(gomock.Any(), tagID).Return(existingContactTag.ToDB(), nil)

		// 设置mock期望 - Update
		expectedErr := errors.New("数据库错误")
		contactTagRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		err := logic.Delete(context.Background(), tagID, inactivate)
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
	})
}

func TestConvertToContactTag(t *testing.T) {
	t.Run("测试转换数据库联系人标签到逻辑联系人标签", func(t *testing.T) {
		// 准备测试数据
		now := time.Now()
		dbContactTag := &contacttagrepo.ContactTag{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_COMPANY,
			OrganizationID:   123,
			Name:             "VIP客户",
			Color:            "#FF0000",
			SortOrder:        1,
			Description:      "重要客户标签",
			State:            customerpb.ContactTag_ACTIVE,
			Type:             customerpb.ContactTag_CUSTOM,
			CreatedTime:      now,
			UpdatedTime:      now,
			DeletedTime:      nil,
		}

		// 执行测试
		result := convertToContactTag(dbContactTag)
		require.NotNil(t, result)
		require.Equal(t, int64(1), result.ID)
		require.Equal(t, customerpb.OrganizationRef_COMPANY, result.OrganizationType)
		require.Equal(t, int64(123), result.OrganizationID)
		require.Equal(t, "VIP客户", result.Name)
		require.Equal(t, "#FF0000", result.Color)
		require.Equal(t, int32(1), result.SortOrder)
		require.Equal(t, "重要客户标签", result.Description)
		require.Equal(t, customerpb.ContactTag_ACTIVE, result.State)
		require.Equal(t, customerpb.ContactTag_CUSTOM, result.Type)
		require.Equal(t, now, result.CreatedTime)
		require.Equal(t, now, result.UpdatedTime)
		require.Nil(t, result.DeletedTime)
	})

	t.Run("测试转换数据库联系人标签列表到逻辑联系人标签列表", func(t *testing.T) {
		// 准备测试数据
		now := time.Now()
		dbContactTags := []*contacttagrepo.ContactTag{
			{
				ID:               1,
				OrganizationType: customerpb.OrganizationRef_COMPANY,
				OrganizationID:   123,
				Name:             "VIP客户",
				Color:            "#FF0000",
				State:            customerpb.ContactTag_ACTIVE,
				CreatedTime:      now,
				UpdatedTime:      now,
			},
			{
				ID:               2,
				OrganizationType: customerpb.OrganizationRef_COMPANY,
				OrganizationID:   123,
				Name:             "重要客户",
				Color:            "#00FF00",
				State:            customerpb.ContactTag_ACTIVE,
				CreatedTime:      now.Add(-time.Hour),
				UpdatedTime:      now.Add(-time.Hour),
			},
		}

		// 执行测试
		result := convertToContactTags(dbContactTags)
		require.NotNil(t, result)
		require.Len(t, result, 2)
		require.Equal(t, int64(1), result[0].ID)
		require.Equal(t, "VIP客户", result[0].Name)
		require.Equal(t, int64(2), result[1].ID)
		require.Equal(t, "重要客户", result[1].Name)
	})
}

func TestContactTag_ToDB(t *testing.T) {
	t.Run("测试联系人标签转换为数据库模型", func(t *testing.T) {
		now := time.Now()
		delTime := now.Add(24 * time.Hour)
		contactTag := &ContactTag{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_COMPANY,
			OrganizationID:   123,
			Name:             "VIP客户",
			Color:            "#FF0000",
			SortOrder:        1,
			Description:      "重要客户标签",
			State:            customerpb.ContactTag_ACTIVE,
			Type:             customerpb.ContactTag_CUSTOM,
			CreatedTime:      now,
			UpdatedTime:      now,
			DeletedTime:      &delTime,
		}

		result := contactTag.ToDB()
		require.NotNil(t, result)
		require.Equal(t, int64(1), result.ID)
		require.Equal(t, customerpb.OrganizationRef_COMPANY, result.OrganizationType)
		require.Equal(t, int64(123), result.OrganizationID)
		require.Equal(t, "VIP客户", result.Name)
		require.Equal(t, "#FF0000", result.Color)
		require.Equal(t, int32(1), result.SortOrder)
		require.Equal(t, "重要客户标签", result.Description)
		require.Equal(t, customerpb.ContactTag_ACTIVE, result.State)
		require.Equal(t, customerpb.ContactTag_CUSTOM, result.Type)
		require.Equal(t, now, result.CreatedTime)
		require.Equal(t, now, result.UpdatedTime)
		require.Equal(t, &delTime, result.DeletedTime)
	})
}

func TestContactTag_ToPB(t *testing.T) {
	t.Run("正常转换-有删除时间", func(t *testing.T) {
		now := time.Now().UTC()
		delTime := now.Add(24 * time.Hour)
		c := &ContactTag{
			ID:               123,
			OrganizationType: customerpb.OrganizationRef_COMPANY,
			OrganizationID:   456,
			Name:             "VIP客户",
			Color:            "#FF0000",
			SortOrder:        1,
			Description:      "重要客户标签",
			State:            customerpb.ContactTag_ACTIVE,
			Type:             customerpb.ContactTag_CUSTOM,
			DeletedTime:      &delTime,
			CreatedTime:      now,
			UpdatedTime:      now,
		}
		pb := c.ToPB()
		require.NotNil(t, pb)
		require.Equal(t, c.ID, pb.Id)
		require.Equal(t, c.Name, pb.Name)
		require.Equal(t, c.Color, pb.Color)
		require.Equal(t, c.SortOrder, pb.SortOrder)
		require.Equal(t, c.State, pb.State)
		require.Equal(t, c.Description, pb.Description)
		require.Equal(t, c.Type, pb.Type)
		require.Equal(t, c.OrganizationType, pb.Organization.Type)
		require.Equal(t, c.OrganizationID, pb.Organization.Id)
		require.NotNil(t, pb.DeleteTime)
		require.Equal(t, c.CreatedTime.Unix(), pb.CreateTime.Seconds)
		require.Equal(t, c.UpdatedTime.Unix(), pb.UpdateTime.Seconds)
		require.Equal(t, c.DeletedTime.Unix(), pb.DeleteTime.Seconds)
	})

	t.Run("正常转换-无删除时间", func(t *testing.T) {
		now := time.Now().UTC()
		c := &ContactTag{
			ID:               123,
			OrganizationType: customerpb.OrganizationRef_COMPANY,
			OrganizationID:   456,
			Name:             "VIP客户",
			Color:            "#FF0000",
			SortOrder:        1,
			Description:      "重要客户标签",
			State:            customerpb.ContactTag_ACTIVE,
			Type:             customerpb.ContactTag_CUSTOM,
			DeletedTime:      nil,
			CreatedTime:      now,
			UpdatedTime:      now,
		}
		pb := c.ToPB()
		require.NotNil(t, pb)
		require.Equal(t, c.ID, pb.Id)
		require.Equal(t, c.Name, pb.Name)
		require.Equal(t, c.Color, pb.Color)
		require.Equal(t, c.SortOrder, pb.SortOrder)
		require.Equal(t, c.State, pb.State)
		require.Equal(t, c.Description, pb.Description)
		require.Equal(t, c.Type, pb.Type)
		require.Equal(t, c.OrganizationType, pb.Organization.Type)
		require.Equal(t, c.OrganizationID, pb.Organization.Id)
		require.Nil(t, pb.DeleteTime)
		require.Equal(t, c.CreatedTime.Unix(), pb.CreateTime.Seconds)
		require.Equal(t, c.UpdatedTime.Unix(), pb.UpdateTime.Seconds)
	})

	t.Run("c为nil", func(t *testing.T) {
		var c *ContactTag
		pb := c.ToPB()
		require.Nil(t, pb)
	})
}

func TestContactTag_Load(t *testing.T) {
	t.Run("测试从数据库模型加载联系人标签", func(t *testing.T) {
		now := time.Now()
		dbTag := &contacttagrepo.ContactTag{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_COMPANY,
			OrganizationID:   123,
			Name:             "VIP客户",
			Color:            "#FF0000",
			SortOrder:        1,
			Description:      "重要客户标签",
			State:            customerpb.ContactTag_ACTIVE,
			Type:             customerpb.ContactTag_CUSTOM,
			CreatedTime:      now,
			UpdatedTime:      now,
			DeletedTime:      nil,
		}

		contactTag := &ContactTag{}
		result := contactTag.Load(dbTag)
		require.NotNil(t, result)
		require.Equal(t, int64(1), result.ID)
		require.Equal(t, customerpb.OrganizationRef_COMPANY, result.OrganizationType)
		require.Equal(t, int64(123), result.OrganizationID)
		require.Equal(t, "VIP客户", result.Name)
		require.Equal(t, "#FF0000", result.Color)
		require.Equal(t, int32(1), result.SortOrder)
		require.Equal(t, "重要客户标签", result.Description)
		require.Equal(t, customerpb.ContactTag_ACTIVE, result.State)
		require.Equal(t, customerpb.ContactTag_CUSTOM, result.Type)
		require.Equal(t, now, result.CreatedTime)
		require.Equal(t, now, result.UpdatedTime)
		require.Nil(t, result.DeletedTime)
	})
}

func TestListContactTagsPagination_DecodeCursor(t *testing.T) {
	t.Run("空Cursor", func(t *testing.T) {
		p := &ListContactTagsPagination{Cursor: ""}
		cursor := p.DecodeCursor()
		require.Nil(t, cursor)
	})

	t.Run("非法base64", func(t *testing.T) {
		p := &ListContactTagsPagination{Cursor: "!!!notbase64"}
		cursor := p.DecodeCursor()
		require.Nil(t, cursor)
	})

	t.Run("合法base64但非法json", func(t *testing.T) {
		// base64编码的字符串，但不是合法json
		p := &ListContactTagsPagination{Cursor: "aW52YWxpZCBqc29u"}
		cursor := p.DecodeCursor()
		require.NotNil(t, cursor) // 解析失败也会返回空结构体
	})

	t.Run("合法base64和合法json", func(t *testing.T) {
		type testCursor struct {
			ID        int64     `json:"id"`
			CreatedAt time.Time `json:"created_at"`
		}
		origin := testCursor{ID: 123, CreatedAt: time.Now()}
		b, _ := json.Marshal(origin)
		encoded := base64.StdEncoding.EncodeToString(b)
		p := &ListContactTagsPagination{Cursor: encoded}
		cursor := p.DecodeCursor()
		require.NotNil(t, cursor)
	})
}

func TestGORMZeroValueUpdate(t *testing.T) {
	t.Run("测试GORM零值不会被更新", func(t *testing.T) {
		// 这个测试验证了GORM的Updates()方法不会更新零值字段
		// 在实际的数据库操作中，零值字段会被忽略

		// 模拟一个更新请求，其中某些字段为空值
		updateReq := &UpdateContactTagRequest{
			ID:          1,
			Name:        "", // 空字符串 - 零值
			Color:       "", // 空字符串 - 零值
			SortOrder:   0,  // 0 - 零值
			Description: "", // 空字符串 - 零值
		}

		// 在GORM的Updates()方法中，这些零值字段不会被更新到数据库
		// 只有非零值字段（如UpdatedTime）会被更新

		// 验证零值的定义
		require.Equal(t, "", updateReq.Name)            // 空字符串是零值
		require.Equal(t, "", updateReq.Color)           // 空字符串是零值
		require.Equal(t, int32(0), updateReq.SortOrder) // 0是零值
		require.Equal(t, "", updateReq.Description)     // 空字符串是零值

		// 这些零值在GORM的Updates()中会被忽略，不会覆盖数据库中的现有值
	})
}
