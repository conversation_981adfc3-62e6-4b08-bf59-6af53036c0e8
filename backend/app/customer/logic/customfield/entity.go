package customfield

import (
	"time"

	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Option struct {
	ID            int64
	FieldID       int64
	ValueString   string
	ValueInt64    int64
	ValueDouble   float64
	ValueBool     bool
	ValueMoney    any
	ValueRelation any
	Label         string
	SortOrder     int
	State         customerpb.CustomField_State
	CreatedTime   time.Time
	UpdatedTime   time.Time
	DeletedTime   *time.Time
}

func (o *Option) ToPB() *customerpb.CustomField_Option {
	if o == nil {
		return nil
	}

	// 构造 Value - 支持所有类型
	var value *customerpb.CustomField_Value

	// 按优先级检查各种值类型，优先检查有意义的值
	if o.ValueString != "" {
		value = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_String_{String_: o.ValueString},
		}
	} else if o.ValueInt64 != 0 {
		value = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_Int64{Int64: o.ValueInt64},
		}
	} else if o.ValueDouble != 0 {
		value = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_DoubleValue{DoubleValue: o.ValueDouble},
		}
	} else if o.ValueBool {
		value = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_Bool{Bool: o.ValueBool},
		}
	} else if o.ValueMoney != nil {
		// 处理Money类型 - JSON反序列化后是map[string]any
		if moneyMap, ok := o.ValueMoney.(map[string]any); ok {
			// 安全地提取字段值
			currencyCode, currencyOk := moneyMap["currency_code"].(string)
			units, unitsOk := moneyMap["units"].(float64)
			nanos, nanosOk := moneyMap["nanos"].(float64)

			if currencyOk && unitsOk && nanosOk {
				money := &money.Money{
					CurrencyCode: currencyCode,
					Units:        int64(units),
					Nanos:        int32(nanos),
				}
				value = &customerpb.CustomField_Value{
					Value: &customerpb.CustomField_Value_Money{Money: money},
				}
			}
		}
	} else if o.ValueRelation != nil {
		// 处理Relation类型 - JSON反序列化后是map[string]any
		if relationMap, ok := o.ValueRelation.(map[string]any); ok {
			// 安全地提取字段值
			entityStr, entityOk := relationMap["entity"].(string)
			id, idOk := relationMap["id"].(float64)

			if entityOk && idOk {
				var entityEnum customerpb.CustomField_Value_Relation_Entity
				switch entityStr {
				case "CUSTOMER":
					entityEnum = customerpb.CustomField_Value_Relation_CUSTOMER
				case "LEAD":
					entityEnum = customerpb.CustomField_Value_Relation_LEAD
				default:
					// 未知的entity类型，跳过
					break
				}

				if entityEnum != customerpb.CustomField_Value_Relation_ENTITY_UNSPECIFIED {
					relation := &customerpb.CustomField_Value_Relation{
						Entity: entityEnum,
						Id:     int64(id),
					}
					value = &customerpb.CustomField_Value{
						Value: &customerpb.CustomField_Value_Relation_{Relation: relation},
					}
				}
			}
		}
	}

	return &customerpb.CustomField_Option{
		Value:     value,
		Label:     o.Label,
		SortOrder: int32(o.SortOrder),
		State:     o.State,
	}
}

type Definition struct {
	ID               int64
	OrganizationType customerpb.OrganizationRef_Type
	OrganizationID   int64
	AssociationType  customerpb.CustomField_AssociationType
	FieldName        string
	FieldLabel       string
	FieldType        customerpb.CustomField_Type
	IsRequired       bool
	DefaultValue     any
	ValidationRules  map[string]any
	DisplayOrder     int
	HelpText         string
	State            customerpb.CustomField_State
	DeletedTime      *time.Time
	CreatedTime      time.Time
	UpdatedTime      time.Time
}

func (d *Definition) ToPB() *customerpb.CustomField {
	if d == nil {
		return nil
	}

	customField := &customerpb.CustomField{
		Id: d.ID,
		Organization: &customerpb.OrganizationRef{
			Type: d.OrganizationType,
			Id:   d.OrganizationID,
		},
		AssociationType: d.AssociationType,
		Key:             d.FieldName,
		Label:           d.FieldLabel,
		Type:            d.FieldType,
		IsRequired:      d.IsRequired,
		State:           d.State,
		DisplayOrder:    int32(d.DisplayOrder),
		HelpText:        d.HelpText,
		CreateTime: &timestamppb.Timestamp{
			Seconds: d.CreatedTime.Unix(),
			Nanos:   int32(d.CreatedTime.Nanosecond()),
		},
		UpdateTime: &timestamppb.Timestamp{
			Seconds: d.UpdatedTime.Unix(),
			Nanos:   int32(d.UpdatedTime.Nanosecond()),
		},
	}

	// 处理默认值
	customField.DefaultValue = d.convertDefaultValue()

	// 处理验证规则
	if d.ValidationRules != nil {
		if validationRulesStruct, err := structpb.NewStruct(d.ValidationRules); err == nil {
			customField.ValidationRules = validationRulesStruct
		}
	}

	// 处理删除时间
	if d.DeletedTime != nil {
		customField.DeleteTime = &timestamppb.Timestamp{
			Seconds: d.DeletedTime.Unix(),
			Nanos:   int32(d.DeletedTime.Nanosecond()),
		}
	}

	return customField
}

// convertDefaultValue 将默认值转换为protobuf格式
func (d *Definition) convertDefaultValue() *customerpb.CustomField_Value {
	if d.DefaultValue == nil {
		return nil
	}

	switch v := d.DefaultValue.(type) {
	case string:
		return &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_String_{String_: v},
		}
	case int64:
		return &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_Int64{Int64: v},
		}
	case float64:
		return &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_DoubleValue{DoubleValue: v},
		}
	case bool:
		return &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_Bool{Bool: v},
		}
	case *money.Money:
		// 直接处理Money对象（Service层传入的情况）
		return &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_Money{Money: v},
		}
	case *customerpb.CustomField_Value_Relation:
		// 直接处理Relation对象（Service层传入的情况）
		return &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_Relation_{Relation: v},
		}
	case map[string]any:
		return d.convertMapValue(v)
	case time.Time:
		return &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_TimestampTime{
				TimestampTime: &timestamppb.Timestamp{
					Seconds: v.Unix(),
					Nanos:   int32(v.Nanosecond()),
				},
			},
		}
	}

	return nil
}

// convertMapValue 处理JSON反序列化后的map类型默认值
func (d *Definition) convertMapValue(v map[string]any) *customerpb.CustomField_Value {
	// 尝试解析Money类型
	if currencyCode, currencyOk := v["currency_code"].(string); currencyOk {
		units, unitsOk := v["units"].(float64)
		nanos, nanosOk := v["nanos"].(float64)

		if unitsOk && nanosOk {
			money := &money.Money{
				CurrencyCode: currencyCode,
				Units:        int64(units),
				Nanos:        int32(nanos),
			}
			return &customerpb.CustomField_Value{
				Value: &customerpb.CustomField_Value_Money{Money: money},
			}
		}
	}

	// 尝试解析Relation类型
	if entityStr, entityOk := v["entity"].(string); entityOk {
		id, idOk := v["id"].(float64)

		if idOk {
			var entityEnum customerpb.CustomField_Value_Relation_Entity
			switch entityStr {
			case customerpb.CustomField_Value_Relation_CUSTOMER.String():
				entityEnum = customerpb.CustomField_Value_Relation_CUSTOMER
			case customerpb.CustomField_Value_Relation_LEAD.String():
				entityEnum = customerpb.CustomField_Value_Relation_LEAD
			}

			if entityEnum != customerpb.CustomField_Value_Relation_ENTITY_UNSPECIFIED {
				relation := &customerpb.CustomField_Value_Relation{
					Entity: entityEnum,
					Id:     int64(id),
				}
				return &customerpb.CustomField_Value{
					Value: &customerpb.CustomField_Value_Relation_{Relation: relation},
				}
			}
		}
	}

	return nil
}

type DefinitionWithOptions struct {
	Definition *Definition
	Options    []*Option
}

func (dwo *DefinitionWithOptions) ToPB() *customerpb.CustomField {
	if dwo == nil || dwo.Definition == nil {
		return nil
	}

	customField := dwo.Definition.ToPB()
	if customField == nil {
		return nil
	}

	// 添加选项
	if len(dwo.Options) > 0 {
		options := make([]*customerpb.CustomField_Option, 0, len(dwo.Options))
		for _, opt := range dwo.Options {
			if pbOpt := opt.ToPB(); pbOpt != nil {
				options = append(options, pbOpt)
			}
		}
		customField.Options = options
	}

	return customField
}

// 查询参数结构体

type GetDefinitionParams struct {
	ID int64
}

type ListDefinitionsFilter struct {
	IDs              []int64
	OrganizationType customerpb.OrganizationRef_Type
	OrganizationID   int64
	AssociationType  customerpb.CustomField_AssociationType
	FieldNames       []string
	States           []customerpb.CustomField_State
}

type ListDefinitionsPagination struct {
	PageSize        int32
	Cursor          string
	ReturnTotalSize bool
}

type ListDefinitionsOrderBy struct {
	Field     customerpb.ListCustomFieldsRequest_Sorting_Field
	Direction customerpb.ListCustomFieldsRequest_Sorting_Direction
}

type ListDefinitionsParams struct {
	Filter     *ListDefinitionsFilter
	Pagination *ListDefinitionsPagination
	OrderBy    *ListDefinitionsOrderBy
}

type ListDefinitionsResponse struct {
	Definitions []*DefinitionWithOptions `json:"definitions"`
	HasNext     bool                     `json:"has_next"`
	NextToken   string                   `json:"next_token"`
	TotalSize   *int64                   `json:"total_size"`
}
