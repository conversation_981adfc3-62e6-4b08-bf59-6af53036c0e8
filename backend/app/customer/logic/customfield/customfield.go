package customfield

import (
	"context"
	"errors"

	"github.com/bytedance/sonic"
	"google.golang.org/grpc/codes"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	repocf "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customfield"
	"github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

// Logic 提供自定义字段的业务逻辑服务
// 只暴露 DefinitionWithOptions 相关接口
type Logic struct {
	customFieldRepo repocf.Repository
}

func New() *Logic {
	return &Logic{
		customFieldRepo: repocf.New(),
	}
}

func NewByParams(customFieldRepo repocf.Repository) *Logic {
	return &Logic{
		customFieldRepo: customFieldRepo,
	}
}

// Create 创建自定义字段及其选项
func (l *Logic) Create(ctx context.Context, dwo *DefinitionWithOptions) (*DefinitionWithOptions, error) {
	if dwo == nil {
		return nil, errs.Newm(codes.InvalidArgument, "definition with options is nil")
	}
	repoDwo, err := toRepo(dwo)
	if err != nil {
		return nil, err
	}
	created, err := l.customFieldRepo.Create(ctx, repoDwo)
	if err != nil {
		return nil, errs.New(customerpb.ErrCode_ERR_CODE_CREATE_CUSTOM_FIELD_FAILED)
	}
	return toLogic(created), nil
}

// Update 更新自定义字段及其选项
func (l *Logic) Update(ctx context.Context, dwo *DefinitionWithOptions) (*DefinitionWithOptions, error) {
	if dwo == nil {
		return nil, errs.Newm(codes.InvalidArgument, "definition with options is nil")
	}
	repoDwo, err := toRepo(dwo)
	if err != nil {
		return nil, err
	}
	updated, err := l.customFieldRepo.Update(ctx, repoDwo)
	if err != nil {
		return nil, err
	}
	return toLogic(updated), nil
}

// Get 查询单个自定义字段及其选项
func (l *Logic) Get(ctx context.Context, params *GetDefinitionParams) (*DefinitionWithOptions, error) {
	if params == nil {
		return nil, errs.Newm(codes.InvalidArgument, "get params is nil")
	}
	dwo, err := l.customFieldRepo.Get(ctx, params.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_CUSTOM_FIELD_NOT_FOUND)
		}
		return nil, err
	}
	// toLogic永远不会返回错误，所以移除错误处理
	return toLogic(dwo), nil
}

// List 查询自定义字段定义列表及其选项
func (l *Logic) List(ctx context.Context, params *ListDefinitionsParams) (*ListDefinitionsResponse, error) {
	if params == nil {
		return nil, errs.Newm(codes.InvalidArgument, "list params is nil")
	}
	var filter *repocf.DefinitionListFilter
	if params.Filter != nil {
		filter = &repocf.DefinitionListFilter{
			IDs:              params.Filter.IDs,
			OrganizationType: params.Filter.OrganizationType,
			OrganizationID:   params.Filter.OrganizationID,
			AssociationType:  params.Filter.AssociationType,
			FieldNames:       params.Filter.FieldNames,
			States:           params.Filter.States,
		}
	}
	var pagination *repocf.Pagination
	if params.Pagination != nil {
		pagination = &repocf.Pagination{
			PageSize:        params.Pagination.PageSize,
			Cursor:          utils.DecodeCursor(params.Pagination.Cursor),
			ReturnTotalSize: params.Pagination.ReturnTotalSize,
		}
	}
	var orderBy *repocf.DefinitionOrderBy
	if params.OrderBy != nil {
		orderBy = &repocf.DefinitionOrderBy{
			Field:     params.OrderBy.Field,
			Direction: params.OrderBy.Direction,
		}
	}

	dbResult, err := l.customFieldRepo.List(ctx, filter, pagination, orderBy)
	if err != nil {
		return nil, err
	}

	var result []*DefinitionWithOptions
	for _, d := range dbResult.Data {
		// toLogic永远不会返回错误，所以移除错误处理
		logicDef := toLogic(d)
		result = append(result, logicDef)
	}

	response := &ListDefinitionsResponse{
		Definitions: result,
		HasNext:     dbResult.HasNext,
		TotalSize:   dbResult.TotalCount,
	}

	// 生成下一页令牌
	if dbResult.HasNext && len(dbResult.Data) > 0 {
		lastDef := dbResult.Data[len(dbResult.Data)-1].Definition
		cursor := postgres.Cursor{
			ID:        lastDef.ID,
			CreatedAt: lastDef.CreatedTime,
		}
		response.NextToken = cursor.EncodeCursor()
	}

	return response, nil
}

// entity转换
func toRepo(dwo *DefinitionWithOptions) (*repocf.DefinitionWithOptions, error) {
	if dwo == nil {
		return nil, nil
	}
	repoDef, err := toRepoDef(dwo.Definition)
	if err != nil {
		return nil, err
	}
	var repoOpts []*repocf.Option
	for _, opt := range dwo.Options {
		repoOpt, err := toRepoOpt(opt)
		if err != nil {
			return nil, err
		}
		repoOpts = append(repoOpts, repoOpt)
	}
	return &repocf.DefinitionWithOptions{
		Definition: repoDef,
		Options:    repoOpts,
	}, nil
}

func toRepoDef(def *Definition) (*repocf.Definition, error) {
	if def == nil {
		return nil, nil
	}
	var defaultValue, validationRules []byte
	var err error
	if def.DefaultValue != nil {
		defaultValue, err = sonic.Marshal(def.DefaultValue)
		if err != nil {
			return nil, err
		}
	}
	if def.ValidationRules != nil {
		validationRules, err = sonic.Marshal(def.ValidationRules)
		if err != nil {
			return nil, err
		}
	}
	return &repocf.Definition{
		ID:               def.ID,
		OrganizationType: def.OrganizationType,
		OrganizationID:   def.OrganizationID,
		AssociationType:  def.AssociationType,
		FieldName:        def.FieldName,
		FieldLabel:       def.FieldLabel,
		FieldType:        def.FieldType,
		IsRequired:       def.IsRequired,
		DefaultValue:     defaultValue,
		ValidationRules:  validationRules,
		DisplayOrder:     def.DisplayOrder,
		HelpText:         def.HelpText,
		State:            def.State,
		DeletedTime:      def.DeletedTime,
		CreatedTime:      def.CreatedTime,
		UpdatedTime:      def.UpdatedTime,
	}, nil
}

func toLogic(dwo *repocf.DefinitionWithOptions) *DefinitionWithOptions {
	if dwo == nil {
		return nil
	}
	// toLogicDef永远不会返回错误，所以移除错误处理
	logicDef := toLogicDef(dwo.Definition)
	return &DefinitionWithOptions{
		Definition: logicDef,
		Options:    toLogicOptions(dwo.Options),
	}
}

func toLogicDef(def *repocf.Definition) *Definition {
	if def == nil {
		return nil
	}
	var defaultValue any
	var validationRules map[string]any
	if def.DefaultValue != nil {
		_ = sonic.Unmarshal(def.DefaultValue, &defaultValue)
	}
	if def.ValidationRules != nil {
		_ = sonic.Unmarshal(def.ValidationRules, &validationRules)
	}
	return &Definition{
		ID:               def.ID,
		OrganizationType: def.OrganizationType,
		OrganizationID:   def.OrganizationID,
		AssociationType:  def.AssociationType,
		FieldName:        def.FieldName,
		FieldLabel:       def.FieldLabel,
		FieldType:        def.FieldType,
		IsRequired:       def.IsRequired,
		DefaultValue:     defaultValue,
		ValidationRules:  validationRules,
		DisplayOrder:     def.DisplayOrder,
		HelpText:         def.HelpText,
		State:            def.State,
		DeletedTime:      def.DeletedTime,
		CreatedTime:      def.CreatedTime,
		UpdatedTime:      def.UpdatedTime,
	}
}

func toLogicOptions(opts []*repocf.Option) []*Option {
	var result []*Option
	for _, o := range opts {
		logicOpt := toLogicOpt(o)
		result = append(result, logicOpt)
	}
	return result
}

func toLogicOpt(opt *repocf.Option) *Option {
	if opt == nil {
		return nil
	}
	var valueMoney any
	var valueRelation any
	if opt.ValueMoney != nil {
		_ = sonic.Unmarshal(opt.ValueMoney, &valueMoney)
	}
	if opt.ValueRelation != nil {
		_ = sonic.Unmarshal(opt.ValueRelation, &valueRelation)
	}
	return &Option{
		ID:            opt.ID,
		FieldID:       opt.FieldID,
		ValueString:   opt.ValueString,
		ValueInt64:    opt.ValueInt64,
		ValueDouble:   opt.ValueDouble,
		ValueBool:     opt.ValueBool,
		ValueMoney:    valueMoney,
		ValueRelation: valueRelation,
		Label:         opt.Label,
		SortOrder:     opt.SortOrder,
		State:         opt.State,
		CreatedTime:   opt.CreatedTime,
		UpdatedTime:   opt.UpdatedTime,
		DeletedTime:   opt.DeletedTime,
	}
}

func toRepoOpt(opt *Option) (*repocf.Option, error) {
	if opt == nil {
		return nil, nil
	}
	var valueMoney, valueRelation []byte
	var err error
	if opt.ValueMoney != nil {
		valueMoney, err = sonic.Marshal(opt.ValueMoney)
		if err != nil {
			return nil, err
		}
	}
	if opt.ValueRelation != nil {
		valueRelation, err = sonic.Marshal(opt.ValueRelation)
		if err != nil {
			return nil, err
		}
	}
	return &repocf.Option{
		ID:            opt.ID,
		FieldID:       opt.FieldID,
		ValueString:   opt.ValueString,
		ValueInt64:    opt.ValueInt64,
		ValueDouble:   opt.ValueDouble,
		ValueBool:     opt.ValueBool,
		ValueMoney:    valueMoney,
		ValueRelation: valueRelation,
		Label:         opt.Label,
		SortOrder:     opt.SortOrder,
		State:         opt.State,
		CreatedTime:   opt.CreatedTime,
		UpdatedTime:   opt.UpdatedTime,
		DeletedTime:   opt.DeletedTime,
	}, nil
}
