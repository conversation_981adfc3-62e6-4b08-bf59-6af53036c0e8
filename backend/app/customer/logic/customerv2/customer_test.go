package customer

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"gorm.io/datatypes"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	customermock "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer/mock"
	"github.com/MoeGolibrary/moego/backend/app/customer/utils"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

func TestNew(t *testing.T) {
	t.Run("测试创建新的Logic实例", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo)
		require.NotNil(t, logic)
	})

	t.Run("New方法", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		postgres.SetDB(&gorm.DB{})
		logic := New()
		require.NotNil(t, logic)
	})
}

func TestCreateCustomer(t *testing.T) {
	t.Run("测试创建客户成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		logic := NewByParams(customerRepo)

		// 准备测试数据
		customer := &Customer{
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   123,
			GivenName:        "测试",
			FamilyName:       "用户",
			CustomerType:     customerpb.CustomerType_CUSTOMER,
			CustomFields:     datatypes.JSON(`{"key": "value"}`),
			LifeCycleID:      1,
			OwnerStaffID:     456,
		}

		// 设置mock期望
		mockCustomer := &customerrepo.Customer{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   123,
			GivenName:        "测试",
			FamilyName:       "用户",
			CustomerType:     customerpb.CustomerType_CUSTOMER,
			State:            customerpb.Customer_ACTIVE,
			CustomFields:     datatypes.JSON(`{"key": "value"}`),
			LifeCycleID:      1,
			OwnerStaffID:     456,
			CreatedTime:      time.Now(),
			UpdatedTime:      time.Now(),
		}
		customerRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(mockCustomer, nil)

		// 执行测试
		result, err := logic.CreateCustomer(context.Background(), customer)
		require.NoError(t, err, "创建客户时不应返回错误")
		require.NotNil(t, result, "返回的客户结果不应为nil")
		t.Logf("result: %+v", result)
		require.Equal(t, int64(1), result.ID, "客户ID应为1")
		require.Equal(t, customerpb.Customer_ACTIVE, result.State, "客户状态应为ACTIVE")
		require.Equal(t, customerpb.CustomerType_CUSTOMER, result.CustomerType, "客户类型应为CUSTOMER")
	})

	t.Run("测试创建客户失败-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		logic := NewByParams(customerRepo)

		// 准备测试数据
		customer := &Customer{
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   123,
			GivenName:        "测试",
			FamilyName:       "用户",
		}

		// 设置mock期望
		expectedErr := errors.New("数据库错误")
		customerRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		result, err := logic.CreateCustomer(context.Background(), customer)
		require.Error(t, err, "应返回错误")
		require.Nil(t, result, "返回结果应为nil")
		require.Contains(t, err.Error(), "119506", "error code is not 119506")
	})
}

func TestGetCustomer(t *testing.T) {
	t.Run("测试获取客户成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo)

		// 准备测试数据
		customerID := int64(1)

		// 设置mock期望
		expectedCustomer := &customerrepo.Customer{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   123,
			GivenName:        "测试",
			FamilyName:       "用户",
			CustomerType:     customerpb.CustomerType_CUSTOMER,
			State:            customerpb.Customer_ACTIVE,
			CustomFields:     datatypes.JSON(`{"key": "value"}`),
			LifeCycleID:      1,
			OwnerStaffID:     456,
			CreatedTime:      time.Now(),
			UpdatedTime:      time.Now(),
		}
		customerRepo.EXPECT().Get(gomock.Any(), customerID, customerpb.CustomerType_CUSTOMER).Return(expectedCustomer, nil)

		// 执行测试
		result, err := logic.GetCustomer(context.Background(), customerID)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, int64(1), result.ID)
		require.Equal(t, "测试", result.GivenName)
		require.Equal(t, "用户", result.FamilyName)
	})

	t.Run("测试获取客户失败-客户不存在", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo)

		// 准备测试数据
		customerID := int64(999)

		// 设置mock期望
		customerRepo.EXPECT().Get(gomock.Any(), customerID, customerpb.CustomerType_CUSTOMER).Return(nil, gorm.ErrRecordNotFound)

		// 执行测试
		result, err := logic.GetCustomer(context.Background(), customerID)
		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "119501", "error code is not 119501")
	})

	t.Run("测试获取客户失败-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo)

		// 准备测试数据
		customerID := int64(1)

		// 设置mock期望
		expectedErr := errors.New("数据库错误")
		customerRepo.EXPECT().Get(gomock.Any(), customerID, customerpb.CustomerType_CUSTOMER).Return(nil, expectedErr)

		// 执行测试
		result, err := logic.GetCustomer(context.Background(), customerID)
		require.Error(t, err)
		require.Nil(t, result)
		require.Equal(t, expectedErr, err)
	})
}

func TestListCustomers(t *testing.T) {
	t.Run("测试获取客户列表成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo)

		// 准备测试数据
		organizationType := customerpb.OrganizationRef_BUSINESS
		organizationID := int64(123)
		req := &ListCustomersRequest{
			Filter: &ListCustomersFilter{
				OrganizationType: &organizationType,
				OrganizationID:   organizationID,
				CustomerType:     customerpb.CustomerType_CUSTOMER,
				States:           []customerpb.Customer_State{customerpb.Customer_ACTIVE},
			},
			Pagination: &ListCustomersPagination{
				PageSize:        10,
				ReturnTotalSize: true,
			},
			OrderBy: &ListCustomersOrderBy{
				Field:     customerpb.ListCustomersRequest_Sorting_CREATED_TIME,
				Direction: customerpb.ListCustomersRequest_Sorting_DESC,
			},
		}

		// 设置mock期望
		expectedCustomers := []*customerrepo.Customer{
			{
				ID:               1,
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
				OrganizationID:   organizationID,
				GivenName:        "测试1",
				FamilyName:       "用户1",
				CustomerType:     customerpb.CustomerType_CUSTOMER,
				State:            customerpb.Customer_ACTIVE,
				CreatedTime:      time.Now(),
				UpdatedTime:      time.Now(),
			},
			{
				ID:               2,
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
				OrganizationID:   organizationID,
				GivenName:        "测试2",
				FamilyName:       "用户2",
				CustomerType:     customerpb.CustomerType_CUSTOMER,
				State:            customerpb.Customer_ACTIVE,
				CreatedTime:      time.Now().Add(-time.Hour),
				UpdatedTime:      time.Now().Add(-time.Hour),
			},
		}

		customerRepo.EXPECT().ListByCursor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&customerrepo.CursorResult{
			Data:       expectedCustomers,
			HasNext:    true,
			TotalCount: func() *int64 { count := int64(2); return &count }(),
		}, nil)

		// 执行测试
		result, err := logic.ListCustomers(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Len(t, result.Customers, 2)
		require.True(t, result.HasNext)
		require.NotEmpty(t, result.NextToken)
		require.Equal(t, int64(2), *result.TotalSize)
	})

	t.Run("测试获取客户列表-空结果", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo)

		// 准备测试数据
		organizationType := customerpb.OrganizationRef_BUSINESS
		organizationID := int64(123)
		req := &ListCustomersRequest{
			Filter: &ListCustomersFilter{
				OrganizationType: &organizationType,
				OrganizationID:   organizationID,
			},
			Pagination: &ListCustomersPagination{
				PageSize:        10,
				ReturnTotalSize: true,
			},
			OrderBy: &ListCustomersOrderBy{
				Field:     customerpb.ListCustomersRequest_Sorting_CREATED_TIME,
				Direction: customerpb.ListCustomersRequest_Sorting_DESC,
			},
		}

		// 设置mock期望
		customerRepo.EXPECT().ListByCursor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&customerrepo.CursorResult{
			Data:       []*customerrepo.Customer{},
			HasNext:    false,
			TotalCount: func() *int64 { count := int64(0); return &count }(),
		}, nil)

		// 执行测试
		result, err := logic.ListCustomers(context.Background(), req)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Empty(t, result.Customers)
		require.False(t, result.HasNext)
		require.Empty(t, result.NextToken)
		require.Equal(t, int64(0), *result.TotalSize)
	})

	t.Run("测试获取客户列表-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo)

		// 准备测试数据
		organizationType := customerpb.OrganizationRef_BUSINESS
		organizationID := int64(123)
		req := &ListCustomersRequest{
			Filter: &ListCustomersFilter{
				OrganizationType: &organizationType,
				OrganizationID:   organizationID,
			},
			Pagination: &ListCustomersPagination{
				PageSize: 10,
			},
		}

		// 设置mock期望
		expectedErr := errors.New("数据库错误")
		customerRepo.EXPECT().ListByCursor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		result, err := logic.ListCustomers(context.Background(), req)
		require.Error(t, err)
		require.Nil(t, result)
		require.Equal(t, expectedErr, err)
	})
}

func TestUpdateCustomer(t *testing.T) {
	t.Run("测试更新客户成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo)

		// 准备测试数据
		customerID := int64(1)
		updateReq := &UpdateCustomerRequest{
			GivenName:    "更新",
			FamilyName:   "姓名",
			CustomFields: datatypes.JSON(`{"updated": "value"}`),
			LifeCycleID:  2,
			OwnerStaffID: 789,
		}

		// 设置mock期望 - GetCustomer
		existingCustomer := &customerrepo.Customer{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   123,
			GivenName:        "原",
			FamilyName:       "姓名",
			CustomerType:     customerpb.CustomerType_CUSTOMER,
			State:            customerpb.Customer_ACTIVE,
			CustomFields:     datatypes.JSON(`{"key": "value"}`),
			LifeCycleID:      1,
			OwnerStaffID:     456,
			CreatedTime:      time.Now().Add(-24 * time.Hour),
			UpdatedTime:      time.Now().Add(-24 * time.Hour),
		}
		customerRepo.EXPECT().Get(gomock.Any(), customerID, customerpb.CustomerType_CUSTOMER).Return(existingCustomer, nil)

		// 设置mock期望 - Update
		updatedCustomer := &customerrepo.Customer{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   123,
			GivenName:        "更新",
			FamilyName:       "姓名",
			CustomerType:     customerpb.CustomerType_CUSTOMER,
			State:            customerpb.Customer_ACTIVE,
			CustomFields:     datatypes.JSON(`{"updated": "value"}`),
			LifeCycleID:      2,
			OwnerStaffID:     789,
			CreatedTime:      time.Now().Add(-24 * time.Hour),
			UpdatedTime:      time.Now(),
		}
		customerRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(updatedCustomer, nil)

		// 执行测试
		result, err := logic.UpdateCustomer(context.Background(), customerID, updateReq)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, int64(1), result.ID)
		require.Equal(t, "更新", result.GivenName)
		require.Equal(t, "姓名", result.FamilyName)
		require.Equal(t, int64(2), result.LifeCycleID)
		require.Equal(t, int64(789), result.OwnerStaffID)
	})

	t.Run("测试更新客户失败-客户不存在", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo)

		// 准备测试数据
		customerID := int64(999)
		updateReq := &UpdateCustomerRequest{
			GivenName:  "更新",
			FamilyName: "姓名",
		}

		// 设置mock期望
		customerRepo.EXPECT().Get(gomock.Any(), customerID, customerpb.CustomerType_CUSTOMER).Return(nil, gorm.ErrRecordNotFound)

		// 执行测试
		result, err := logic.UpdateCustomer(context.Background(), customerID, updateReq)
		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "119501", "error code is not 119501")
	})

	t.Run("测试更新客户失败-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo)

		// 准备测试数据
		customerID := int64(1)
		updateReq := &UpdateCustomerRequest{
			GivenName:  "更新",
			FamilyName: "姓名",
		}

		// 设置mock期望
		existingCustomer := &customerrepo.Customer{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   123,
			GivenName:        "原",
			FamilyName:       "姓名",
			CustomerType:     customerpb.CustomerType_CUSTOMER,
			State:            customerpb.Customer_ACTIVE,
			LifeCycleID:      1,
			OwnerStaffID:     456,
			CreatedTime:      time.Now().Add(-24 * time.Hour),
			UpdatedTime:      time.Now().Add(-24 * time.Hour),
		}
		customerRepo.EXPECT().Get(gomock.Any(), customerID, customerpb.CustomerType_CUSTOMER).Return(existingCustomer, nil)

		expectedErr := errors.New("数据库错误")
		customerRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		result, err := logic.UpdateCustomer(context.Background(), customerID, updateReq)
		require.Error(t, err)
		require.Nil(t, result)
		require.Equal(t, expectedErr, err)
	})
}

func TestDeleteCustomer(t *testing.T) {
	t.Run("测试删除客户成功-软删除", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo)

		// 准备测试数据
		customerID := int64(1)
		inactivate := true

		// 设置mock期望 - GetCustomer
		existingCustomer := &customerrepo.Customer{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   123,
			GivenName:        "测试",
			FamilyName:       "用户",
			CustomerType:     customerpb.CustomerType_CUSTOMER,
			State:            customerpb.Customer_ACTIVE,
			LifeCycleID:      1,
			OwnerStaffID:     456,
			CreatedTime:      time.Now().Add(-24 * time.Hour),
			UpdatedTime:      time.Now().Add(-24 * time.Hour),
		}
		customerRepo.EXPECT().Get(gomock.Any(), customerID, customerpb.CustomerType_CUSTOMER).Return(existingCustomer, nil)

		// 设置mock期望 - Update
		customerRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(&customerrepo.Customer{}, nil)

		// 执行测试
		err := logic.DeleteCustomer(context.Background(), customerID, inactivate)
		require.NoError(t, err)
	})

	t.Run("测试删除客户成功-硬删除", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo)

		// 准备测试数据
		customerID := int64(1)
		inactivate := false

		// 设置mock期望 - GetCustomer
		existingCustomer := &customerrepo.Customer{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   123,
			GivenName:        "测试",
			FamilyName:       "用户",
			CustomerType:     customerpb.CustomerType_CUSTOMER,
			State:            customerpb.Customer_ACTIVE,
			LifeCycleID:      1,
			OwnerStaffID:     456,
			CreatedTime:      time.Now().Add(-24 * time.Hour),
			UpdatedTime:      time.Now().Add(-24 * time.Hour),
		}
		customerRepo.EXPECT().Get(gomock.Any(), customerID, customerpb.CustomerType_CUSTOMER).Return(existingCustomer, nil)

		// 设置mock期望 - Update
		customerRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(&customerrepo.Customer{}, nil)

		// 执行测试
		err := logic.DeleteCustomer(context.Background(), customerID, inactivate)
		require.NoError(t, err)
	})

	t.Run("测试删除客户失败-客户不存在", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo)

		// 准备测试数据
		customerID := int64(999)
		inactivate := true

		// 设置mock期望
		customerRepo.EXPECT().Get(gomock.Any(), customerID, customerpb.CustomerType_CUSTOMER).Return(nil, gorm.ErrRecordNotFound)

		// 执行测试
		err := logic.DeleteCustomer(context.Background(), customerID, inactivate)
		require.Error(t, err)
		require.Contains(t, err.Error(), "119501", "error code is not 119501")
	})

	t.Run("测试删除客户失败-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo)

		// 准备测试数据
		customerID := int64(1)
		inactivate := true

		// 设置mock期望 - GetCustomer
		existingCustomer := &customerrepo.Customer{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   123,
			GivenName:        "测试",
			FamilyName:       "用户",
			CustomerType:     customerpb.CustomerType_CUSTOMER,
			State:            customerpb.Customer_ACTIVE,
			LifeCycleID:      1,
			OwnerStaffID:     456,
			CreatedTime:      time.Now().Add(-24 * time.Hour),
			UpdatedTime:      time.Now().Add(-24 * time.Hour),
		}
		customerRepo.EXPECT().Get(gomock.Any(), customerID, customerpb.CustomerType_CUSTOMER).Return(existingCustomer, nil)

		// 设置mock期望 - Update
		expectedErr := errors.New("数据库错误")
		customerRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		err := logic.DeleteCustomer(context.Background(), customerID, inactivate)
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
	})
}

func TestConvertToCustomer(t *testing.T) {
	t.Run("测试转换数据库客户到逻辑客户", func(t *testing.T) {
		// 准备测试数据
		dbCustomer := &customerrepo.Customer{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   123,
			GivenName:        "测试",
			FamilyName:       "用户",
			CustomerType:     customerpb.CustomerType_CUSTOMER,
			State:            customerpb.Customer_ACTIVE,
			CustomFields:     datatypes.JSON(`{"key": "value"}`),
			LifeCycleID:      1,
			OwnerStaffID:     456,
			DeletedTime:      nil,
			CreatedTime:      time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			UpdatedTime:      time.Date(2023, 1, 2, 0, 0, 0, 0, time.UTC),
		}

		// 执行测试
		result := convertToCustomer(dbCustomer)
		require.NotNil(t, result)
		require.Equal(t, int64(1), result.ID)
		require.Equal(t, customerpb.OrganizationRef_BUSINESS, result.OrganizationType)
		require.Equal(t, int64(123), result.OrganizationID)
		require.Equal(t, "测试", result.GivenName)
		require.Equal(t, "用户", result.FamilyName)
		require.Equal(t, customerpb.CustomerType_CUSTOMER, result.CustomerType)
		require.Equal(t, customerpb.Customer_ACTIVE, result.State)
		require.Equal(t, datatypes.JSON(`{"key": "value"}`), result.CustomFields)
		require.Equal(t, int64(1), result.LifeCycleID)
		require.Equal(t, int64(456), result.OwnerStaffID)
		require.Nil(t, result.DeletedTime)
		require.Equal(t, time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC), result.CreatedTime)
		require.Equal(t, time.Date(2023, 1, 2, 0, 0, 0, 0, time.UTC), result.UpdatedTime)
	})

	t.Run("测试转换数据库客户列表到逻辑客户列表", func(t *testing.T) {
		// 准备测试数据
		dbCustomers := []*customerrepo.Customer{
			{
				ID:               1,
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
				OrganizationID:   123,
				GivenName:        "测试1",
				FamilyName:       "用户1",
				CustomerType:     customerpb.CustomerType_CUSTOMER,
				State:            customerpb.Customer_ACTIVE,
				CreatedTime:      time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
				UpdatedTime:      time.Date(2023, 1, 2, 0, 0, 0, 0, time.UTC),
			},
			{
				ID:               2,
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
				OrganizationID:   123,
				GivenName:        "测试2",
				FamilyName:       "用户2",
				CustomerType:     customerpb.CustomerType_CUSTOMER,
				State:            customerpb.Customer_ACTIVE,
				CreatedTime:      time.Date(2023, 1, 3, 0, 0, 0, 0, time.UTC),
				UpdatedTime:      time.Date(2023, 1, 4, 0, 0, 0, 0, time.UTC),
			},
		}

		// 执行测试
		result := convertToCustomers(dbCustomers)
		require.NotNil(t, result)
		require.Len(t, result, 2)
		require.Equal(t, int64(1), result[0].ID)
		require.Equal(t, "测试1", result[0].GivenName)
		require.Equal(t, int64(2), result[1].ID)
		require.Equal(t, "测试2", result[1].GivenName)
	})
}

func TestCustomer_ToPB(t *testing.T) {
	t.Run("正常转换", func(t *testing.T) {
		now := time.Now().UTC()
		delTime := now.Add(24 * time.Hour)
		c := &Customer{
			ID:               123,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   456,
			GivenName:        "张三",
			FamilyName:       "李四",
			CustomerType:     customerpb.CustomerType_CUSTOMER,
			State:            customerpb.Customer_ACTIVE,
			CustomFields:     datatypes.JSON(`{"foo": "bar"}`),
			LifeCycleID:      1,
			OwnerStaffID:     2,
			DeletedTime:      &delTime,
			CreatedTime:      now,
			UpdatedTime:      now,
		}
		pb := c.ToPB()
		require.NotNil(t, pb)
		require.Equal(t, c.ID, pb.Id)
		require.Equal(t, c.OrganizationType, pb.Organization.Type)
		require.Equal(t, c.OrganizationID, pb.Organization.Id)
		require.Equal(t, c.GivenName, pb.GivenName)
		require.Equal(t, c.FamilyName, pb.FamilyName)
		require.Equal(t, c.State, pb.State)
		require.Equal(t, c.LifeCycleID, pb.LifecycleId)
		require.Equal(t, c.OwnerStaffID, pb.OwnerStaffId)
		require.NotNil(t, pb.CustomFields)
		require.Equal(t, "bar", pb.CustomFields.Fields["foo"].GetStringValue())
		require.NotNil(t, pb.DeleteTime)
		require.Equal(t, c.CreatedTime.Unix(), pb.CreateTime.Seconds)
		require.Equal(t, c.UpdatedTime.Unix(), pb.UpdateTime.Seconds)
		require.Equal(t, c.DeletedTime.Unix(), pb.DeleteTime.Seconds)
	})

	t.Run("CustomFields为空", func(t *testing.T) {
		c := &Customer{
			ID:           1,
			CustomFields: datatypes.JSON([]byte{}),
			CreatedTime:  time.Now().UTC(),
			UpdatedTime:  time.Now().UTC(),
		}
		pb := c.ToPB()
		require.NotNil(t, pb)
		require.NotNil(t, pb.CustomFields)
		require.Equal(t, 0, len(pb.CustomFields.Fields))
	})

	t.Run("CustomFields为非法JSON", func(t *testing.T) {
		c := &Customer{
			ID:           2,
			CustomFields: datatypes.JSON([]byte("{invalid json")),
			CreatedTime:  time.Now().UTC(),
			UpdatedTime:  time.Now().UTC(),
		}
		pb := c.ToPB()
		require.NotNil(t, pb)
		require.NotNil(t, pb.CustomFields)
		require.Equal(t, 0, len(pb.CustomFields.Fields))
	})

	t.Run("DeletedTime为nil", func(t *testing.T) {
		c := &Customer{
			ID:           3,
			CustomFields: datatypes.JSON(`{"a":1}`),
			CreatedTime:  time.Now().UTC(),
			UpdatedTime:  time.Now().UTC(),
			DeletedTime:  nil,
		}
		pb := c.ToPB()
		require.NotNil(t, pb)
		require.Nil(t, pb.DeleteTime)
	})

	t.Run("c为nil", func(t *testing.T) {
		var c *Customer
		pb := c.ToPB()
		require.Nil(t, pb)
	})
}

func TestListCustomersPagination_DecodeCursor(t *testing.T) {
	t.Run("空Cursor", func(t *testing.T) {
		p := &ListCustomersPagination{Cursor: ""}
		cursor := utils.DecodeCursor(p.Cursor)
		require.Nil(t, cursor)
	})

	t.Run("非法base64", func(t *testing.T) {
		p := &ListCustomersPagination{Cursor: "!!!notbase64"}
		cursor := utils.DecodeCursor(p.Cursor)
		require.Nil(t, cursor)
	})

	t.Run("合法base64但非法json", func(t *testing.T) {
		// base64编码的字符串，但不是合法json
		p := &ListCustomersPagination{Cursor: "aW52YWxpZCBqc29u"}
		cursor := utils.DecodeCursor(p.Cursor)
		require.NotNil(t, cursor) // 解析失败也会返回空结构体
	})

	t.Run("合法base64和合法json", func(t *testing.T) {
		type testCursor struct {
			ID int64 `json:"id"`
		}
		origin := testCursor{ID: 123}
		b, _ := json.Marshal(origin)
		encoded := base64.StdEncoding.EncodeToString(b)
		p := &ListCustomersPagination{Cursor: encoded}
		cursor := utils.DecodeCursor(p.Cursor)
		require.NotNil(t, cursor)
	})
}
