load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "history_log",
    srcs = [
        "entity.go",
        "history_log.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/history_log",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/db/customer_history_log",
        "//backend/app/customer/utils",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v1:customer",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "history_log_test",
    srcs = ["history_log_test.go"],
    embed = [":history_log"],
    deps = [
        "//backend/app/customer/repo/db",
        "//backend/app/customer/repo/db/customer_history_log",
        "//backend/app/customer/repo/db/customer_history_log/mock",
        "//backend/app/customer/utils",
        "//backend/proto/customer/v1:customer",
        "@com_github_stretchr_testify//assert",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_uber_go_mock//gomock",
    ],
)
