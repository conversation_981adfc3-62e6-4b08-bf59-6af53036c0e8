load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "growthbook",
    srcs = ["growth_book.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/growthbook",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/config/growthbook",
        "//backend/common/rpc/framework/log",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_growthbook_growthbook_golang//:growthbook-golang",
    ],
)
