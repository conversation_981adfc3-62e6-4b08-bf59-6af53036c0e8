// Code generated by MockGen. DO NOT EDIT.
// Source: ./postgres/customfield/repo.go
//
// Generated by this command:
//
//	mockgen -source=./postgres/customfield/repo.go -destination=./postgres/customfield/mock/repo_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	customfield "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customfield"
	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRepository) Create(ctx context.Context, dwo *customfield.DefinitionWithOptions) (*customfield.DefinitionWithOptions, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, dwo)
	ret0, _ := ret[0].(*customfield.DefinitionWithOptions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRepositoryMockRecorder) Create(ctx, dwo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRepository)(nil).Create), ctx, dwo)
}

// CreateOption mocks base method.
func (m *MockRepository) CreateOption(ctx context.Context, opt *customfield.Option) (*customfield.Option, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOption", ctx, opt)
	ret0, _ := ret[0].(*customfield.Option)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOption indicates an expected call of CreateOption.
func (mr *MockRepositoryMockRecorder) CreateOption(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOption", reflect.TypeOf((*MockRepository)(nil).CreateOption), ctx, opt)
}

// Get mocks base method.
func (m *MockRepository) Get(ctx context.Context, id int64) (*customfield.DefinitionWithOptions, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*customfield.DefinitionWithOptions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockRepositoryMockRecorder) Get(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRepository)(nil).Get), ctx, id)
}

// List mocks base method.
func (m *MockRepository) List(ctx context.Context, filter *customfield.DefinitionListFilter, pagination *customfield.Pagination, orderBy *customfield.DefinitionOrderBy) (*customfield.DefinitionCursorResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, filter, pagination, orderBy)
	ret0, _ := ret[0].(*customfield.DefinitionCursorResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockRepositoryMockRecorder) List(ctx, filter, pagination, orderBy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockRepository)(nil).List), ctx, filter, pagination, orderBy)
}

// Update mocks base method.
func (m *MockRepository) Update(ctx context.Context, dwo *customfield.DefinitionWithOptions) (*customfield.DefinitionWithOptions, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, dwo)
	ret0, _ := ret[0].(*customfield.DefinitionWithOptions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockRepositoryMockRecorder) Update(ctx, dwo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRepository)(nil).Update), ctx, dwo)
}

// UpdateOption mocks base method.
func (m *MockRepository) UpdateOption(ctx context.Context, opt *customfield.Option) (*customfield.Option, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOption", ctx, opt)
	ret0, _ := ret[0].(*customfield.Option)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateOption indicates an expected call of UpdateOption.
func (mr *MockRepositoryMockRecorder) UpdateOption(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOption", reflect.TypeOf((*MockRepository)(nil).UpdateOption), ctx, opt)
}
