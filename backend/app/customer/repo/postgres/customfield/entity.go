package customfield

import (
	"time"

	"gorm.io/datatypes"

	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Definition struct {
	ID              int64                        `gorm:"primaryKey;column:id"`
	EntityType      string                       `gorm:"column:entity_type"`
	FieldName       string                       `gorm:"column:field_name"`
	FieldLabel      string                       `gorm:"column:field_label"`
	FieldType       customerpb.CustomField_Type  `gorm:"column:field_type;serializer:proto_enum"`
	IsRequired      bool                         `gorm:"column:is_required"`
	DefaultValue    datatypes.JSON               `gorm:"column:default_value"`
	FieldOptions    datatypes.JSON               `gorm:"column:field_options;default:'[]'"`
	ValidationRules datatypes.JSON               `gorm:"column:validation_rules;default:'{}'"`
	DisplayOrder    int                          `gorm:"column:display_order"`
	HelpText        string                       `gorm:"column:help_text"`
	TenantID        int64                        `gorm:"column:tenant_id"`
	State           customerpb.CustomField_State `gorm:"column:state;serializer:proto_enum"`
	DeletedTime     *time.Time                   `gorm:"column:deleted_time"`
	CreatedTime     time.Time                    `gorm:"column:created_time;autoCreateTime"`
	UpdatedTime     time.Time                    `gorm:"column:updated_time;autoUpdateTime"`
}

func (Definition) TableName() string {
	return "custom_field_definition"
}

type Option struct {
	ID            int64                        `gorm:"primaryKey;column:id"`
	FieldID       int64                        `gorm:"column:field_id"`
	ValueString   string                       `gorm:"column:value_string"`
	ValueInt64    int64                        `gorm:"column:value_int64"`
	ValueDouble   float64                      `gorm:"column:value_double"`
	ValueBool     bool                         `gorm:"column:value_bool"`
	ValueMoney    datatypes.JSON               `gorm:"column:value_money"`
	ValueRelation datatypes.JSON               `gorm:"column:value_relation"`
	Label         string                       `gorm:"column:label"`
	SortOrder     int                          `gorm:"column:sort_order"`
	State         customerpb.CustomField_State `gorm:"column:state;serializer:proto_enum"`
	CreatedTime   time.Time                    `gorm:"column:created_time;autoCreateTime"`
	UpdatedTime   time.Time                    `gorm:"column:updated_time;autoUpdateTime"`
}

func (Option) TableName() string {
	return "custom_field_option"
}
