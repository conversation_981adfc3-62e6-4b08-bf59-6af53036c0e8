package customfield

import (
	"time"

	"gorm.io/datatypes"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Definition struct {
	ID               int64                                  `gorm:"primaryKey;column:id"`
	OrganizationType customerpb.OrganizationRef_Type        `gorm:"column:organization_type;serializer:proto_enum"`
	OrganizationID   int64                                  `gorm:"column:organization_id"`
	AssociationType  customerpb.CustomField_AssociationType `gorm:"column:association_type;serializer:proto_enum"`
	FieldName        string                                 `gorm:"column:field_name"`
	FieldLabel       string                                 `gorm:"column:field_label"`
	FieldType        customerpb.CustomField_Type            `gorm:"column:field_type;serializer:proto_enum"`
	IsRequired       bool                                   `gorm:"column:is_required"`
	DefaultValue     datatypes.JSON                         `gorm:"column:default_value"`
	ValidationRules  datatypes.JSON                         `gorm:"column:validation_rules;default:'{}'"`
	DisplayOrder     int                                    `gorm:"column:display_order"`
	HelpText         string                                 `gorm:"column:help_text"`
	State            customerpb.CustomField_State           `gorm:"column:state;serializer:proto_enum"`
	DeletedTime      *time.Time                             `gorm:"column:deleted_time"`
	CreatedTime      time.Time                              `gorm:"column:created_time;autoCreateTime"`
	UpdatedTime      time.Time                              `gorm:"column:updated_time;autoUpdateTime"`
}

func (Definition) TableName() string {
	return "custom_field_definition"
}

type Option struct {
	ID            int64                        `gorm:"primaryKey;column:id"`
	FieldID       int64                        `gorm:"column:field_id"`
	ValueString   string                       `gorm:"column:value_string"`
	ValueInt64    int64                        `gorm:"column:value_int64"`
	ValueDouble   float64                      `gorm:"column:value_double"`
	ValueBool     bool                         `gorm:"column:value_bool"`
	ValueMoney    datatypes.JSON               `gorm:"column:value_money"`
	ValueRelation datatypes.JSON               `gorm:"column:value_relation"`
	Label         string                       `gorm:"column:label"`
	SortOrder     int                          `gorm:"column:sort_order"`
	State         customerpb.CustomField_State `gorm:"column:state;serializer:proto_enum"`
	CreatedTime   time.Time                    `gorm:"column:created_time;autoCreateTime"`
	UpdatedTime   time.Time                    `gorm:"column:updated_time;autoUpdateTime"`
	DeletedTime   *time.Time                   `gorm:"column:deleted_time"`
}

func (Option) TableName() string {
	return "custom_field_option"
}

// 定义与选项的组合体
type DefinitionWithOptions struct {
	Definition *Definition
	Options    []*Option
}

// 过滤、分页、排序结构体
type DefinitionListFilter struct {
	IDs              []int64
	OrganizationType customerpb.OrganizationRef_Type
	OrganizationID   int64
	AssociationType  customerpb.CustomField_AssociationType
	FieldNames       []string
	States           []customerpb.CustomField_State
}

type OptionListFilter struct {
	IDs     []int64
	FieldID int64
	States  []customerpb.CustomField_State
	Labels  []string
}

type Pagination struct {
	PageSize        int32
	Cursor          *postgres.Cursor
	ReturnTotalSize bool
}

type DefinitionOrderBy struct {
	Field     customerpb.ListCustomFieldsRequest_Sorting_Field
	Direction customerpb.ListCustomFieldsRequest_Sorting_Direction
}

type OptionOrderBy struct {
	Field     string
	Direction string // "ASC" or "DESC"
}

type DefinitionCursorResult struct {
	Data       []*DefinitionWithOptions
	HasNext    bool
	TotalCount *int64
}

type OptionCursorResult struct {
	Data       []*Option
	HasNext    bool
	TotalCount *int64
}
