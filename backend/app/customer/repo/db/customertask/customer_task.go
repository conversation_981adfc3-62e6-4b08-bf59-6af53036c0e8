package customertask

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	Create(context context.Context, task *CustomerTask) error
	Update(context context.Context, task *CustomerTask) error
	List(context context.Context, datum *ListTasksDatum) ([]*CustomerTask, error)
	Delete(context context.Context, id int64, staffID int64) error
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	return &impl{
		db: db.GetDB(),
	}
}

func (i *impl) Create(ctx context.Context, task *CustomerTask) error {
	if err := i.db.WithContext(ctx).Create(task).Error; err != nil {
		log.ErrorContextf(ctx, "Create CustomerTask err, err:%+v", err)
		return err
	}
	return nil
}

func (i *impl) Update(ctx context.Context, task *CustomerTask) error {
	if err := i.db.WithContext(ctx).Updates(task).Error; err != nil {
		log.ErrorContextf(ctx, "Update CustomerTask err, err:%+v", err)
		return err
	}
	return nil
}

type ListTasksDatum struct {
	CustomerID int64
}

func (i *impl) List(ctx context.Context, datum *ListTasksDatum) ([]*CustomerTask, error) {
	query := i.db.WithContext(ctx).Table("moe_customer_task").Where("delete_time IS NULL")

	// filter
	if datum.CustomerID != 0 {
		query = query.Where("customer_id = ?", datum.CustomerID)
	}

	var res []*CustomerTask
	if err := query.
		Order("update_time desc").
		Find(&res).Error; err != nil {
		log.ErrorContextf(ctx, "List CustomerTask err, err:%+v", err)
		return nil, err
	}

	return res, nil
}

func (i *impl) Delete(ctx context.Context, id int64, staffID int64) error {
	if err := i.db.WithContext(ctx).Table("moe_customer_task").
		Where("id = ?", id).
		Update("delete_by", staffID).
		Update("delete_time", time.Now()).
		Error; err != nil {
		log.ErrorContextf(ctx, "Delete CustomerTask err, id:%d, staffID:%d", id, staffID)
		return err
	}
	return nil
}
