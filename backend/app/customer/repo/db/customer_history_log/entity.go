package customerhistorylog

import (
	"time"

	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

// unit test
// mockgen -destination=./mock/gorm_mock.go -package=mock -source=gorm.go

// CustomerHistoryLog 商家客户历史记录表
type CustomerHistoryLog struct {
	ID                  int64                         `gorm:"column:id;primary_key"`
	CompanyID           int64                         `gorm:"column:company_id"`
	BusinessID          int64                         `gorm:"column:business_id"`
	CustomerID          int64                         `gorm:"column:customer_id"`
	CustomerName        string                        `gorm:"column:customer_name"`
	CustomerPhoneNumber string                        `gorm:"column:customer_phone_number"`
	StaffID             int64                         `gorm:"column:staff_id"`
	Type                customerpb.HistoryLog_Type    `gorm:"column:type;serializer:proto_enum"`
	Action              *customerpb.HistoryLog_Action `gorm:"column:action;serializer:proto_json"`
	Source              customerpb.HistoryLog_Source  `gorm:"column:source;serializer:proto_enum"`
	SourceID            *int64                        `gorm:"column:source_id"`
	SourceName          string                        `gorm:"column:source_name"`
	CreateTime          time.Time                     `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL'"`
	UpdateTime          time.Time                     `gorm:"column:update_time;default:CURRENT_TIMESTAMP;NOT NULL"`
}

// TableName 表名
func (m *CustomerHistoryLog) TableName() string {
	return "moe_customer_history_log"
}

type ListHistoryLogsDatum struct {
	// filter field
	CustomerID     *int64
	HistoryLogType *customerpb.HistoryLog_Type
	CompanyID      *int64

	// page
	PageSize int
	PageNum  int
}
