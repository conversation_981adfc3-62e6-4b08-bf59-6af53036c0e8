load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "customer",
    srcs = [
        "customer.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/db",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v1:customer",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_uber_go_zap//:zap",
    ],
)
