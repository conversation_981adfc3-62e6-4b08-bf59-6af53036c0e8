// Code generated by MockGen. DO NOT EDIT.
// Source: ./pet/pet.go
//
// Generated by this command:
//
//	mockgen -source=./pet/pet.go -destination=./pet/mock/pet_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// ListCustomerPet mocks base method.
func (m *MockReadWriter) ListCustomerPet(ctx context.Context, customerID int64) ([]*petpb.Pet, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCustomerPet", ctx, customerID)
	ret0, _ := ret[0].([]*petpb.Pet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCustomerPet indicates an expected call of ListCustomerPet.
func (mr *MockReadWriterMockRecorder) ListCustomerPet(ctx, customerID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCustomerPet", reflect.TypeOf((*MockReadWriter)(nil).ListCustomerPet), ctx, customerID)
}
