package pet

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
)

type ReadWriter interface {
	ListCustomerPet(ctx context.Context, customerID int64) ([]*petpb.Pet, error)
}

type impl struct {
	pet petpb.PetServiceClient
}

func New() ReadWriter {
	return &impl{
		pet: grpc.NewClient("moego-pet", petpb.NewPetServiceClient),
	}
}

func (i *impl) ListCustomerPet(ctx context.Context, customerID int64) ([]*petpb.Pet, error) {
	pets, err := i.pet.ListPet(ctx, &petpb.ListPetRequest{
		Parent:   customerID,
		PageSize: 100,
	})
	if err != nil {
		log.ErrorContextf(ctx, "ListCustomerPet err:%+v, customerID:%d", err, customerID)
		return nil, err
	}
	return pets.GetPets(), nil
}
