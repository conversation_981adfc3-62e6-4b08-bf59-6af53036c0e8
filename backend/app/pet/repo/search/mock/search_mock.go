// Code generated by MockGen. DO NOT EDIT.
// Source: ./search.go
//
// Generated by this command:
//
//	mockgen -destination=./mock/search_mock.go -package=mock -source=./search.go
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// IndexPetDocument mocks base method.
func (m *MockReadWriter) IndexPetDocument(ctx context.Context, request *searchpb.BulkDocumentRequest) (*searchpb.BulkDocumentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IndexPetDocument", ctx, request)
	ret0, _ := ret[0].(*searchpb.BulkDocumentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IndexPetDocument indicates an expected call of IndexPetDocument.
func (mr *MockReadWriterMockRecorder) IndexPetDocument(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IndexPetDocument", reflect.TypeOf((*MockReadWriter)(nil).IndexPetDocument), ctx, request)
}

// SearchDocument mocks base method.
func (m *MockReadWriter) SearchDocument(ctx context.Context, request *searchpb.SearchDocumentRequest) (*searchpb.SearchDocumentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchDocument", ctx, request)
	ret0, _ := ret[0].(*searchpb.SearchDocumentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchDocument indicates an expected call of SearchDocument.
func (mr *MockReadWriterMockRecorder) SearchDocument(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchDocument", reflect.TypeOf((*MockReadWriter)(nil).SearchDocument), ctx, request)
}
