package customer

import (
	"context"
	"time"

	"gorm.io/gorm"

	igorm "github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm"
)

// BusinessCustomer 商家客户表结构
type BusinessCustomer struct {
	ID                     uint       `gorm:"column:id;primaryKey;autoIncrement"`
	BusinessID             int        `gorm:"column:business_id"`
	AvatarPath             string     `gorm:"column:avatar_path"`
	Email                  string     `gorm:"column:email"`
	FirstName              string     `gorm:"column:first_name"`
	LastName               string     `gorm:"column:last_name"`
	Status                 int8       `gorm:"column:status"`
	Inactive               int8       `gorm:"column:inactive"`
	ClientColor            string     `gorm:"column:client_color"`
	IsBlockMessage         int8       `gorm:"column:is_block_message"`
	IsBlockOnlineBooking   int8       `gorm:"column:is_block_online_booking"`
	LoginEmail             string     `gorm:"column:login_email"`
	ReferralSourceID       int        `gorm:"column:referral_source_id"`
	ReferralSourceDesc     string     `gorm:"column:referral_source_desc"`
	SendAutoEmail          int8       `gorm:"column:send_auto_email"`
	SendAutoMessage        int8       `gorm:"column:send_auto_message"`
	SendAppAutoMessage     int8       `gorm:"column:send_app_auto_message"`
	UnconfirmedReminderBy  int8       `gorm:"column:unconfirmed_reminder_by"`
	PreferredGroomerID     int        `gorm:"column:preferred_groomer_id"`
	PreferredFrequencyDay  int        `gorm:"column:preferred_frequency_day"`
	PreferredFrequencyType int8       `gorm:"column:preferred_frequency_type"`
	LastServiceTime        string     `gorm:"column:last_service_time"`
	Source                 string     `gorm:"column:source"`
	ExternalID             string     `gorm:"column:external_id"`
	CreateTime             int64      `gorm:"column:create_time"`
	UpdateTime             int64      `gorm:"column:update_time"`
	CreateBy               uint       `gorm:"column:create_by"`
	UpdateBy               uint       `gorm:"column:update_by"`
	IsRecurring            *int8      `gorm:"column:is_recurring"`
	ShareApptStatus        int8       `gorm:"column:share_appt_status"`
	ShareRangeType         int8       `gorm:"column:share_range_type"`
	ShareRangeValue        int        `gorm:"column:share_range_value"`
	ShareApptJSON          string     `gorm:"column:share_appt_json"`
	PreferredDay           string     `gorm:"column:preferred_day"`
	PreferredTime          string     `gorm:"column:preferred_time"`
	AccountID              int64      `gorm:"column:account_id"`
	CustomerCode           string     `gorm:"column:customer_code;uniqueIndex:uk_customer_code"`
	IsUnsubscribed         int8       `gorm:"column:is_unsubscribed"`
	CompanyID              int64      `gorm:"column:company_id"`
	Birthday               *time.Time `gorm:"column:birthday"`
}

// TableName 设置表名
func (b *BusinessCustomer) TableName() string {
	return "moe_business_customer"
}

type ListParams struct {
	CompanyIDs  []int64
	CustomerIDs []int64
	Limit       int32
	Offset      int32
	Sort        string
}

type ReadWriter interface {
	List(ctx context.Context, params *ListParams) ([]*BusinessCustomer, error)
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	return &impl{
		db: igorm.Get(),
	}
}

func (i *impl) List(ctx context.Context, params *ListParams) ([]*BusinessCustomer, error) {
	var customers []*BusinessCustomer
	query := i.db.WithContext(ctx).Model(&BusinessCustomer{})

	if len(params.CompanyIDs) > 0 {
		query = query.Where("company_id IN ?", params.CompanyIDs)
	}

	if len(params.CustomerIDs) > 0 {
		query = query.Where("id IN ?", params.CustomerIDs)
	}

	if params.Limit > 0 {
		query = query.Limit(int(params.Limit))
	}

	if params.Offset > 0 {
		query = query.Offset(int(params.Offset))
	}

	if params.Sort != "" {
		query = query.Order(params.Sort)
	}

	if err := query.Find(&customers).Error; err != nil {
		return nil, err
	}
	return customers, nil
}
