package pet

import (
	"context"

	"go.uber.org/zap"
	"gorm.io/gorm"

	igorm "github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/utils/pagination"
)

// CustomerPet 对应数据库表 moe_customer_pet
type CustomerPet struct {
	ID                    uint   `gorm:"column:id;primaryKey;autoIncrement"`
	BusinessID            int    `gorm:"column:business_id"`
	CustomerID            int    `gorm:"column:customer_id"`
	PetName               string `gorm:"column:pet_name;type:varchar(50)"`
	PetTypeID             uint   `gorm:"column:pet_type_id"`
	AvatarPath            string `gorm:"column:avatar_path;type:varchar(255)"`
	Breed                 string `gorm:"column:breed;type:varchar(50)"`
	BreedMix              int8   `gorm:"column:breed_mix"`
	Birthday              string `gorm:"column:birthday;type:varchar(50)"`
	Gender                int8   `gorm:"column:gender"`
	HairLength            string `gorm:"column:hair_length;type:varchar(50)"`
	Behavior              string `gorm:"column:behavior;type:varchar(50)"`
	Weight                string `gorm:"column:weight;type:varchar(50)"`
	Fixed                 string `gorm:"column:fixed;type:varchar(50)"`
	Status                int8   `gorm:"column:status"`
	LifeStatus            int8   `gorm:"column:life_status"`
	CreateTime            uint64 `gorm:"column:create_time"`
	UpdateTime            uint64 `gorm:"column:update_time"`
	ExpiryNotification    int8   `gorm:"column:expiry_notification"`
	LastVisit             string `gorm:"column:last_visit;type:varchar(20)"`
	LastAddonsIDs         string `gorm:"column:last_addons_ids;type:varchar(500)"`
	LastServiceIDs        string `gorm:"column:last_service_ids;type:varchar(500)"`
	VetName               string `gorm:"column:vet_name;type:varchar(100)"`
	VetPhone              string `gorm:"column:vet_phone;type:varchar(30)"`
	VetAddress            string `gorm:"column:vet_address;type:varchar(100)"`
	EmergencyContactName  string `gorm:"column:emergency_contact_name;type:varchar(100)"`
	EmergencyContactPhone string `gorm:"column:emergency_contact_phone;type:varchar(60)"`
	HealthIssues          string `gorm:"column:health_issues;type:varchar(3000)"`
	PetID                 int64  `gorm:"column:pet_id"`
	CompanyID             int64  `gorm:"column:company_id"`
	EvaluationStatus      int8   `gorm:"column:evaluation_status"`
	PetAppearanceNotes    string `gorm:"column:pet_appearance_notes;type:varchar(512)"`
	PetAppearanceColor    string `gorm:"column:pet_appearance_color;type:varchar(128)"`
}

// TableName 设置表名
func (CustomerPet) TableName() string {
	return "moe_customer_pet"
}

type ReadWriter interface {
	List(ctx context.Context, query *Query, filter *Filter, pagination *pagination.Pagination) ([]*CustomerPet, error)
	Create(ctx context.Context, pet *CustomerPet) (int64, error)
	BatchCreate(ctx context.Context, pets []*CustomerPet) ([]int64, error)
	Update(ctx context.Context, pet *CustomerPet) (int64, error)
	Get(ctx context.Context, petID int64) (*CustomerPet, error)
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	return &impl{
		db: igorm.Get(),
	}
}

type Query struct {
	CompanyIDs  []int64
	CustomerIDs []int64
	PetIDs      []int64
}

type Filter struct {
	Status int8
}

func (i *impl) List(ctx context.Context,
	query *Query, filter *Filter, pagination *pagination.Pagination) ([]*CustomerPet, error) {
	var pets []*CustomerPet
	db := i.db.WithContext(ctx).Model(&CustomerPet{})

	if query != nil {
		if len(query.CompanyIDs) > 0 {
			db = db.Where("company_id IN ?", query.CompanyIDs)
		}

		if len(query.CustomerIDs) > 0 {
			db = db.Where("customer_id IN ?", query.CustomerIDs)
		}

		if len(query.PetIDs) > 0 {
			db = db.Where("pet_id IN ?", query.PetIDs)
		}
	}

	if filter != nil {
		if filter.Status != 0 {
			db = db.Where("status = ?", filter.Status)
		}
	}

	if pagination != nil {
		if pagination.Limit > 0 {
			db = db.Limit(pagination.Limit)
		} else {
			db = db.Limit(10)
		}

		if pagination.Offset >= 0 {
			db = db.Offset(pagination.Offset)
		}

		if pagination.OrderBy != nil && *pagination.OrderBy != "" {
			db = db.Order(*pagination.OrderBy)
		}
	}

	if err := db.Find(&pets).Error; err != nil {
		return nil, err
	}

	return pets, nil
}

func (i *impl) Get(ctx context.Context, petID int64) (*CustomerPet, error) {
	pet := &CustomerPet{}
	if err := i.db.WithContext(ctx).Where("id = ?", petID).First(pet).Error; err != nil {
		log.ErrorContextf(ctx, "Get pet err, petID:%d, err:%+v", petID, err)
		return nil, err
	}
	return pet, nil
}

func (i *impl) Create(ctx context.Context, pet *CustomerPet) (int64, error) {
	if err := i.db.WithContext(ctx).Create(pet).Error; err != nil {
		log.ErrorContext(ctx, "Create pet failed", zap.Error(err))
		return 0, err
	}
	return int64(pet.ID), nil
}

func (i *impl) BatchCreate(ctx context.Context, pets []*CustomerPet) ([]int64, error) {
	if err := i.db.WithContext(ctx).Create(pets).Error; err != nil {
		log.ErrorContext(ctx, "BatchCreate pet failed", zap.Error(err))
		return nil, err
	}
	ids := make([]int64, len(pets))
	for i, pet := range pets {
		ids[i] = int64(pet.ID)
	}
	return ids, nil
}

func (i *impl) Update(ctx context.Context, pet *CustomerPet) (int64, error) {
	if err := i.db.WithContext(ctx).Model(&CustomerPet{}).Where("id = ?", pet.ID).Updates(pet).Error; err != nil {
		log.ErrorContext(ctx, "Update pet failed", zap.Error(err))
		return 0, err
	}
	return int64(pet.ID), nil
}
