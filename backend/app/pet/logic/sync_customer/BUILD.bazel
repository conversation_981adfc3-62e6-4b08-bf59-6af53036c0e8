load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "sync_customer",
    srcs = ["sync_customer.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/pet/logic/sync_customer",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/pet/repo/customer",
        "//backend/app/pet/repo/gorm/pet",
        "//backend/common/rpc/framework/log",
    ],
)
