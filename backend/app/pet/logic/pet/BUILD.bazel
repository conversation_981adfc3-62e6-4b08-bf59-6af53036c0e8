load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "pet",
    srcs = [
        "entity.go",
        "pet.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/pet/logic/pet",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/pet/repo/gorm/customer",
        "//backend/app/pet/repo/gorm/pet",
        "//backend/common/utils/pagination",
        "//backend/proto/pet/v1:pet",
    ],
)

go_test(
    name = "pet_test",
    srcs = ["pet_test.go"],
    deps = [
        ":pet",
        "//backend/app/pet/repo/gorm",
        "//backend/app/pet/repo/gorm/customer",
        "//backend/app/pet/repo/gorm/mock/customer",
        "//backend/app/pet/repo/gorm/mock/pet",
        "//backend/app/pet/repo/gorm/pet",
        "//backend/common/utils/pagination",
        "//backend/proto/pet/v1:pet",
        "@com_github_stretchr_testify//require",
        "@io_gorm_gorm//:gorm",
        "@org_uber_go_mock//gomock",
    ],
)
