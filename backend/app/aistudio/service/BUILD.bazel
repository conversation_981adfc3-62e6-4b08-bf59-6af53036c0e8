load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "service",
    srcs = [
        "aistudio_create_task.go",
        "aistudio_create_task_log.go",
        "aistudio_create_template.go",
        "aistudio_delete_task.go",
        "aistudio_delete_template.go",
        "aistudio_get_task.go",
        "aistudio_get_task_log.go",
        "aistudio_get_template.go",
        "aistudio_list_task.go",
        "aistudio_list_task_log.go",
        "aistudio_list_tempate_id.go",
        "aistudio_list_template.go",
        "aistudio_request_gemini.go",
        "aistudio_run_task.go",
        "aistudio_slack.go",
        "aistudio_update_task.go",
        "aistudio_update_template.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/aistudio/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/aistudio/logic/aistudio",
        "//backend/app/aistudio/repo/aistudio",
        "//backend/app/aistudio/repo/aistudio/entity",
        "//backend/common/rpc/framework/log",
        "//backend/proto/aistudio/v1:aistudio",
        "@com_github_samber_lo//:lo",
        "@com_github_slack_go_slack//:slack",
        "@org_golang_google_protobuf//types/known/emptypb",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "service_test",
    srcs = [
        "aistudio_create_task_test.go",
        "aistudio_request_gemini_test.go",
        "aistudio_run_task_test.go",
        "aistudio_slack_test.go",
    ],
    embed = [":service"],
    deps = [
        "//backend/app/aistudio/logic/aistudio",
        "//backend/proto/aistudio/v1:aistudio",
        "@com_github_stretchr_testify//assert",
    ],
)
