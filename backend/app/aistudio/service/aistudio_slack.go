package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/slack-go/slack"
	"google.golang.org/protobuf/types/known/structpb"

	aistudiologic "github.com/MoeGolibrary/moego/backend/app/aistudio/logic/aistudio"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

// HandleUrlVerification 处理 Slack 的 URL 验证请求
func (s *AiStudioService) HandleURLVerification(
	ctx context.Context, req *toolspb.HandleURLVerificationRequest) (*toolspb.HandleURLVerificationResponse, error) {
	log.InfoContextf(ctx,
		"Received URL Verification request: Type=%s, Token=%s, Challenge=%s",
		req.GetType(), req.GetToken(), req.GetChallenge())

	// 检查类型是否正确 (可选)
	if req.GetType() != "url_verification" {
		log.InfoContextf(ctx, "Warning: Received unexpected type for URL verification: %s", req.GetType())
		// 根据你的策略，可以返回错误，或者仍然尝试返回 challenge
	}

	// 直接从请求中获取 challenge 并包含在响应中
	response := &toolspb.HandleURLVerificationResponse{
		Challenge: req.GetChallenge(),
	}

	log.InfoContextf(ctx, "Responding to challenge with: %s", response.GetChallenge())

	// --- 重要提示 ---
	// 你的 HTTP 转码器 (grpc-gateway / Envoy) 需要配置成:
	// 1. 接收到这个 gRPC 响应后。
	// 2. 提取 response.Challenge 字段的值。
	// 3. 将这个值作为 HTTP 响应体 (Content-Type: text/plain) 直接返回给 Slack。
	// gRPC 方法本身只是按 Protobuf 结构返回数据。
	return response, nil
}

// HandleEventCallback 处理 Slack 的事件回调请求
func (s *AiStudioService) HandleEventCallback(
	ctx context.Context, req *toolspb.HandleEventCallbackRequest) (*toolspb.HandleEventCallbackResponse, error) {
	log.InfoContextf(ctx, "Received Event Callback: Type=%s, EventID=%s, EventTime=%d, TeamID=%s",
		req.GetType(), req.GetEventId(), req.GetEventTime(), req.GetTeamId())

	// --- 重要：立即返回响应 ---
	// Slack 要求在 3 秒内收到 HTTP 200 OK 确认。
	// gRPC 返回 Empty 和 nil 错误，转码器应将其映射为 HTTP 200 OK。
	// 实际的事件处理应该异步进行。
	go s.processEventAsync(req) // <--- 启动 Goroutine 处理事件

	response := &toolspb.HandleEventCallbackResponse{
		Challenge: req.GetChallenge(),
	}
	return response, nil
}

// processEventAsync 异步处理具体的事件逻辑
func (s *AiStudioService) processEventAsync(req *toolspb.HandleEventCallbackRequest) {
	// 在新的 Goroutine 中运行，避免阻塞 HandleEventCallback 的快速返回

	eventStruct := req.GetEvent()
	if eventStruct == nil || eventStruct.Fields == nil {
		log.Infof("Event struct is nil or empty in Event Callback.")
		return
	}

	// 打印收到的 Event Struct (用于调试)
	eventJSON, _ := json.MarshalIndent(eventStruct, "", "  ")
	log.Infof("Processing Event Struct:%s", string(eventJSON))

	// 提取事件类型
	eventTypeField, ok := eventStruct.Fields["type"]
	if !ok {
		log.Info("Field 'type' not found in event struct.")
		return
	}
	eventType := eventTypeField.GetStringValue()
	log.Infof("Detected Event Type: %s", eventType)

	// 根据事件类型进行分发处理
	switch eventType {
	case "app_mention":
		s.handleAppMention(eventStruct)
	// case "message":
	// 	s.handleMessage(eventStruct) // 处理其他事件类型...
	// case "reaction_added":
	//  s.handleReaction(eventStruct)
	default:
		log.Infof("Received unhandled event type: %s", eventType)
	}
}

// handleAppMention 处理 app_mention 事件
func (s *AiStudioService) handleAppMention(eventStruct *structpb.Struct) {
	// 从 Struct 中提取 app_mention 事件的关键字段
	// 注意：需要进行类型断言和错误检查
	channelField, cok := eventStruct.Fields["channel"]
	textField, tok := eventStruct.Fields["text"]
	userField, uok := eventStruct.Fields["user"]
	tsField, tsok := eventStruct.Fields["ts"] // 消息时间戳

	if !cok || !tok || !uok || !tsok {
		log.Info("Missing required fields (channel, text, user, ts) in app_mention event struct.")
		return
	}

	channelID := channelField.GetStringValue()
	text := textField.GetStringValue()
	userID := userField.GetStringValue()
	messageTs := tsField.GetStringValue() // 获取消息的时间戳，可用于回复线程

	log.Infof("Handling app_mention: User '%s' in Channel '%s' at TS '%s' said: '%s'",
		userID, channelID, messageTs, text)

	// 发送回复消息
	s.sendSlaceMsg(channelID, messageTs, "收到！开始运行...")

	// 在这里解析 text 内容，执行你的业务逻辑...
	// ... business logic based on text ...
	reply, err := RunAiStudioTaskBySlack(aistudiologic.NewTask(), text)
	if err != nil {
		s.sendSlaceMsg(channelID, messageTs, fmt.Sprintf("运行失败：%s", err.Error()))
		return
	}
	s.sendSlaceMsg(channelID, messageTs, reply)
}

func (s *AiStudioService) sendSlaceMsg(channelID, messageTs, responseText string) {
	// 使用 Slack API Client 发送消息
	// 注意：api.PostMessageContext 可以在需要时传递 context
	_, timestamp, err := s.slackClient.PostMessage(
		channelID,
		slack.MsgOptionText(responseText, false), // false 表示不转义特殊字符，如果需要 markdown 请用 true
		// 如果想在原消息下回复（创建或加入线程），添加这个选项:
		slack.MsgOptionTS(messageTs),
	)
	if err != nil {
		log.Infof("Error posting reply message to channel %s: %v", channelID, err)
		return
	}
	log.Infof("Reply message successfully sent to channel %s at timestamp %s", channelID, timestamp)
}

func RunAiStudioTaskBySlack(taskRunner aistudiologic.TaskRunner, text string) (reply string, err error) {
	// text: @bot run taskName key1=val1 key2=val2
	// text: @bot taskName val1 val2
	text = strings.Trim(text, " \n\r\t")
	textSplit := strings.Split(text, " ")
	command := safeGetSlice(textSplit, 1)
	userInputEnvs := map[string]string{}

	switch command {
	case "run":
		taskName := safeGetSlice(textSplit, 2)
		for i := 3; i < len(textSplit); i++ {
			keyVal := strings.Split(textSplit[i], "=")
			if len(keyVal) == 2 {
				key := keyVal[0]
				val := keyVal[1]
				userInputEnvs[key] = val
			}
		}
		taskResp, err := taskRunner.RunAiStudioTask(context.Background(), taskName, 0, userInputEnvs)
		if err != nil {
			return "", err
		}
		return taskResp.Result, nil
	case "help", "": // Handle help command and empty command
		return `help:
模式1: @PlatformsApp run $TaskName key1=val1 key2=val2
模式2: @PlatformsApp $TaskName val1 val2, 其中val1在模版中对应{{0}}`, nil

	default:
		// New mode: @bot taskName val1 val2
		// Here, command is the taskName
		taskName := command
		for i := 2; i < len(textSplit); i++ {
			val := textSplit[i]
			key := fmt.Sprintf("%d", i-2) // key will be "0", "1", ...
			userInputEnvs[key] = val
		}
		taskResp, err := taskRunner.RunAiStudioTask(context.Background(), taskName, 0, userInputEnvs)
		if err != nil {
			return "", err
		}
		return taskResp.Result, nil
	}
}

func safeGetSlice(s []string, index int) string {
	if index < len(s) {
		return s[index]
	}
	return ""
}
