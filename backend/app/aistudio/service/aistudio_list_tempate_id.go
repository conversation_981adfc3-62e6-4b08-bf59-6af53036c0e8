package service

import (
	"context"
	"fmt"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio/entity"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

func (s *AiStudioService) ListAiStudioTemplateID(ctx context.Context,
	_ *toolspb.ListAiStudioTemplateIDRequest) (*toolspb.ListAiStudioTemplateIDResponse, error) {
	dbRepo := aistudio.New()
	dbEntities, _, err := dbRepo.ListTemplate(ctx, -1, -1)
	if err != nil {
		return nil, err
	}
	options := lo.Map(dbEntities, func(dbEntity *entity.AiStudioTemplate, _ int) *toolspb.AiStudioTemplateID {
		return &toolspb.AiStudioTemplateID{
			Label: dbEntity.Name,
			Value: fmt.Sprint(dbEntity.ID),
		}
	})
	return &toolspb.ListAiStudioTemplateIDResponse{
		Status: 0,
		Msg:    "Success",
		Data: &toolspb.ListAiStudioTemplateIDData{
			Options: options,
		},
	}, nil
}
