// nolint: lll
package service

import (
	"reflect"
	"testing"

	aistudiologic "github.com/MoeGolibrary/moego/backend/app/aistudio/logic/aistudio"
)

func TestParseEnvs(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected map[string]string
	}{
		{
			name:     "Empty input",
			input:    "",
			expected: map[string]string{},
		},
		{
			name:  "Single env",
			input: "key1=value1",
			expected: map[string]string{
				"key1": "value1",
			},
		},
		{
			name:  "Multiple envs",
			input: "key1= value1\nkey2=value2",
			expected: map[string]string{
				"key1": "value1",
				"key2": "value2",
			},
		},
		{
			name:  "Env with spaces",
			input: "key1=  value1  ",
			expected: map[string]string{
				"key1": "value1",
			},
		},
		{
			name:     "Invalid env",
			input:    "key1",
			expected: map[string]string{},
		},
		{
			name:  "Input has =",
			input: "key1=value1=value2\n\nkey3=value3",
			expected: map[string]string{
				"key1": "value1=value2",
				"key3": "value3",
			},
		},
		{
			name:  "Multiple envs with spaces",
			input: "datadog-api-key=2c2dae1ad5d7952998d15af901a7f60d\ndatadog-app-key=****************************************\n\njira-url=https://moego.atlassian.net\njira-username=<EMAIL>\njira-token=ATATT3xFfGF02xghAYSPSRN6DQMAjhoacPIPP_\n\nslack-bot-token=xoxb-xxxx-xxxx-xxxxxxx",
			expected: map[string]string{
				"datadog-api-key": "2c2dae1ad5d7952998d15af901a7f60d",
				"datadog-app-key": "****************************************",
				"jira-url":        "https://moego.atlassian.net",
				"jira-username":   "<EMAIL>",
				"jira-token":      "ATATT3xFfGF02xghAYSPSRN6DQMAjhoacPIPP_",
				"slack-bot-token": "xoxb-xxxx-xxxx-xxxxxxx",
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actual := aistudiologic.ParseEnvs(tc.input)
			if !reflect.DeepEqual(actual, tc.expected) {
				t.Errorf("Expected %v, but got %v", tc.expected, actual)
			}
		})
	}
}
