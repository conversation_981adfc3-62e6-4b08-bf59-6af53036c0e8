package aistudio

import (
	"context"
	"encoding/json"
	"os"
	"sync"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio/entity"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
)

type CreateAIStudioTestSuite struct {
	suite.Suite
	rw ReadWriter
}

func TestCreateDeployTask(t *testing.T) {
	// t.Skip("skip db test in online env")
	suite.Run(t, new(CreateAIStudioTestSuite))
}

var setupTestSvc sync.Once

func (t *CreateAIStudioTestSuite) SetupSuite() {
	setupTestSvc.Do(func() {
		currentDir, _ := os.Getwd()
		currentDir = currentDir[:len(currentDir)-len("repo/aistudio")]
		rpc.ServerConfigPath = currentDir + "config/testing/config.yaml"
		_ = rpc.NewServer()
		t.rw = New()
	})
}

func (t *CreateAIStudioTestSuite) TestCreate() {
	template := &entity.AiStudioTemplate{
		Model:   "gemini-2.0-flash-001",
		Prompt:  "This is a test prompt",
		Mcps:    "mcp-atlassian,datadog,mcp-slack,unix_timestamps_mcp",
		EnvKeys: "datadog-api-key,datadog-app-key,jira-url,jira-username,jira-token,slack-bot-token",
		Name:    "test template",
	}

	err := t.rw.CreateTemplate(context.Background(), template)
	t.NoError(err)

	// 验证是否已创建
	retrievedTemplate, err := t.rw.GetTemplate(context.Background(), template.ID)
	t.NoError(err)
	t.Equal(template.Model, retrievedTemplate.Model)
	t.Equal(template.Prompt, retrievedTemplate.Prompt)
	t.Equal(template.Mcps, retrievedTemplate.Mcps)
	t.Equal(template.EnvKeys, retrievedTemplate.EnvKeys)
	t.Equal(template.Name, retrievedTemplate.Name)

	// 清理测试数据
	err = t.rw.DeleteTemplate(context.Background(), template.ID)
	t.NoError(err)
}

func (t *CreateAIStudioTestSuite) TestUpdate() {
	template := &entity.AiStudioTemplate{
		Model:   "gemini-2.0-flash-001",
		Prompt:  "This is a test prompt",
		Mcps:    "mcp-atlassian,datadog,mcp-slack,unix_timestamps_mcp",
		EnvKeys: "datadog-api-key,datadog-app-key,jira-url,jira-username,jira-token,slack-bot-token",
		Name:    "test template",
	}

	err := t.rw.CreateTemplate(context.Background(), template)
	t.NoError(err)

	// 更新模板
	template.Model = "gemini-2.0-pro-002"
	template.Prompt = "This is an updated test prompt"
	template.Name = "updated test template"
	err = t.rw.UpdateTemplate(context.Background(), template)
	t.NoError(err)

	// 验证是否已更新
	updatedTemplate, err := t.rw.GetTemplate(context.Background(), template.ID)
	t.NoError(err)
	t.Equal("gemini-2.0-pro-002", updatedTemplate.Model)
	t.Equal("This is an updated test prompt", updatedTemplate.Prompt)
	t.Equal("updated test template", updatedTemplate.Name)

	// 清理测试数据
	err = t.rw.DeleteTemplate(context.Background(), template.ID)
	t.NoError(err)
}

func (t *CreateAIStudioTestSuite) TestTaskCRUD() {
	// 创建一个模板用于关联任务
	template := &entity.AiStudioTemplate{
		Model:   "gemini-2.0-flash-001",
		Prompt:  "This is a test prompt",
		Mcps:    "mcp-atlassian,datadog,mcp-slack,unix_timestamps_mcp",
		EnvKeys: "datadog-api-key,datadog-app-key,jira-url,jira-username,jira-token,slack-bot-token",
		Name:    "test template",
	}
	err := t.rw.CreateTemplate(context.Background(), template)
	t.NoError(err)

	// 创建任务
	task := &entity.AiStudioTask{
		TaskName:   "test task",
		TemplateID: template.ID,
		Envs:       "test envs",
	}
	err = t.rw.CreateTask(context.Background(), task)
	t.NoError(err)

	// 获取任务
	retrievedTask, err := t.rw.GetTask(context.Background(), task.ID)
	t.NoError(err)
	t.Equal(task.TaskName, retrievedTask.TaskName)
	t.Equal(task.TemplateID, retrievedTask.TemplateID)
	t.Equal(task.Envs, retrievedTask.Envs)

	// 更新任务
	task.TaskName = "updated task"
	task.Envs = "updated envs"
	err = t.rw.UpdateTask(context.Background(), task)
	t.NoError(err)

	// 验证任务是否已更新
	updatedTask, err := t.rw.GetTask(context.Background(), task.ID)
	t.NoError(err)
	t.Equal("updated task", updatedTask.TaskName)
	t.Equal("updated envs", updatedTask.Envs)

	// 删除任务
	err = t.rw.DeleteTask(context.Background(), task.ID)
	t.NoError(err)

	// 验证任务是否已删除
	_, err = t.rw.GetTask(context.Background(), task.ID)
	t.Error(err)

	// 清理测试数据
	err = t.rw.DeleteTemplate(context.Background(), template.ID)
	t.NoError(err)
}

func (t *CreateAIStudioTestSuite) TestTaskLogCRUD() {
	// 创建一个任务用于关联任务日志
	template := &entity.AiStudioTemplate{
		Model:   "gemini-2.0-flash-001",
		Prompt:  "This is a test prompt",
		Mcps:    "mcp-atlassian,datadog,mcp-slack,unix_timestamps_mcp",
		EnvKeys: "datadog-api-key,datadog-app-key,jira-url,jira-username,jira-token,slack-bot-token",
		Name:    "test template",
	}
	err := t.rw.CreateTemplate(context.Background(), template)
	t.NoError(err)

	task := &entity.AiStudioTask{
		TaskName:   "test task",
		TemplateID: template.ID,
		Envs:       "test envs",
	}
	err = t.rw.CreateTask(context.Background(), task)
	t.NoError(err)

	t.testListTask(context.Background(), task.TaskName)

	// 创建任务日志
	dialogues := new(entity.AiStudioTaskLogDialogues)
	dialogues.Chats = append(dialogues.Chats, "test chat")
	jsonData, _ := json.Marshal(dialogues)

	taskLog := &entity.AiStudioTaskLog{
		TaskID:    task.ID,
		Prompt:    "test prompt",
		Envs:      "test envs",
		Dialogues: jsonData,
	}
	err = t.rw.CreateTaskLog(context.Background(), taskLog)
	t.NoError(err)

	t.testListTaskLog(context.Background(), task.TaskName)

	// 获取任务日志
	retrievedTaskLog, err := t.rw.GetTaskLog(context.Background(), taskLog.ID)
	t.NoError(err)
	t.Equal(taskLog.TaskID, retrievedTaskLog.TaskID)
	t.Equal(taskLog.Prompt, retrievedTaskLog.Prompt)
	t.Equal(taskLog.Envs, retrievedTaskLog.Envs)

	retrievedTaskLogDialogues := new(entity.AiStudioTaskLogDialogues)
	err = json.Unmarshal(retrievedTaskLog.Dialogues, retrievedTaskLogDialogues)
	t.NoError(err)
	t.Equal(dialogues.Chats[0], retrievedTaskLogDialogues.Chats[0])

	// 删除任务日志
	err = t.rw.DeleteTaskLog(context.Background(), taskLog.ID)
	t.NoError(err)

	// 验证任务日志是否已删除
	_, err = t.rw.GetTaskLog(context.Background(), taskLog.ID)
	t.Error(err)

	// 清理测试数据
	err = t.rw.DeleteTask(context.Background(), task.ID)
	t.NoError(err)
	err = t.rw.DeleteTemplate(context.Background(), template.ID)
	t.NoError(err)
}

func (t *CreateAIStudioTestSuite) testListTask(ctx context.Context, mustHaveName string) {
	logs, _, err := t.rw.ListTasks(ctx, 10, 0)
	t.NoError(err)

	t.T().Logf("tasks: %s", lo.Must(json.Marshal(logs)))

	find := false
	for _, log := range logs {
		if log.TaskName == mustHaveName {
			find = true
		}
	}
	t.True(find)
}

func (t *CreateAIStudioTestSuite) testListTaskLog(ctx context.Context, mustHaveName string) {
	logs, _, err := t.rw.ListTaskLogs(ctx, 1024, 0)
	t.NoError(err)

	t.T().Logf("task logs: %s", lo.Must(json.Marshal(logs)))

	find := false
	for _, log := range logs {
		if log.Task.TaskName == mustHaveName {
			find = true
		}
	}
	t.True(find)
}
