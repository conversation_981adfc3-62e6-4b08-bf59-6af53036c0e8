package aistudio

import (
	"reflect"
	"testing"
)

func TestMCPConfigBuilder_buildMCPAtlassian(t *testing.T) {
	builder := NewMCPConfigBuilder()
	replacement := map[string]string{
		"jira-username": "<EMAIL>",
		"jira-token":    "test-token",
	}

	expected := `{"timeout":60,"command":"uvx","args":["mcp-atlassian","--jira-url=https://moego.atlassian.net","--jira-username=<EMAIL>","--jira-token=test-token"],"env":{},"transportType":"stdio"}`

	actual := builder.buildMCPAtlassian(replacement)

	if !reflect.DeepEqual(actual.String(), expected) {
		t.<PERSON>rf("buildMCPAtlassian() = %v, want %v", actual, expected)
	}
}

func TestMCPConfigBuilder_buildUnixTimestamps(t *testing.T) {
	builder := NewMCPConfigBuilder()
	replacement := map[string]string{}

	expected := `{"timeout":60,"command":"npx","args":["-y","github:Ivor/unix-timestamps-mcp"],"env":{},"transportType":"stdio"}`

	actual := builder.buildUnixTimestamps(replacement)

	if !reflect.DeepEqual(actual.String(), expected) {
		t.Errorf("buildUnixTimestamps() = %v, want %v", actual, expected)
	}
}

func TestMCPConfigBuilder_buildDatadog(t *testing.T) {
	builder := NewMCPConfigBuilder()
	replacement := map[string]string{
		"datadog-api-key": "test-api-key",
		"datadog-app-key": "test-app-key",
	}

	expected := `{"timeout":60,"command":"npx","args":["-y","@winor30/mcp-server-datadog"],"env":{"DATADOG_API_KEY":"test-api-key","DATADOG_APP_KEY":"test-app-key","DATADOG_SITE":"us5.datadoghq.com"},"transportType":"stdio"}`

	actual := builder.buildDatadog(replacement)

	if !reflect.DeepEqual(actual.String(), expected) {
		t.Errorf("buildDatadog() = %v, want %v", actual, expected)
	}
}

func TestMCPConfigBuilder_buildMCPSlack(t *testing.T) {
	builder := NewMCPConfigBuilder()
	replacement := map[string]string{
		"slack-bot-token": "test-token",
	}

	expected := `{"timeout":60,"command":"npx","args":["-y","@modelcontextprotocol/server-slack"],"env":{"SLACK_BOT_TOKEN":"test-token","SLACK_TEAM_ID":"T011CF3CMJN"},"transportType":"stdio"}`

	actual := builder.buildMCPSlack(replacement)

	if !reflect.DeepEqual(actual.String(), expected) {
		t.Errorf("buildMCPSlack() = %v, want %v", actual, expected)
	}
}
