package aistudio

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"testing"

	"github.com/stretchr/testify/mock"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio/entity"
	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio/mocks"
)

// MockMCPClient is a mock implementation of MCPClientInterface
type MockMCPClient struct {
	mock.Mock
}

func (m *MockMCPClient) Send2Gemini(ctx context.Context) (*GeminiResp, error) {
	args := m.Called(ctx)
	return args.Get(0).(*GeminiResp), args.Error(1)
}

func Test_GetClient(t *testing.T) {
	type args struct {
		model     string
		prompt    string
		geminiKey string
		mcps      []string
		envs      map[string]string
	}
	tests := []struct {
		name     string
		inputs   args
		expected string
	}{
		{
			name: "test1",
			inputs: args{
				model:     "gemini-2.0-flash",
				prompt:    "This is a multi-step task, please complete it in order:\\n\\n1. Please first Get all Jira Tasks or Stories, condition: assignee or report is {{jira-username}}, all projects, up to 10 items.\\n2. Please optimize the summaries of these tasks from the user perspective, outputs, and outcomes. A good example is: As a user role, I hope to do outputs and achieve outcomes.\\n3. outputs the jira key, summary of the original jira task and the optimized summary(convert it to Chinese) in the previous step.",
				geminiKey: "AIzaSyBFjCEU8GBqUJCLtNZ4gfg4aXn",
				mcps:      []string{"mcp-atlassian", "datadog", "mcp-slack", "unix_timestamps_mcp"},
				envs: map[string]string{
					"datadog-api-key": "2c2dae1ad5d7952998d15af901a7f60d",
					"datadog-app-key": "****************************************",
					"jira-url":        "https://moego.atlassian.net",
					"jira-username":   "<EMAIL>",
					"jira-token":      "ATATT3xFfGF02xghAYSPSRN6DQMAjhoacPIPP_",
					"slack-bot-token": "xoxb-xxxx-xxxx-xxxxxxx",
				},
			},
			expected: `{"model_type":"gemini-2.0-flash-001","init_prompt":"This is a multi-step task, please complete it in order:\\n\\n1. Please first Get all Jira Tasks or Stories, condition: assignee or <NAME_EMAIL>, all projects, up to 10 items.\\n2. Please optimize the summaries of these tasks from the user perspective, outputs, and outcomes. A good example is: As a user role, I hope to do outputs and achieve outcomes.\\n3. outputs the jira key, summary of the original jira task and the optimized summary(convert it to Chinese) in the previous step.","mcp_servers_config":{"datadog":{"timeout":60,"command":"npx","args":["-y","@winor30/mcp-server-datadog"],"env":{"DATADOG_API_KEY":"2c2dae1ad5d7952998d15af901a7f60d","DATADOG_APP_KEY":"****************************************","DATADOG_SITE":"us5.datadoghq.com"},"transportType":"stdio"},"mcp-atlassian":{"timeout":60,"command":"uvx","args":["mcp-atlassian","--jira-url=https://moego.atlassian.net","--jira-username=<EMAIL>","--jira-token=ATATT3xFfGF02xghAYSPSRN6DQMAjhoacPIPP_"],"env":{},"transportType":"stdio"},"mcp-slack":{"timeout":60,"command":"npx","args":["-y","@modelcontextprotocol/server-slack"],"env":{"SLACK_BOT_TOKEN":"xoxb-xxxx-xxxx-xxxxxxx","SLACK_TEAM_ID":"T011CF3CMJN"},"transportType":"stdio"},"unix_timestamps_mcp":{"timeout":60,"command":"npx","args":["-y","github:Ivor/unix-timestamps-mcp"],"env":{},"transportType":"stdio"}},"api_key":"AIzaSyBFjCEU8GBqUJCLtNZ4gfg4aXn"}`,
		},
	}
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			client := GetClient(tc.inputs.model, tc.inputs.prompt, tc.inputs.geminiKey, tc.inputs.mcps, tc.inputs.envs)
			clientJs, _ := json.Marshal(client)
			if !reflect.DeepEqual(tc.expected, string(clientJs)) {
				t.Errorf("Expected %s, but got %s", tc.expected, string(clientJs))
			}
		})
	}
}

func Test_ParseEnvs(t *testing.T) {
	tests := []struct {
		name       string
		envsString string
		expected   map[string]string
	}{
		{
			name:       "empty string",
			envsString: "",
			expected:   map[string]string{},
		},
		{
			name:       "single valid env",
			envsString: "KEY1=VALUE1",
			expected:   map[string]string{"KEY1": "VALUE1"},
		},
		{
			name:       "multiple valid envs",
			envsString: "KEY1=VALUE1\nKEY2=VALUE2",
			expected:   map[string]string{"KEY1": "VALUE1", "KEY2": "VALUE2"},
		},
		{
			name:       "envs with spaces",
			envsString: " KEY_WITH_SPACES = VALUE_WITH_SPACES ",
			expected:   map[string]string{"KEY_WITH_SPACES": "VALUE_WITH_SPACES"},
		},
		{
			name:       "envs with comments and empty lines",
			envsString: "# This is a comment\nKEY1=VALUE1\n\n# Another comment\nKEY2=VALUE2",
			expected:   map[string]string{"KEY1": "VALUE1", "KEY2": "VALUE2"},
		},
		{
			name:       "line without equals sign",
			envsString: "INVALID_LINE\nKEY1=VALUE1",
			expected:   map[string]string{"KEY1": "VALUE1"},
		},
		{
			name:       "value with equals sign",
			envsString: "KEY1=VALUE=WITH=EQUALS",
			expected:   map[string]string{"KEY1": "VALUE=WITH=EQUALS"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ParseEnvs(tt.envsString)
			if !reflect.DeepEqual(got, tt.expected) {
				t.Errorf("ParseEnvs() for %q got = %v, want %v", tt.envsString, got, tt.expected)
			}
		})
	}
}

func Test_checkReqEnvInTaskEnv(t *testing.T) {
	type args struct {
		reqEnvKV  map[string]string
		taskEnvKV map[string]string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "all required envs present",
			args: args{
				reqEnvKV:  map[string]string{"KEY1": "VAL1", "KEY2": "VAL2"},
				taskEnvKV: map[string]string{"KEY1": "VAL1", "KEY2": "VAL2", "KEY3": "VAL3"},
			},
			wantErr: false,
		},
		{
			name: "missing required env",
			args: args{
				reqEnvKV:  map[string]string{"KEY1": "VAL1", "KEY2": "VAL2"},
				taskEnvKV: map[string]string{"KEY1": "VAL1"},
			},
			wantErr: true,
		},
		{
			name: "empty required envs",
			args: args{
				reqEnvKV:  map[string]string{},
				taskEnvKV: map[string]string{"KEY1": "VAL1"},
			},
			wantErr: false,
		},
		{
			name: "empty task envs with required envs",
			args: args{
				reqEnvKV:  map[string]string{"KEY1": "VAL1"},
				taskEnvKV: map[string]string{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := checkReqEnvInTaskEnv(tt.args.reqEnvKV, tt.args.taskEnvKV); (err != nil) != tt.wantErr {
				t.Errorf("checkReqEnvInTaskEnv() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestAiStudioTask_RunAiStudioTask(t *testing.T) {
	tests := []struct {
		name          string
		taskName      string
		taskID        int64
		reqEnvKV      map[string]string
		mockRepoSetup func(*mocks.ReadWriter)
		mockMCPClient func() *MockMCPClient
		expectedResp  *TaskResponse
		wantErr       bool
		expectedErr   string
	}{
		{
			name:     "Run by Task ID - Success",
			taskID:   1,
			reqEnvKV: map[string]string{"jira-username": "testuser"},
			mockRepoSetup: func(m *mocks.ReadWriter) {
				m.On("GetTask", mock.Anything, int64(1)).Return(&entity.AiStudioTask{
					ID:        1,
					TaskName:  "TestTask",
					AIKey:     "test-key",
					IMChannel: "test-channel",
					Envs:      "jira-username=defaultuser\nother=value",
					Template: entity.AiStudioTemplate{
						Model:  "gemini-pro",
						Mcps:   "mcp-atlassian",
						Prompt: "Hello {{jira-username}}",
					},
				}, nil).Once()
				m.On("CreateTaskLog", mock.Anything, mock.AnythingOfType("*entity.AiStudioTaskLog")).Return(nil).Once()
			},
			mockMCPClient: func() *MockMCPClient {
				mockClient := new(MockMCPClient)
				mockClient.On("Send2Gemini", mock.Anything).Return(&GeminiResp{
					Dialogues: []string{"Hello testuser"},
					Result:    "Success",
				}, nil).Once()
				return mockClient
			},
			expectedResp: &TaskResponse{
				Dialogues: []string{"Hello testuser"},
				Result:    "Success",
				IMChannel: "test-channel",
			},
			wantErr: false,
		},
		{
			name:     "Run by Task Name - Success",
			taskName: "TestTaskByName",
			reqEnvKV: map[string]string{"jira-username": "anotheruser"},
			mockRepoSetup: func(m *mocks.ReadWriter) {
				m.On("GetTaskByName", mock.Anything, "TestTaskByName").Return(&entity.AiStudioTask{
					ID:        2,
					TaskName:  "TestTaskByName",
					AIKey:     "test-key-2",
					IMChannel: "test-channel-2",
					Envs:      "jira-username=defaultuser",
					Template: entity.AiStudioTemplate{
						Model:  "gemini-pro",
						Mcps:   "mcp-atlassian",
						Prompt: "Hi {{jira-username}}",
					},
				}, nil).Once()
				m.On("CreateTaskLog", mock.Anything, mock.AnythingOfType("*entity.AiStudioTaskLog")).Return(nil).Once()
			},
			mockMCPClient: func() *MockMCPClient {
				mockClient := new(MockMCPClient)
				mockClient.On("Send2Gemini", mock.Anything).Return(&GeminiResp{
					Dialogues: []string{"Hi anotheruser"},
					Result:    "Success 2",
				}, nil).Once()
				return mockClient
			},
			expectedResp: &TaskResponse{
				Dialogues: []string{"Hi anotheruser"},
				Result:    "Success 2",
				IMChannel: "test-channel-2",
			},
			wantErr: false,
		},
		{
			name:          "No Task ID or Name",
			taskID:        0,
			taskName:      "",
			reqEnvKV:      map[string]string{},
			mockRepoSetup: func(m *mocks.ReadWriter) {},
			mockMCPClient: func() *MockMCPClient { return nil },
			expectedResp:  &TaskResponse{},
			wantErr:       true,
			expectedErr:   "failed to get task, name:, id:0",
		},
		{
			name:     "GetTask Error",
			taskID:   1,
			reqEnvKV: map[string]string{},
			mockRepoSetup: func(m *mocks.ReadWriter) {
				m.On("GetTask", mock.Anything, int64(1)).Return(nil, errors.New("db error")).Once()
			},
			mockMCPClient: func() *MockMCPClient { return nil },
			expectedResp:  &TaskResponse{},
			wantErr:       true,
			expectedErr:   "failed to get task by ID 1: db error",
		},
		{
			name:     "GetTaskByName Error",
			taskName: "NonExistentTask",
			reqEnvKV: map[string]string{},
			mockRepoSetup: func(m *mocks.ReadWriter) {
				m.On("GetTaskByName", mock.Anything, "NonExistentTask").Return(nil, errors.New("not found")).Once()
			},
			mockMCPClient: func() *MockMCPClient { return nil },
			expectedResp:  &TaskResponse{},
			wantErr:       true,
			expectedErr:   "failed to get task by name NonExistentTask: not found",
		},
		{
			name:     "Req Env Not In Task Env Error",
			taskID:   1,
			reqEnvKV: map[string]string{"nonexistent_key": "value"},
			mockRepoSetup: func(m *mocks.ReadWriter) {
				m.On("GetTask", mock.Anything, int64(1)).Return(&entity.AiStudioTask{
					ID:        1,
					TaskName:  "TestTask",
					AIKey:     "test-key",
					IMChannel: "test-channel",
					Envs:      "jira-username=defaultuser",
					Template: entity.AiStudioTemplate{
						Model:  "gemini-pro",
						Mcps:   "mcp-atlassian",
						Prompt: "Hello {{jira-username}}",
					},
				}, nil).Once()
			},
			mockMCPClient: func() *MockMCPClient { return nil },
			expectedResp:  nil, // Expect nil because the error happens before resp is initialized
			wantErr:       true,
			expectedErr:   "envs not match,key:nonexistent_key is not in map[jira-username:defaultuser]",
		},
		{
			name:     "Send2Gemini Error",
			taskID:   1,
			reqEnvKV: map[string]string{"jira-username": "testuser"},
			mockRepoSetup: func(m *mocks.ReadWriter) {
				m.On("GetTask", mock.Anything, int64(1)).Return(&entity.AiStudioTask{
					ID:        1,
					TaskName:  "TestTask",
					AIKey:     "test-key",
					IMChannel: "test-channel",
					Envs:      "jira-username=defaultuser",
					Template: entity.AiStudioTemplate{
						Model:  "gemini-pro",
						Mcps:   "mcp-atlassian",
						Prompt: "Hello {{jira-username}}",
					},
				}, nil).Once()
			},
			mockMCPClient: func() *MockMCPClient {
				mockClient := new(MockMCPClient)
				mockClient.On("Send2Gemini", mock.Anything).Return(&GeminiResp{}, errors.New("gemini error")).Once()
				return mockClient
			},
			expectedResp: nil,
			wantErr:      true,
			expectedErr:  "failed to send to gemini: gemini error",
		},
		{
			name:     "CreateTaskLog Error",
			taskID:   1,
			reqEnvKV: map[string]string{"jira-username": "testuser"},
			mockRepoSetup: func(m *mocks.ReadWriter) {
				m.On("GetTask", mock.Anything, int64(1)).Return(&entity.AiStudioTask{
					ID:        1,
					TaskName:  "TestTask",
					AIKey:     "test-key",
					IMChannel: "test-channel",
					Envs:      "jira-username=defaultuser",
					Template: entity.AiStudioTemplate{
						Model:  "gemini-pro",
						Mcps:   "mcp-atlassian",
						Prompt: "Hello {{jira-username}}",
					},
				}, nil).Once()
				m.On("CreateTaskLog", mock.Anything, mock.AnythingOfType("*entity.AiStudioTaskLog")).Return(errors.New("log error")).Once()
			},
			mockMCPClient: func() *MockMCPClient {
				mockClient := new(MockMCPClient)
				mockClient.On("Send2Gemini", mock.Anything).Return(&GeminiResp{
					Dialogues: []string{"Hello testuser"},
					Result:    "Success",
				}, nil).Once()
				return mockClient
			},
			expectedResp: nil,
			wantErr:      true,
			expectedErr:  "failed to create task log: log error",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := new(mocks.ReadWriter)
			if tt.mockRepoSetup != nil {
				tt.mockRepoSetup(mockRepo)
			}

			var mockMCP *MockMCPClient
			if tt.mockMCPClient != nil {
				mockMCP = tt.mockMCPClient()
			}

			s := &Task{
				repo: mockRepo,
				clientGetterFactory: func(model, prompt, geminiKey string, mcps []string, envs map[string]string) MCPClientInterface {
					return mockMCP
				},
			}

			resp, err := s.RunAiStudioTask(context.Background(), tt.taskName, tt.taskID, tt.reqEnvKV)

			if (err != nil) != tt.wantErr {
				t.Errorf("RunAiStudioTask() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && err != nil && err.Error() != tt.expectedErr {
				t.Errorf("RunAiStudioTask() error = %v, expectedErr %v", err.Error(), tt.expectedErr)
				return
			}
			if !reflect.DeepEqual(resp, tt.expectedResp) {
				t.Errorf("RunAiStudioTask() got = %v, want %v", resp, tt.expectedResp)
			}

			mockRepo.AssertExpectations(t)
			if mockMCP != nil {
				mockMCP.AssertExpectations(t)
			}
		})
	}
}
