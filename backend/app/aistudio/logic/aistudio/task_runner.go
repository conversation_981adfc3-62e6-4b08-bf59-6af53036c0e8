package aistudio

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio/entity"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ClientGetterFunc func(model, prompt, geminiKey string, mcps []string, envs map[string]string) MCPClientInterface

func GetClient(model, prompt, geminiKey string, mcps []string, envs map[string]string) MCPClientInterface {
	mcpServersConfig := map[string]*MCPConfig{}
	for _, mcp := range mcps {
		mcpConfig := NewMCPConfigBuilder().Build(mcp, envs)
		mcpServersConfig[mcp] = mcpConfig
	}
	prompt = ReplaceVariables(prompt, envs)
	client := NewMCPClient(model, prompt, geminiKey, mcpServersConfig)
	return client
}

// TaskRunner is the interface for running AI Studio tasks.
type TaskRunner interface {
	RunAiStudioTask(ctx context.Context,
		taskName string, taskID int64, reqEnvKV map[string]string) (*TaskResponse, error)
}

// TaskResponse run task response
type TaskResponse struct {
	Dialogues []string `json:"dialogues"`
	Result    string   `json:"result"`
	IMChannel string   `json:"im_channel"`
}

func NewTask() *Task {
	return &Task{repo: aistudio.New(), clientGetterFactory: GetClient}
}

type Task struct {
	repo                aistudio.ReadWriter
	clientGetterFactory ClientGetterFunc
}

func (a *Task) RunAiStudioTask(ctx context.Context,
	taskName string, taskID int64, reqEnvKV map[string]string) (resp *TaskResponse, err error) {

	resp = &TaskResponse{}
	var task *entity.AiStudioTask
	if taskID != 0 {
		// 通过ID运行任务
		task, err = a.repo.GetTask(ctx, taskID)
		if err != nil {
			return resp, fmt.Errorf("failed to get task by ID %v: %v", taskID, err)
		}
	} else if taskName != "" {
		// 通过Name运行任务
		task, err = a.repo.GetTaskByName(ctx, taskName)
		if err != nil {
			return resp, fmt.Errorf("failed to get task by name %v: %v", taskName, err)
		}
	} else {
		return resp, fmt.Errorf("failed to get task, name:%v, id:%v", taskName, taskID)
	}

	model := task.Template.Model
	mcps := strings.Split(task.Template.Mcps, ",")
	prompt := task.Template.Prompt
	taskEnvKV := ParseEnvs(task.Envs)
	aiKey := task.AIKey

	err = checkReqEnvInTaskEnv(reqEnvKV, taskEnvKV)
	if err != nil {
		return nil, err
	}
	log.InfoContextf(ctx, "RunAiStudioTask geminiKey: %s, prompt: %s, mcps: %v, taskEnvKV: %s,reqEnvKV:%v model: %s",
		aiKey, prompt, mcps, taskEnvKV, reqEnvKV, model)

	for reqEnvKey, reqEnvValue := range reqEnvKV {
		// 如果调用的时候传入env，以调用时为准
		_, ok := taskEnvKV[reqEnvKey]
		if ok {
			taskEnvKV[reqEnvKey] = reqEnvValue
		}
	}

	client := a.clientGetterFactory(model, prompt, aiKey, mcps, taskEnvKV)
	geminiResp, err := client.Send2Gemini(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to send to gemini: %v", err)
	}

	err = a.repo.CreateTaskLog(ctx, &entity.AiStudioTaskLog{
		TaskID:     task.ID,
		Prompt:     prompt,
		Envs:       string(lo.Must(json.Marshal(taskEnvKV))),
		Dialogues:  lo.Must(json.Marshal(entity.AiStudioTaskLogDialogues{Chats: geminiResp.Dialogues})),
		CreateTime: time.Now(),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create task log: %v", err)
	}

	return &TaskResponse{
		Dialogues: geminiResp.Dialogues,
		Result:    geminiResp.Result,
		IMChannel: task.IMChannel,
	}, nil
}

func ParseEnvs(envsString string) map[string]string {
	envs := make(map[string]string)
	lines := strings.Split(envsString, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		index := strings.Index(line, "=")
		if index == -1 {
			continue
		}
		key := strings.TrimSpace(line[:index])
		value := strings.TrimSpace(line[index+1:])
		envs[key] = value
	}
	return envs
}

func checkReqEnvInTaskEnv(reqEnvKV map[string]string,
	taskEnvKV map[string]string) (err error) {
	// req envs: key=value\nkey=value
	// 确认所有req的env都在regEnv中
	for reqEnvKey := range reqEnvKV {
		_, ok := taskEnvKV[reqEnvKey]
		if !ok {
			return fmt.Errorf("envs not match,key:%s is not in %v", reqEnvKey, taskEnvKV)
		}
	}

	return nil
}
