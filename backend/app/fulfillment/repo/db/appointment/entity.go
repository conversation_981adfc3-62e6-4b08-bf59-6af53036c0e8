package appointment

import (
	"time"
)

// 列名常量定义，避免手搓列名
const (
	ColumnBusinessID      = "business_id"
	ColumnCompanyID       = "company_id"
	ColumnCustomerID      = "customer_id"
	ColumnStatus          = "status"
	ColumnServiceItemType = "service_item_type"
	ColumnStartTime       = "start_time"
	ColumnEndTime         = "end_time"
	ColumnColorCode       = "color_code"
	ColumnCreatedAt       = "created_at"
	ColumnUpdatedAt       = "updated_at"
	ColumnID              = "id"
)

type BaseParam struct {
	BusinessID     int32
	CompanyID      int32
	PaginationInfo *PaginationInfo
	StartTime      time.Time
	EndTime        time.Time
}

type Filter struct {
	CustomerIDs      []int64
	Statuses         []int32
	ServiceItemTypes []int32
	BusinessIDs      []int32
}

type PaginationInfo struct {
	Offset int32
	Limit  int32
}

type Appointment struct {
	ID              int       `json:"id" gorm:"column:id"`
	BusinessID      int       `json:"business_id" gorm:"column:business_id"`
	CustomerID      int       `json:"customer_id" gorm:"column:customer_id"`
	CompanyID       int       `json:"company_id" gorm:"column:company_id"`
	Status          int       `json:"status" gorm:"column:status"`
	ServiceItemType int       `json:"service_item_type" gorm:"column:service_item_type"`
	StartTime       time.Time `json:"start_time" gorm:"column:start_time"`
	ColorCode       string    `json:"color_code" gorm:"column:color_code"`
	EndTime         time.Time `json:"end_time" gorm:"column:end_time"`
	CreatedAt       time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt       time.Time `json:"updated_at" gorm:"column:updated_at"`
}

// TableName 指定表名
func (Appointment) TableName() string {
	return "appointment"
}
