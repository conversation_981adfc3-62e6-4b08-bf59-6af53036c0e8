package appointment

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func (i *impl) BatchCreate(ctx context.Context, appointments []*Appointment) error {
	if len(appointments) == 0 {
		return nil
	}
	// 使用事务进行批量插入
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.CreateInBatches(appointments, len(appointments)).Error; err != nil {
			log.ErrorContextf(ctx, "BatchCreateAppointment err, err:%+v", err)
			return err
		}
		return nil
	})
}
