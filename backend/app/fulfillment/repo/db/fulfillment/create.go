package fulfillment

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func (i *impl) BatchCreate(ctx context.Context, fulfillments []*Fulfillment) error {
	if len(fulfillments) == 0 {
		return nil
	}
	// 使用事务进行批量插入
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.CreateInBatches(fulfillments, len(fulfillments)).Error; err != nil {
			log.ErrorContextf(ctx, "BatchCreateFulfillment err, err:%+v", err)
			return err
		}
		return nil
	})
}
