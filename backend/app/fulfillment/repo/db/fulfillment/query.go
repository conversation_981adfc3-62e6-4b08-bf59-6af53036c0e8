package fulfillment

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func (i *impl) Count(ctx context.Context, param *BaseParam, filter *Filter) (int64, error) {
	if param == nil {
		return 0, nil
	}
	query := i.buildQuery(ctx, param, filter)
	var count int64
	if err := query.Count(&count).Error; err != nil {
		log.ErrorContextf(ctx, "GetFulfillmentListCount err, err:%+v", err)
		return 0, err
	}
	return count, nil
}

func (i *impl) List(ctx context.Context, param *BaseParam, filter *Filter) ([]*Fulfillment, error) {
	if param == nil {
		return []*Fulfillment{}, nil
	}
	query := i.buildQuery(ctx, param, filter)
	var fulfillmentList []*Fulfillment
	offset, limit := buildPaginationInfo(param.PaginationInfo)
	if err := query.
		Offset(int(offset)).
		Limit(int(limit)).
		Order(ColumnCreateTime + " desc").
		Find(&fulfillmentList).Error; err != nil {
		log.ErrorContextf(ctx, "GetFulfillmentList err, err:%+v", err)
		return nil, err
	}
	return fulfillmentList, nil
}

func buildPaginationInfo(paginationInfo *PaginationInfo) (int32, int32) {
	if paginationInfo == nil {
		return defaultOffset, defaultLimit
	}
	return paginationInfo.Offset, paginationInfo.Limit
}

func (i *impl) buildQuery(ctx context.Context, param *BaseParam, filter *Filter) *gorm.DB {
	// 使用Model方法，让GORM自动处理表名和字段映射
	query := i.db.WithContext(ctx).Model(&Fulfillment{})

	// 应用BaseParam过滤条件
	query = i.applyBaseParamFilters(query, param)

	// 应用Filter过滤条件
	if filter != nil {
		query = i.applyFilterConditions(query, filter)
	}

	return query
}

// applyBaseParamFilters 应用BaseParam过滤条件
func (i *impl) applyBaseParamFilters(query *gorm.DB, param *BaseParam) *gorm.DB {
	if param.BusinessID != 0 {
		query = query.Where(ColumnBusinessID+" = ?", param.BusinessID)
	}
	if param.CompanyID != 0 {
		query = query.Where(ColumnCompanyID+" = ?", param.CompanyID)
	}
	// 时间范围
	query = query.Where(ColumnStartTime+" >= ? AND "+ColumnEndTime+" <= ?", param.StartTime, param.EndTime)
	return query
}

// applyFilterConditions 应用Filter过滤条件
func (i *impl) applyFilterConditions(query *gorm.DB, filter *Filter) *gorm.DB {
	if len(filter.CareTypes) > 0 {
		query = query.Where(ColumnCareType+" IN ?", filter.CareTypes)
	}
	if len(filter.PetIDs) > 0 {
		query = query.Where(ColumnPetID+" IN ?", filter.PetIDs)
	}
	if filter.StaffID > 0 {
		query = query.Where(ColumnStaffID+" = ?", filter.StaffID)
	}
	if len(filter.States) > 0 {
		query = query.Where(ColumnState+" IN ?", filter.States)
	}
	if len(filter.CustomerIDs) > 0 {
		query = query.Where(ColumnCustomerID+" IN ?", filter.CustomerIDs)
	}
	return query
}

func (i *impl) DeleteByServiceInstanceID(ctx context.Context, serviceInstanceID int64) error {
	return i.db.WithContext(ctx).Where(ColumnServiceInstanceID+" = ?", serviceInstanceID).Delete(&Fulfillment{}).Error
}

func (i *impl) GetByServiceInstanceID(ctx context.Context, serviceInstanceID int64) ([]*Fulfillment, error) {
	var fulfillments []*Fulfillment
	query := i.db.WithContext(ctx).Where(ColumnServiceInstanceID+" = ?", serviceInstanceID)
	if err := query.Find(&fulfillments).Error; err != nil {
		return nil, err
	}
	return fulfillments, nil
}
