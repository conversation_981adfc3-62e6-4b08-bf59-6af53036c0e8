load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "fulfillment",
    srcs = [
        "create.go",
        "entity.go",
        "fulfillment.go",
        "query.go",
        "update.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db",
        "//backend/common/rpc/framework/log",
        "@io_gorm_gorm//:gorm",
    ],
)
