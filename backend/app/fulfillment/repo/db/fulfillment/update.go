package fulfillment

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// Update 更新单个fulfillment记录
// 调用者只需要填写要更新的字段，系统会自动更新UpdatedAt时间戳
func (i *impl) Update(ctx context.Context, fulfillment *Fulfillment) error {
	if fulfillment == nil {
		return nil
	}
	// 使用Select来只更新非零值字段
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 执行更新操作
		if err := tx.Model(&Fulfillment{}).
			Where(ColumnID+" = ?", fulfillment.ID).
			Updates(&fulfillment).
			Error; err != nil {
			log.ErrorContextf(ctx, "UpdateFulfillment err, fulfillment_id:%d, err:%+v", fulfillment.ID, err)
			return err
		}

		return nil
	})
}
