load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "fulfillment_report",
    srcs = ["fulfillment_report.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/fulfillment_report",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db",
        "//backend/app/fulfillment/repo/db/fulfillment_report",
        "//backend/proto/fulfillment/v1:fulfillment",
    ],
)
