package fulfillmentreport

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
	fulfillmentreportrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment_report"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

func New() *Logic {
	return &Logic{
		frRepo: fulfillmentreportrepo.New(),
	}
}

type Logic struct {
	frRepo fulfillmentreportrepo.ReadWriter
	_      db.TransactionManager
}

func (l *Logic) GetFulfillmentReportTemplate(_ context.Context,
	_ *pb.GetFulfillmentReportTemplateRequest) (*pb.GetFulfillmentReportTemplateResponse, error) {
	// TODO: implement
	return nil, nil
}

func (l *Logic) UpdateFulfillmentReportTemplate(_ context.Context,
	_ *pb.UpdateFulfillmentReportTemplateRequest) (*pb.UpdateFulfillmentReportTemplateResponse, error) {
	// TODO: implement
	return nil, nil
}
