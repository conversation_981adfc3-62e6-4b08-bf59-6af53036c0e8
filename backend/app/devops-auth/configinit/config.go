package configinit

import (
	"fmt"
	"os"
	"sync"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// Config is the configuration for the service.
type Config struct {
	Server ServerConfig `mapstructure:"server"`
	Google GoogleConfig `mapstructure:"google"`
	JWT    JWTConfig    `mapstructure:"jwt"`
}

// ServerConfig is the configuration for the server.
type ServerConfig struct {
	HTTPPort int `mapstructure:"httpPort"`
	GRPCPort int `mapstructure:"grpcPort"`
}

// GoogleConfig is the configuration for Google SSO.
type GoogleConfig struct {
	ClientID string `mapstructure:"clientId"`
}

// JWTConfig is the configuration for JWT.
type JWTConfig struct {
	Secret string `mapstructure:"secret"`
}

var initCfg sync.Once

// Init initializes the configuration from a given path.
func Init(dir string) *Config {
	var cfg *Config
	initCfg.Do(func() {
		env := os.Getenv("MOEGO_ENVIRONMENT")
		if len(env) == 0 {
			env = "local"
			log.Warnf("MOEGO_ENVIRONMENT is not set, using default value: %s", env)
		}

		c, err := config.DefaultConfigLoader.Load(fmt.Sprintf("%s/%s/devops_auth.yaml", dir, env))
		if err != nil {
			panic(err)
		}

		cfg = &Config{}
		err = c.Unmarshal(cfg)
		if err != nil {
			panic(err)
		}
	})
	return cfg
}
