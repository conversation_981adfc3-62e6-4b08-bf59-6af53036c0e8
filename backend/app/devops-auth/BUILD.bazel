load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "devops-auth_bin",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/devops-auth",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/devops-auth/configinit",
        "//backend/app/devops-auth/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/opentelemetry",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/filters/validation",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/http",
        "//backend/common/rpc/framework/log",
        "@com_github_envoyproxy_go_control_plane_envoy//service/auth/v3:auth",
        "@com_github_gorilla_mux//:mux",
    ],
)

go_binary(
    name = "devops-auth",
    embed = [":devops-auth_bin"],
    visibility = ["//visibility:public"],
)
