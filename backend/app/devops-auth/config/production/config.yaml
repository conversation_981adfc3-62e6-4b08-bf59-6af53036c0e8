server:
  filter:
    - opentelemetry
    - debuglog
    - recovery
    - validation
  service:
    - name: envoy.service.auth.v3.Authorization
      ip: 127.0.0.1
      port: 9090
      protocol: grpc
      timeout: 5000
    - name: moego.devops.auth
      network: tcp
      protocol: http_no_protocol
      timeout: 1000
      ip: 0.0.0.0
      port: 8080
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 5000
  service:
    - callee: greeter_cutomized_name
      target: ip://127.0.0.1:9090
      protocol: grpc
      network: tcp
      transport: grpc
plugins:
  telemetry:
    opentelemetry:
      traces:
        disable_trace_body: false
  auth:
    validation:
      enable_error_log: false
