load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "errs",
    srcs = [
        "errs.go",
        "stack.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs",
    visibility = ["//visibility:public"],
    deps = [
        "//third_party/googleapis/google/rpc:status_go_proto",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)

go_test(
    name = "errs_test",
    srcs = [
        "errs_test.go",
        "stack_test.go",
    ],
    deps = [
        ":errs",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_grpc//codes",
    ],
)
