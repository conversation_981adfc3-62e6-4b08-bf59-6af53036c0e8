load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "money",
    srcs = ["money.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/utils/money",
    visibility = ["//visibility:public"],
    deps = [
        "//third_party/googleapis/google/type:money_go_proto",
        "@com_github_shopspring_decimal//:decimal",
    ],
)

go_test(
    name = "money_test",
    srcs = ["money_test.go"],
    embed = [":money"],
    deps = [
        "//third_party/googleapis/google/type:money_go_proto",
        "@com_github_stretchr_testify//require",
    ],
)
