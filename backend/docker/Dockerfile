FROM alpine:latest

WORKDIR /data

# 从构建阶段复制构建的二进制文件
ARG APP_NAME
ARG BIN_DIR
ARG DD_GIT_REPOSITORY_URL
ARG DD_GIT_COMMIT_SHA

ENV DD_GIT_REPOSITORY_URL=${DD_GIT_REPOSITORY_URL}
ENV DD_GIT_COMMIT_SHA=${DD_GIT_COMMIT_SHA}

# 安装 gcompat, 解决 glibc 版本问题
RUN apk add --no-cache gcompat tzdata

COPY backend/app/${APP_NAME}/${APP_NAME} /data/server-app
COPY backend/app/${APP_NAME}/config/ /data/config/

RUN ls -la

# 后续在集群内都统一暴露 9090 端口
EXPOSE 9090

ENV MOEGO_ENVIRONMENT=production
CMD ["sh", "-c", "./server-app -config ./config/${MOEGO_ENVIRONMENT}/config.yaml"]