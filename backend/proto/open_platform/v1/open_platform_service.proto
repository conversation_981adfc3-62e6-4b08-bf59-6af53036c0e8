// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 使用company_id作为标识符 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_token --)
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用自定义分页方式 --)
// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用_name后缀作为字段名 --)
// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: 使用empty返回 --)
// (-- api-linter: core::0140::uri=disabled
//     aip.dev/not-precedent: 使用url --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 使用customers --)

syntax = "proto3";

package backend.proto.open_platform.v1;

import "google/protobuf/empty.proto";
import "backend/proto/open_platform/v1/open_platform.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/open_platform/v1;open_platformpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.open_platform.v1";

// OpenPlatformService
service OpenPlatformService {
  // Google Ads 集成相关
  // 获取授权用户相关信息 未授权或授权失效返回特定状态码
  rpc GetGoogleAdsUserInfo(GetGoogleAdsUserInfoRequest) returns (GetGoogleAdsUserInfoResponse);
  // 解除授权
  rpc RevokeGoogleAdsOAuth(RevokeGoogleAdsOAuthRequest) returns (google.protobuf.Empty);
  // 获取授权用户Ads账号列表
  rpc ListGoogleAdsAccounts(ListGoogleAdsAccountsRequest) returns (ListGoogleAdsAccountsResponse);
  // 绑定用户Ads账号
  rpc LinkGoogleAdsAccounts(LinkGoogleAdsAccountsRequest) returns (google.protobuf.Empty);

  // Meta Ads 集成相关
  // 获取授权用户相关信息 未授权或授权失效返回特定状态码
  rpc GetMetaAdsUserInfo(GetMetaAdsUserInfoRequest) returns (GetMetaAdsUserInfoResponse);
  // 解除授权
  rpc RevokeMetaAdsOAuth(RevokeMetaAdsOAuthRequest) returns (google.protobuf.Empty);


  // 获取Link配置
  rpc GetLinkSetting(GetLinkSettingRequest) returns (GetLinkSettingResponse);
}

// GetGoogleAdsUserInfoRequest 获取授权用户相关信息请求
message GetGoogleAdsUserInfoRequest {
  // companyID
  int64 company_id = 1;
}

// GetGoogleAdsUserInfoResponse 获取授权用户相关信息响应
message GetGoogleAdsUserInfoResponse {
  // google user info
  GoogleOAuthUserInfo user_info = 1;
  // google ads setting
  GoogleAdsSetting ads_setting = 2;
}


// RevokeGoogleAdsOAuthRequest 解除授权请求
message RevokeGoogleAdsOAuthRequest {
  // companyID
  int64 company_id = 1;
  // staffID
  int64 staff_id = 2;
}

// ListGoogleAdsAccountsRequest 获取授权用户Ads账号列表请求
message ListGoogleAdsAccountsRequest {
  // companyID
  int64 company_id = 1;
}

// ListGoogleAdsAccountsResponse 获取授权用户Ads账号列表响应
message ListGoogleAdsAccountsResponse {
  // GoogleAdsCustomer
  repeated GoogleAdsCustomer customers = 1;
}

// LinkGoogleAdsAccountsRequest 绑定用户Ads账号请求
message LinkGoogleAdsAccountsRequest {
  // companyID
  int64 company_id = 1;
  // staffID
  int64 staff_id = 2;
  // businessID
  int64 business_id = 3;
  // google ads customer ids
  repeated int64 google_ads_customer_ids = 4;
}

// GetMetaAdsUserInfoRequest 获取授权用户相关信息请求
message GetMetaAdsUserInfoRequest {
  // companyID
  int64 company_id = 1;
}

// GetMetaAdsUserInfoResponse 获取授权用户相关信息响应
message GetMetaAdsUserInfoResponse {
  // meta user info
  MetaOAuthUserInfo user_info = 1;
}

// RevokeMetaAdsOAuthRequest 解除授权请求
message RevokeMetaAdsOAuthRequest {
  // companyID
  int64 company_id = 1;
  // staffID
  int64 staff_id = 2;
}

// GetLinkSettingRequest 获取Link配置请求
message GetLinkSettingRequest {
  // Link类型
  enum LinkType {
    // 未指定
    LINK_TYPE_UNSPECIFIED = 0;
    // Google Ads OAuth2
    LINK_TYPE_GOOGLE_ADS_OAUTH2 = 1;
    // Meta Ads OAuth2
    LINK_TYPE_META_ADS_OAUTH2 = 2;
  }

  // Link类型
  LinkType type = 1;
}

// GetLinkSettingResponse 获取Link配置响应
message GetLinkSettingResponse {
  // link
  string link = 1;
}

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
enum ErrCode {
  // (-- api-linter: core::0126::unspecified=disabled
  //     aip.dev/not-precedent: We need to do this because
  //     the content of the error code is automatically generated by
  //     the script and is exclusive to each service.
  //     Please do not turn off this linter for the rest of the enum --)
  // 成功
  ERR_CODE_OK = 0;
  // 本服务自动分配的全局唯一的起始错误码
  ERR_CODE_UNSPECIFIED = 114700;

  // 授权码未找到
  ERR_CODE_OAUTH_NOT_FOUND = 114701;
  // 授权码失效
  ERR_CODE_OAUTH_EXPIRED = 114702;
}
