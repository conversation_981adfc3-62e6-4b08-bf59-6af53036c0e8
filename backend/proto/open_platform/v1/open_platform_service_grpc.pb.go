// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 使用company_id作为标识符 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_token --)
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用自定义分页方式 --)
// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用_name后缀作为字段名 --)
// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: 使用empty返回 --)
// (-- api-linter: core::0140::uri=disabled
//     aip.dev/not-precedent: 使用url --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 使用customers --)

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/open_platform/v1/open_platform_service.proto

package open_platformpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	OpenPlatformService_GetGoogleAdsUserInfo_FullMethodName  = "/backend.proto.open_platform.v1.OpenPlatformService/GetGoogleAdsUserInfo"
	OpenPlatformService_RevokeGoogleAdsOAuth_FullMethodName  = "/backend.proto.open_platform.v1.OpenPlatformService/RevokeGoogleAdsOAuth"
	OpenPlatformService_ListGoogleAdsAccounts_FullMethodName = "/backend.proto.open_platform.v1.OpenPlatformService/ListGoogleAdsAccounts"
	OpenPlatformService_LinkGoogleAdsAccounts_FullMethodName = "/backend.proto.open_platform.v1.OpenPlatformService/LinkGoogleAdsAccounts"
	OpenPlatformService_GetMetaAdsUserInfo_FullMethodName    = "/backend.proto.open_platform.v1.OpenPlatformService/GetMetaAdsUserInfo"
	OpenPlatformService_RevokeMetaAdsOAuth_FullMethodName    = "/backend.proto.open_platform.v1.OpenPlatformService/RevokeMetaAdsOAuth"
	OpenPlatformService_GetLinkSetting_FullMethodName        = "/backend.proto.open_platform.v1.OpenPlatformService/GetLinkSetting"
)

// OpenPlatformServiceClient is the client API for OpenPlatformService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// OpenPlatformService
type OpenPlatformServiceClient interface {
	// Google Ads 集成相关
	// 获取授权用户相关信息 未授权或授权失效返回特定状态码
	GetGoogleAdsUserInfo(ctx context.Context, in *GetGoogleAdsUserInfoRequest, opts ...grpc.CallOption) (*GetGoogleAdsUserInfoResponse, error)
	// 解除授权
	RevokeGoogleAdsOAuth(ctx context.Context, in *RevokeGoogleAdsOAuthRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取授权用户Ads账号列表
	ListGoogleAdsAccounts(ctx context.Context, in *ListGoogleAdsAccountsRequest, opts ...grpc.CallOption) (*ListGoogleAdsAccountsResponse, error)
	// 绑定用户Ads账号
	LinkGoogleAdsAccounts(ctx context.Context, in *LinkGoogleAdsAccountsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Meta Ads 集成相关
	// 获取授权用户相关信息 未授权或授权失效返回特定状态码
	GetMetaAdsUserInfo(ctx context.Context, in *GetMetaAdsUserInfoRequest, opts ...grpc.CallOption) (*GetMetaAdsUserInfoResponse, error)
	// 解除授权
	RevokeMetaAdsOAuth(ctx context.Context, in *RevokeMetaAdsOAuthRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取Link配置
	GetLinkSetting(ctx context.Context, in *GetLinkSettingRequest, opts ...grpc.CallOption) (*GetLinkSettingResponse, error)
}

type openPlatformServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOpenPlatformServiceClient(cc grpc.ClientConnInterface) OpenPlatformServiceClient {
	return &openPlatformServiceClient{cc}
}

func (c *openPlatformServiceClient) GetGoogleAdsUserInfo(ctx context.Context, in *GetGoogleAdsUserInfoRequest, opts ...grpc.CallOption) (*GetGoogleAdsUserInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGoogleAdsUserInfoResponse)
	err := c.cc.Invoke(ctx, OpenPlatformService_GetGoogleAdsUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *openPlatformServiceClient) RevokeGoogleAdsOAuth(ctx context.Context, in *RevokeGoogleAdsOAuthRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, OpenPlatformService_RevokeGoogleAdsOAuth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *openPlatformServiceClient) ListGoogleAdsAccounts(ctx context.Context, in *ListGoogleAdsAccountsRequest, opts ...grpc.CallOption) (*ListGoogleAdsAccountsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListGoogleAdsAccountsResponse)
	err := c.cc.Invoke(ctx, OpenPlatformService_ListGoogleAdsAccounts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *openPlatformServiceClient) LinkGoogleAdsAccounts(ctx context.Context, in *LinkGoogleAdsAccountsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, OpenPlatformService_LinkGoogleAdsAccounts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *openPlatformServiceClient) GetMetaAdsUserInfo(ctx context.Context, in *GetMetaAdsUserInfoRequest, opts ...grpc.CallOption) (*GetMetaAdsUserInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMetaAdsUserInfoResponse)
	err := c.cc.Invoke(ctx, OpenPlatformService_GetMetaAdsUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *openPlatformServiceClient) RevokeMetaAdsOAuth(ctx context.Context, in *RevokeMetaAdsOAuthRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, OpenPlatformService_RevokeMetaAdsOAuth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *openPlatformServiceClient) GetLinkSetting(ctx context.Context, in *GetLinkSettingRequest, opts ...grpc.CallOption) (*GetLinkSettingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLinkSettingResponse)
	err := c.cc.Invoke(ctx, OpenPlatformService_GetLinkSetting_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OpenPlatformServiceServer is the server API for OpenPlatformService service.
// All implementations must embed UnimplementedOpenPlatformServiceServer
// for forward compatibility.
//
// OpenPlatformService
type OpenPlatformServiceServer interface {
	// Google Ads 集成相关
	// 获取授权用户相关信息 未授权或授权失效返回特定状态码
	GetGoogleAdsUserInfo(context.Context, *GetGoogleAdsUserInfoRequest) (*GetGoogleAdsUserInfoResponse, error)
	// 解除授权
	RevokeGoogleAdsOAuth(context.Context, *RevokeGoogleAdsOAuthRequest) (*emptypb.Empty, error)
	// 获取授权用户Ads账号列表
	ListGoogleAdsAccounts(context.Context, *ListGoogleAdsAccountsRequest) (*ListGoogleAdsAccountsResponse, error)
	// 绑定用户Ads账号
	LinkGoogleAdsAccounts(context.Context, *LinkGoogleAdsAccountsRequest) (*emptypb.Empty, error)
	// Meta Ads 集成相关
	// 获取授权用户相关信息 未授权或授权失效返回特定状态码
	GetMetaAdsUserInfo(context.Context, *GetMetaAdsUserInfoRequest) (*GetMetaAdsUserInfoResponse, error)
	// 解除授权
	RevokeMetaAdsOAuth(context.Context, *RevokeMetaAdsOAuthRequest) (*emptypb.Empty, error)
	// 获取Link配置
	GetLinkSetting(context.Context, *GetLinkSettingRequest) (*GetLinkSettingResponse, error)
	mustEmbedUnimplementedOpenPlatformServiceServer()
}

// UnimplementedOpenPlatformServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedOpenPlatformServiceServer struct{}

func (UnimplementedOpenPlatformServiceServer) GetGoogleAdsUserInfo(context.Context, *GetGoogleAdsUserInfoRequest) (*GetGoogleAdsUserInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoogleAdsUserInfo not implemented")
}
func (UnimplementedOpenPlatformServiceServer) RevokeGoogleAdsOAuth(context.Context, *RevokeGoogleAdsOAuthRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RevokeGoogleAdsOAuth not implemented")
}
func (UnimplementedOpenPlatformServiceServer) ListGoogleAdsAccounts(context.Context, *ListGoogleAdsAccountsRequest) (*ListGoogleAdsAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGoogleAdsAccounts not implemented")
}
func (UnimplementedOpenPlatformServiceServer) LinkGoogleAdsAccounts(context.Context, *LinkGoogleAdsAccountsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkGoogleAdsAccounts not implemented")
}
func (UnimplementedOpenPlatformServiceServer) GetMetaAdsUserInfo(context.Context, *GetMetaAdsUserInfoRequest) (*GetMetaAdsUserInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMetaAdsUserInfo not implemented")
}
func (UnimplementedOpenPlatformServiceServer) RevokeMetaAdsOAuth(context.Context, *RevokeMetaAdsOAuthRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RevokeMetaAdsOAuth not implemented")
}
func (UnimplementedOpenPlatformServiceServer) GetLinkSetting(context.Context, *GetLinkSettingRequest) (*GetLinkSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLinkSetting not implemented")
}
func (UnimplementedOpenPlatformServiceServer) mustEmbedUnimplementedOpenPlatformServiceServer() {}
func (UnimplementedOpenPlatformServiceServer) testEmbeddedByValue()                             {}

// UnsafeOpenPlatformServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OpenPlatformServiceServer will
// result in compilation errors.
type UnsafeOpenPlatformServiceServer interface {
	mustEmbedUnimplementedOpenPlatformServiceServer()
}

func RegisterOpenPlatformServiceServer(s grpc.ServiceRegistrar, srv OpenPlatformServiceServer) {
	// If the following call pancis, it indicates UnimplementedOpenPlatformServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&OpenPlatformService_ServiceDesc, srv)
}

func _OpenPlatformService_GetGoogleAdsUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGoogleAdsUserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OpenPlatformServiceServer).GetGoogleAdsUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OpenPlatformService_GetGoogleAdsUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OpenPlatformServiceServer).GetGoogleAdsUserInfo(ctx, req.(*GetGoogleAdsUserInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OpenPlatformService_RevokeGoogleAdsOAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeGoogleAdsOAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OpenPlatformServiceServer).RevokeGoogleAdsOAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OpenPlatformService_RevokeGoogleAdsOAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OpenPlatformServiceServer).RevokeGoogleAdsOAuth(ctx, req.(*RevokeGoogleAdsOAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OpenPlatformService_ListGoogleAdsAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGoogleAdsAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OpenPlatformServiceServer).ListGoogleAdsAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OpenPlatformService_ListGoogleAdsAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OpenPlatformServiceServer).ListGoogleAdsAccounts(ctx, req.(*ListGoogleAdsAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OpenPlatformService_LinkGoogleAdsAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LinkGoogleAdsAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OpenPlatformServiceServer).LinkGoogleAdsAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OpenPlatformService_LinkGoogleAdsAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OpenPlatformServiceServer).LinkGoogleAdsAccounts(ctx, req.(*LinkGoogleAdsAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OpenPlatformService_GetMetaAdsUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMetaAdsUserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OpenPlatformServiceServer).GetMetaAdsUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OpenPlatformService_GetMetaAdsUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OpenPlatformServiceServer).GetMetaAdsUserInfo(ctx, req.(*GetMetaAdsUserInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OpenPlatformService_RevokeMetaAdsOAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeMetaAdsOAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OpenPlatformServiceServer).RevokeMetaAdsOAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OpenPlatformService_RevokeMetaAdsOAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OpenPlatformServiceServer).RevokeMetaAdsOAuth(ctx, req.(*RevokeMetaAdsOAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OpenPlatformService_GetLinkSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLinkSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OpenPlatformServiceServer).GetLinkSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OpenPlatformService_GetLinkSetting_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OpenPlatformServiceServer).GetLinkSetting(ctx, req.(*GetLinkSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OpenPlatformService_ServiceDesc is the grpc.ServiceDesc for OpenPlatformService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OpenPlatformService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.open_platform.v1.OpenPlatformService",
	HandlerType: (*OpenPlatformServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGoogleAdsUserInfo",
			Handler:    _OpenPlatformService_GetGoogleAdsUserInfo_Handler,
		},
		{
			MethodName: "RevokeGoogleAdsOAuth",
			Handler:    _OpenPlatformService_RevokeGoogleAdsOAuth_Handler,
		},
		{
			MethodName: "ListGoogleAdsAccounts",
			Handler:    _OpenPlatformService_ListGoogleAdsAccounts_Handler,
		},
		{
			MethodName: "LinkGoogleAdsAccounts",
			Handler:    _OpenPlatformService_LinkGoogleAdsAccounts_Handler,
		},
		{
			MethodName: "GetMetaAdsUserInfo",
			Handler:    _OpenPlatformService_GetMetaAdsUserInfo_Handler,
		},
		{
			MethodName: "RevokeMetaAdsOAuth",
			Handler:    _OpenPlatformService_RevokeMetaAdsOAuth_Handler,
		},
		{
			MethodName: "GetLinkSetting",
			Handler:    _OpenPlatformService_GetLinkSetting_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/open_platform/v1/open_platform_service.proto",
}
