// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/tools/v1/testing_service.proto

package toolspb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CleanAppsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CleanAppsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CleanAppsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CleanAppsRequestMultiError, or nil if none found.
func (m *CleanAppsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CleanAppsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPolicies() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CleanAppsRequestValidationError{
						field:  fmt.Sprintf("Policies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CleanAppsRequestValidationError{
						field:  fmt.Sprintf("Policies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CleanAppsRequestValidationError{
					field:  fmt.Sprintf("Policies[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.ValidateOnly != nil {
		// no validation rules for ValidateOnly
	}

	if m.Duration != nil {

		if all {
			switch v := interface{}(m.GetDuration()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CleanAppsRequestValidationError{
						field:  "Duration",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CleanAppsRequestValidationError{
						field:  "Duration",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDuration()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CleanAppsRequestValidationError{
					field:  "Duration",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CleanAppsRequestMultiError(errors)
	}

	return nil
}

// CleanAppsRequestMultiError is an error wrapping multiple validation errors
// returned by CleanAppsRequest.ValidateAll() if the designated constraints
// aren't met.
type CleanAppsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CleanAppsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CleanAppsRequestMultiError) AllErrors() []error { return m }

// CleanAppsRequestValidationError is the validation error returned by
// CleanAppsRequest.Validate if the designated constraints aren't met.
type CleanAppsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CleanAppsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CleanAppsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CleanAppsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CleanAppsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CleanAppsRequestValidationError) ErrorName() string { return "CleanAppsRequestValidationError" }

// Error satisfies the builtin error interface
func (e CleanAppsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCleanAppsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CleanAppsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CleanAppsRequestValidationError{}

// Validate checks the field values on CleanAppsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CleanAppsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CleanAppsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CleanAppsResponseMultiError, or nil if none found.
func (m *CleanAppsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CleanAppsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CleanAppsResponseMultiError(errors)
	}

	return nil
}

// CleanAppsResponseMultiError is an error wrapping multiple validation errors
// returned by CleanAppsResponse.ValidateAll() if the designated constraints
// aren't met.
type CleanAppsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CleanAppsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CleanAppsResponseMultiError) AllErrors() []error { return m }

// CleanAppsResponseValidationError is the validation error returned by
// CleanAppsResponse.Validate if the designated constraints aren't met.
type CleanAppsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CleanAppsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CleanAppsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CleanAppsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CleanAppsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CleanAppsResponseValidationError) ErrorName() string {
	return "CleanAppsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CleanAppsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCleanAppsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CleanAppsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CleanAppsResponseValidationError{}

// Validate checks the field values on CleanServicesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CleanServicesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CleanServicesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CleanServicesRequestMultiError, or nil if none found.
func (m *CleanServicesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CleanServicesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.ValidateOnly != nil {
		// no validation rules for ValidateOnly
	}

	if len(errors) > 0 {
		return CleanServicesRequestMultiError(errors)
	}

	return nil
}

// CleanServicesRequestMultiError is an error wrapping multiple validation
// errors returned by CleanServicesRequest.ValidateAll() if the designated
// constraints aren't met.
type CleanServicesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CleanServicesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CleanServicesRequestMultiError) AllErrors() []error { return m }

// CleanServicesRequestValidationError is the validation error returned by
// CleanServicesRequest.Validate if the designated constraints aren't met.
type CleanServicesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CleanServicesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CleanServicesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CleanServicesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CleanServicesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CleanServicesRequestValidationError) ErrorName() string {
	return "CleanServicesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CleanServicesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCleanServicesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CleanServicesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CleanServicesRequestValidationError{}

// Validate checks the field values on CleanServicesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CleanServicesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CleanServicesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CleanServicesResponseMultiError, or nil if none found.
func (m *CleanServicesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CleanServicesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CleanServicesResponseMultiError(errors)
	}

	return nil
}

// CleanServicesResponseMultiError is an error wrapping multiple validation
// errors returned by CleanServicesResponse.ValidateAll() if the designated
// constraints aren't met.
type CleanServicesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CleanServicesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CleanServicesResponseMultiError) AllErrors() []error { return m }

// CleanServicesResponseValidationError is the validation error returned by
// CleanServicesResponse.Validate if the designated constraints aren't met.
type CleanServicesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CleanServicesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CleanServicesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CleanServicesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CleanServicesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CleanServicesResponseValidationError) ErrorName() string {
	return "CleanServicesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CleanServicesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCleanServicesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CleanServicesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CleanServicesResponseValidationError{}

// Validate checks the field values on CleanEnvoyFiltersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CleanEnvoyFiltersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CleanEnvoyFiltersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CleanEnvoyFiltersRequestMultiError, or nil if none found.
func (m *CleanEnvoyFiltersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CleanEnvoyFiltersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.ValidateOnly != nil {
		// no validation rules for ValidateOnly
	}

	if len(errors) > 0 {
		return CleanEnvoyFiltersRequestMultiError(errors)
	}

	return nil
}

// CleanEnvoyFiltersRequestMultiError is an error wrapping multiple validation
// errors returned by CleanEnvoyFiltersRequest.ValidateAll() if the designated
// constraints aren't met.
type CleanEnvoyFiltersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CleanEnvoyFiltersRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CleanEnvoyFiltersRequestMultiError) AllErrors() []error { return m }

// CleanEnvoyFiltersRequestValidationError is the validation error returned by
// CleanEnvoyFiltersRequest.Validate if the designated constraints aren't met.
type CleanEnvoyFiltersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CleanEnvoyFiltersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CleanEnvoyFiltersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CleanEnvoyFiltersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CleanEnvoyFiltersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CleanEnvoyFiltersRequestValidationError) ErrorName() string {
	return "CleanEnvoyFiltersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CleanEnvoyFiltersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCleanEnvoyFiltersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CleanEnvoyFiltersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CleanEnvoyFiltersRequestValidationError{}

// Validate checks the field values on CleanEnvoyFiltersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CleanEnvoyFiltersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CleanEnvoyFiltersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CleanEnvoyFiltersResponseMultiError, or nil if none found.
func (m *CleanEnvoyFiltersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CleanEnvoyFiltersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CleanEnvoyFiltersResponseMultiError(errors)
	}

	return nil
}

// CleanEnvoyFiltersResponseMultiError is an error wrapping multiple validation
// errors returned by CleanEnvoyFiltersResponse.ValidateAll() if the
// designated constraints aren't met.
type CleanEnvoyFiltersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CleanEnvoyFiltersResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CleanEnvoyFiltersResponseMultiError) AllErrors() []error { return m }

// CleanEnvoyFiltersResponseValidationError is the validation error returned by
// CleanEnvoyFiltersResponse.Validate if the designated constraints aren't met.
type CleanEnvoyFiltersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CleanEnvoyFiltersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CleanEnvoyFiltersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CleanEnvoyFiltersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CleanEnvoyFiltersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CleanEnvoyFiltersResponseValidationError) ErrorName() string {
	return "CleanEnvoyFiltersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CleanEnvoyFiltersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCleanEnvoyFiltersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CleanEnvoyFiltersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CleanEnvoyFiltersResponseValidationError{}

// Validate checks the field values on CleanPodDisruptionBudgetsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CleanPodDisruptionBudgetsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CleanPodDisruptionBudgetsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CleanPodDisruptionBudgetsRequestMultiError, or nil if none found.
func (m *CleanPodDisruptionBudgetsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CleanPodDisruptionBudgetsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.ValidateOnly != nil {
		// no validation rules for ValidateOnly
	}

	if len(errors) > 0 {
		return CleanPodDisruptionBudgetsRequestMultiError(errors)
	}

	return nil
}

// CleanPodDisruptionBudgetsRequestMultiError is an error wrapping multiple
// validation errors returned by
// CleanPodDisruptionBudgetsRequest.ValidateAll() if the designated
// constraints aren't met.
type CleanPodDisruptionBudgetsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CleanPodDisruptionBudgetsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CleanPodDisruptionBudgetsRequestMultiError) AllErrors() []error { return m }

// CleanPodDisruptionBudgetsRequestValidationError is the validation error
// returned by CleanPodDisruptionBudgetsRequest.Validate if the designated
// constraints aren't met.
type CleanPodDisruptionBudgetsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CleanPodDisruptionBudgetsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CleanPodDisruptionBudgetsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CleanPodDisruptionBudgetsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CleanPodDisruptionBudgetsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CleanPodDisruptionBudgetsRequestValidationError) ErrorName() string {
	return "CleanPodDisruptionBudgetsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CleanPodDisruptionBudgetsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCleanPodDisruptionBudgetsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CleanPodDisruptionBudgetsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CleanPodDisruptionBudgetsRequestValidationError{}

// Validate checks the field values on CleanPodDisruptionBudgetsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CleanPodDisruptionBudgetsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CleanPodDisruptionBudgetsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CleanPodDisruptionBudgetsResponseMultiError, or nil if none found.
func (m *CleanPodDisruptionBudgetsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CleanPodDisruptionBudgetsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CleanPodDisruptionBudgetsResponseMultiError(errors)
	}

	return nil
}

// CleanPodDisruptionBudgetsResponseMultiError is an error wrapping multiple
// validation errors returned by
// CleanPodDisruptionBudgetsResponse.ValidateAll() if the designated
// constraints aren't met.
type CleanPodDisruptionBudgetsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CleanPodDisruptionBudgetsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CleanPodDisruptionBudgetsResponseMultiError) AllErrors() []error { return m }

// CleanPodDisruptionBudgetsResponseValidationError is the validation error
// returned by CleanPodDisruptionBudgetsResponse.Validate if the designated
// constraints aren't met.
type CleanPodDisruptionBudgetsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CleanPodDisruptionBudgetsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CleanPodDisruptionBudgetsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CleanPodDisruptionBudgetsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CleanPodDisruptionBudgetsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CleanPodDisruptionBudgetsResponseValidationError) ErrorName() string {
	return "CleanPodDisruptionBudgetsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CleanPodDisruptionBudgetsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCleanPodDisruptionBudgetsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CleanPodDisruptionBudgetsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CleanPodDisruptionBudgetsResponseValidationError{}

// Validate checks the field values on CleanAppsRequest_Policy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CleanAppsRequest_Policy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CleanAppsRequest_Policy with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CleanAppsRequest_PolicyMultiError, or nil if none found.
func (m *CleanAppsRequest_Policy) ValidateAll() error {
	return m.validate(true)
}

func (m *CleanAppsRequest_Policy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetValidityPeriod()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CleanAppsRequest_PolicyValidationError{
					field:  "ValidityPeriod",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CleanAppsRequest_PolicyValidationError{
					field:  "ValidityPeriod",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValidityPeriod()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CleanAppsRequest_PolicyValidationError{
				field:  "ValidityPeriod",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.Branch != nil {
		// no validation rules for Branch
	}

	if len(errors) > 0 {
		return CleanAppsRequest_PolicyMultiError(errors)
	}

	return nil
}

// CleanAppsRequest_PolicyMultiError is an error wrapping multiple validation
// errors returned by CleanAppsRequest_Policy.ValidateAll() if the designated
// constraints aren't met.
type CleanAppsRequest_PolicyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CleanAppsRequest_PolicyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CleanAppsRequest_PolicyMultiError) AllErrors() []error { return m }

// CleanAppsRequest_PolicyValidationError is the validation error returned by
// CleanAppsRequest_Policy.Validate if the designated constraints aren't met.
type CleanAppsRequest_PolicyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CleanAppsRequest_PolicyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CleanAppsRequest_PolicyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CleanAppsRequest_PolicyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CleanAppsRequest_PolicyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CleanAppsRequest_PolicyValidationError) ErrorName() string {
	return "CleanAppsRequest_PolicyValidationError"
}

// Error satisfies the builtin error interface
func (e CleanAppsRequest_PolicyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCleanAppsRequest_Policy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CleanAppsRequest_PolicyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CleanAppsRequest_PolicyValidationError{}

// Validate checks the field values on CleanAppsRequest_ValidityPeriod with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CleanAppsRequest_ValidityPeriod) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CleanAppsRequest_ValidityPeriod with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CleanAppsRequest_ValidityPeriodMultiError, or nil if none found.
func (m *CleanAppsRequest_ValidityPeriod) ValidateAll() error {
	return m.validate(true)
}

func (m *CleanAppsRequest_ValidityPeriod) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Duration != nil {

		if all {
			switch v := interface{}(m.GetDuration()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CleanAppsRequest_ValidityPeriodValidationError{
						field:  "Duration",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CleanAppsRequest_ValidityPeriodValidationError{
						field:  "Duration",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDuration()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CleanAppsRequest_ValidityPeriodValidationError{
					field:  "Duration",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.ExpiryTime != nil {

		if all {
			switch v := interface{}(m.GetExpiryTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CleanAppsRequest_ValidityPeriodValidationError{
						field:  "ExpiryTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CleanAppsRequest_ValidityPeriodValidationError{
						field:  "ExpiryTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExpiryTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CleanAppsRequest_ValidityPeriodValidationError{
					field:  "ExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CleanAppsRequest_ValidityPeriodMultiError(errors)
	}

	return nil
}

// CleanAppsRequest_ValidityPeriodMultiError is an error wrapping multiple
// validation errors returned by CleanAppsRequest_ValidityPeriod.ValidateAll()
// if the designated constraints aren't met.
type CleanAppsRequest_ValidityPeriodMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CleanAppsRequest_ValidityPeriodMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CleanAppsRequest_ValidityPeriodMultiError) AllErrors() []error { return m }

// CleanAppsRequest_ValidityPeriodValidationError is the validation error
// returned by CleanAppsRequest_ValidityPeriod.Validate if the designated
// constraints aren't met.
type CleanAppsRequest_ValidityPeriodValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CleanAppsRequest_ValidityPeriodValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CleanAppsRequest_ValidityPeriodValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CleanAppsRequest_ValidityPeriodValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CleanAppsRequest_ValidityPeriodValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CleanAppsRequest_ValidityPeriodValidationError) ErrorName() string {
	return "CleanAppsRequest_ValidityPeriodValidationError"
}

// Error satisfies the builtin error interface
func (e CleanAppsRequest_ValidityPeriodValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCleanAppsRequest_ValidityPeriod.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CleanAppsRequest_ValidityPeriodValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CleanAppsRequest_ValidityPeriodValidationError{}
