// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/tools/v1/staging_service.proto

package toolspb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RestoreDatabasesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RestoreDatabasesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RestoreDatabasesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RestoreDatabasesRequestMultiError, or nil if none found.
func (m *RestoreDatabasesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RestoreDatabasesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RestoreDatabasesRequestMultiError(errors)
	}

	return nil
}

// RestoreDatabasesRequestMultiError is an error wrapping multiple validation
// errors returned by RestoreDatabasesRequest.ValidateAll() if the designated
// constraints aren't met.
type RestoreDatabasesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RestoreDatabasesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RestoreDatabasesRequestMultiError) AllErrors() []error { return m }

// RestoreDatabasesRequestValidationError is the validation error returned by
// RestoreDatabasesRequest.Validate if the designated constraints aren't met.
type RestoreDatabasesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RestoreDatabasesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RestoreDatabasesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RestoreDatabasesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RestoreDatabasesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RestoreDatabasesRequestValidationError) ErrorName() string {
	return "RestoreDatabasesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RestoreDatabasesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRestoreDatabasesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RestoreDatabasesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RestoreDatabasesRequestValidationError{}

// Validate checks the field values on RestoreDatabasesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RestoreDatabasesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RestoreDatabasesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RestoreDatabasesResponseMultiError, or nil if none found.
func (m *RestoreDatabasesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RestoreDatabasesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDatabases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RestoreDatabasesResponseValidationError{
						field:  fmt.Sprintf("Databases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RestoreDatabasesResponseValidationError{
						field:  fmt.Sprintf("Databases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RestoreDatabasesResponseValidationError{
					field:  fmt.Sprintf("Databases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RestoreDatabasesResponseMultiError(errors)
	}

	return nil
}

// RestoreDatabasesResponseMultiError is an error wrapping multiple validation
// errors returned by RestoreDatabasesResponse.ValidateAll() if the designated
// constraints aren't met.
type RestoreDatabasesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RestoreDatabasesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RestoreDatabasesResponseMultiError) AllErrors() []error { return m }

// RestoreDatabasesResponseValidationError is the validation error returned by
// RestoreDatabasesResponse.Validate if the designated constraints aren't met.
type RestoreDatabasesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RestoreDatabasesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RestoreDatabasesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RestoreDatabasesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RestoreDatabasesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RestoreDatabasesResponseValidationError) ErrorName() string {
	return "RestoreDatabasesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RestoreDatabasesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRestoreDatabasesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RestoreDatabasesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RestoreDatabasesResponseValidationError{}

// Validate checks the field values on ResetDatabaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetDatabaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetDatabaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetDatabaseRequestMultiError, or nil if none found.
func (m *ResetDatabaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetDatabaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResetDatabaseRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResetDatabaseRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResetDatabaseRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSqlRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResetDatabaseRequestValidationError{
						field:  fmt.Sprintf("SqlRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResetDatabaseRequestValidationError{
						field:  fmt.Sprintf("SqlRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResetDatabaseRequestValidationError{
					field:  fmt.Sprintf("SqlRequests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Username != nil {
		// no validation rules for Username
	}

	if m.Password != nil {
		// no validation rules for Password
	}

	if len(errors) > 0 {
		return ResetDatabaseRequestMultiError(errors)
	}

	return nil
}

// ResetDatabaseRequestMultiError is an error wrapping multiple validation
// errors returned by ResetDatabaseRequest.ValidateAll() if the designated
// constraints aren't met.
type ResetDatabaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetDatabaseRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetDatabaseRequestMultiError) AllErrors() []error { return m }

// ResetDatabaseRequestValidationError is the validation error returned by
// ResetDatabaseRequest.Validate if the designated constraints aren't met.
type ResetDatabaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetDatabaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetDatabaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetDatabaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetDatabaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetDatabaseRequestValidationError) ErrorName() string {
	return "ResetDatabaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResetDatabaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetDatabaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetDatabaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetDatabaseRequestValidationError{}

// Validate checks the field values on ResetDatabaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetDatabaseResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetDatabaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetDatabaseResponseMultiError, or nil if none found.
func (m *ResetDatabaseResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetDatabaseResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ResetDatabaseResponseMultiError(errors)
	}

	return nil
}

// ResetDatabaseResponseMultiError is an error wrapping multiple validation
// errors returned by ResetDatabaseResponse.ValidateAll() if the designated
// constraints aren't met.
type ResetDatabaseResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetDatabaseResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetDatabaseResponseMultiError) AllErrors() []error { return m }

// ResetDatabaseResponseValidationError is the validation error returned by
// ResetDatabaseResponse.Validate if the designated constraints aren't met.
type ResetDatabaseResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetDatabaseResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetDatabaseResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetDatabaseResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetDatabaseResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetDatabaseResponseValidationError) ErrorName() string {
	return "ResetDatabaseResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ResetDatabaseResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetDatabaseResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetDatabaseResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetDatabaseResponseValidationError{}

// Validate checks the field values on FlushSecretsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FlushSecretsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FlushSecretsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FlushSecretsRequestMultiError, or nil if none found.
func (m *FlushSecretsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FlushSecretsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetSecrets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FlushSecretsRequestValidationError{
						field:  fmt.Sprintf("Secrets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FlushSecretsRequestValidationError{
						field:  fmt.Sprintf("Secrets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FlushSecretsRequestValidationError{
					field:  fmt.Sprintf("Secrets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FlushSecretsRequestMultiError(errors)
	}

	return nil
}

// FlushSecretsRequestMultiError is an error wrapping multiple validation
// errors returned by FlushSecretsRequest.ValidateAll() if the designated
// constraints aren't met.
type FlushSecretsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FlushSecretsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FlushSecretsRequestMultiError) AllErrors() []error { return m }

// FlushSecretsRequestValidationError is the validation error returned by
// FlushSecretsRequest.Validate if the designated constraints aren't met.
type FlushSecretsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FlushSecretsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FlushSecretsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FlushSecretsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FlushSecretsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FlushSecretsRequestValidationError) ErrorName() string {
	return "FlushSecretsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FlushSecretsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFlushSecretsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FlushSecretsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FlushSecretsRequestValidationError{}

// Validate checks the field values on FlushSecretsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FlushSecretsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FlushSecretsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FlushSecretsResponseMultiError, or nil if none found.
func (m *FlushSecretsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FlushSecretsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Changed

	if len(errors) > 0 {
		return FlushSecretsResponseMultiError(errors)
	}

	return nil
}

// FlushSecretsResponseMultiError is an error wrapping multiple validation
// errors returned by FlushSecretsResponse.ValidateAll() if the designated
// constraints aren't met.
type FlushSecretsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FlushSecretsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FlushSecretsResponseMultiError) AllErrors() []error { return m }

// FlushSecretsResponseValidationError is the validation error returned by
// FlushSecretsResponse.Validate if the designated constraints aren't met.
type FlushSecretsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FlushSecretsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FlushSecretsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FlushSecretsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FlushSecretsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FlushSecretsResponseValidationError) ErrorName() string {
	return "FlushSecretsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FlushSecretsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFlushSecretsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FlushSecretsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FlushSecretsResponseValidationError{}

// Validate checks the field values on FlushRoutesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FlushRoutesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FlushRoutesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FlushRoutesRequestMultiError, or nil if none found.
func (m *FlushRoutesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FlushRoutesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return FlushRoutesRequestMultiError(errors)
	}

	return nil
}

// FlushRoutesRequestMultiError is an error wrapping multiple validation errors
// returned by FlushRoutesRequest.ValidateAll() if the designated constraints
// aren't met.
type FlushRoutesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FlushRoutesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FlushRoutesRequestMultiError) AllErrors() []error { return m }

// FlushRoutesRequestValidationError is the validation error returned by
// FlushRoutesRequest.Validate if the designated constraints aren't met.
type FlushRoutesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FlushRoutesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FlushRoutesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FlushRoutesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FlushRoutesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FlushRoutesRequestValidationError) ErrorName() string {
	return "FlushRoutesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FlushRoutesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFlushRoutesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FlushRoutesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FlushRoutesRequestValidationError{}

// Validate checks the field values on FlushRoutesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FlushRoutesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FlushRoutesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FlushRoutesResponseMultiError, or nil if none found.
func (m *FlushRoutesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FlushRoutesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return FlushRoutesResponseMultiError(errors)
	}

	return nil
}

// FlushRoutesResponseMultiError is an error wrapping multiple validation
// errors returned by FlushRoutesResponse.ValidateAll() if the designated
// constraints aren't met.
type FlushRoutesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FlushRoutesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FlushRoutesResponseMultiError) AllErrors() []error { return m }

// FlushRoutesResponseValidationError is the validation error returned by
// FlushRoutesResponse.Validate if the designated constraints aren't met.
type FlushRoutesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FlushRoutesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FlushRoutesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FlushRoutesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FlushRoutesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FlushRoutesResponseValidationError) ErrorName() string {
	return "FlushRoutesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FlushRoutesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFlushRoutesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FlushRoutesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FlushRoutesResponseValidationError{}

// Validate checks the field values on FlushDatabasesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FlushDatabasesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FlushDatabasesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FlushDatabasesRequestMultiError, or nil if none found.
func (m *FlushDatabasesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FlushDatabasesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDatabases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FlushDatabasesRequestValidationError{
						field:  fmt.Sprintf("Databases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FlushDatabasesRequestValidationError{
						field:  fmt.Sprintf("Databases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FlushDatabasesRequestValidationError{
					field:  fmt.Sprintf("Databases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FlushDatabasesRequestMultiError(errors)
	}

	return nil
}

// FlushDatabasesRequestMultiError is an error wrapping multiple validation
// errors returned by FlushDatabasesRequest.ValidateAll() if the designated
// constraints aren't met.
type FlushDatabasesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FlushDatabasesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FlushDatabasesRequestMultiError) AllErrors() []error { return m }

// FlushDatabasesRequestValidationError is the validation error returned by
// FlushDatabasesRequest.Validate if the designated constraints aren't met.
type FlushDatabasesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FlushDatabasesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FlushDatabasesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FlushDatabasesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FlushDatabasesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FlushDatabasesRequestValidationError) ErrorName() string {
	return "FlushDatabasesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FlushDatabasesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFlushDatabasesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FlushDatabasesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FlushDatabasesRequestValidationError{}

// Validate checks the field values on FlushDatabasesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FlushDatabasesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FlushDatabasesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FlushDatabasesResponseMultiError, or nil if none found.
func (m *FlushDatabasesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FlushDatabasesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Deleted

	// no validation rules for Inserted

	if len(errors) > 0 {
		return FlushDatabasesResponseMultiError(errors)
	}

	return nil
}

// FlushDatabasesResponseMultiError is an error wrapping multiple validation
// errors returned by FlushDatabasesResponse.ValidateAll() if the designated
// constraints aren't met.
type FlushDatabasesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FlushDatabasesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FlushDatabasesResponseMultiError) AllErrors() []error { return m }

// FlushDatabasesResponseValidationError is the validation error returned by
// FlushDatabasesResponse.Validate if the designated constraints aren't met.
type FlushDatabasesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FlushDatabasesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FlushDatabasesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FlushDatabasesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FlushDatabasesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FlushDatabasesResponseValidationError) ErrorName() string {
	return "FlushDatabasesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FlushDatabasesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFlushDatabasesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FlushDatabasesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FlushDatabasesResponseValidationError{}

// Validate checks the field values on ResetDatabaseRequest_SqlRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetDatabaseRequest_SqlRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetDatabaseRequest_SqlRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ResetDatabaseRequest_SqlRequestMultiError, or nil if none found.
func (m *ResetDatabaseRequest_SqlRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetDatabaseRequest_SqlRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Database

	if len(errors) > 0 {
		return ResetDatabaseRequest_SqlRequestMultiError(errors)
	}

	return nil
}

// ResetDatabaseRequest_SqlRequestMultiError is an error wrapping multiple
// validation errors returned by ResetDatabaseRequest_SqlRequest.ValidateAll()
// if the designated constraints aren't met.
type ResetDatabaseRequest_SqlRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetDatabaseRequest_SqlRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetDatabaseRequest_SqlRequestMultiError) AllErrors() []error { return m }

// ResetDatabaseRequest_SqlRequestValidationError is the validation error
// returned by ResetDatabaseRequest_SqlRequest.Validate if the designated
// constraints aren't met.
type ResetDatabaseRequest_SqlRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetDatabaseRequest_SqlRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetDatabaseRequest_SqlRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetDatabaseRequest_SqlRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetDatabaseRequest_SqlRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetDatabaseRequest_SqlRequestValidationError) ErrorName() string {
	return "ResetDatabaseRequest_SqlRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResetDatabaseRequest_SqlRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetDatabaseRequest_SqlRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetDatabaseRequest_SqlRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetDatabaseRequest_SqlRequestValidationError{}

// Validate checks the field values on FlushSecretsRequest_Secret with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FlushSecretsRequest_Secret) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FlushSecretsRequest_Secret with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FlushSecretsRequest_SecretMultiError, or nil if none found.
func (m *FlushSecretsRequest_Secret) ValidateAll() error {
	return m.validate(true)
}

func (m *FlushSecretsRequest_Secret) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Secrets

	if len(errors) > 0 {
		return FlushSecretsRequest_SecretMultiError(errors)
	}

	return nil
}

// FlushSecretsRequest_SecretMultiError is an error wrapping multiple
// validation errors returned by FlushSecretsRequest_Secret.ValidateAll() if
// the designated constraints aren't met.
type FlushSecretsRequest_SecretMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FlushSecretsRequest_SecretMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FlushSecretsRequest_SecretMultiError) AllErrors() []error { return m }

// FlushSecretsRequest_SecretValidationError is the validation error returned
// by FlushSecretsRequest_Secret.Validate if the designated constraints aren't met.
type FlushSecretsRequest_SecretValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FlushSecretsRequest_SecretValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FlushSecretsRequest_SecretValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FlushSecretsRequest_SecretValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FlushSecretsRequest_SecretValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FlushSecretsRequest_SecretValidationError) ErrorName() string {
	return "FlushSecretsRequest_SecretValidationError"
}

// Error satisfies the builtin error interface
func (e FlushSecretsRequest_SecretValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFlushSecretsRequest_Secret.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FlushSecretsRequest_SecretValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FlushSecretsRequest_SecretValidationError{}
