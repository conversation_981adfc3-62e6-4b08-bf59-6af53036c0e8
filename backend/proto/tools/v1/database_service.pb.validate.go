// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/tools/v1/database_service.proto

package toolspb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RegisterDatabaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterDatabaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterDatabaseRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterDatabaseRequestMultiError, or nil if none found.
func (m *RegisterDatabaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterDatabaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterDatabaseRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterDatabaseRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterDatabaseRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.AdminUser != nil {
		// no validation rules for AdminUser
	}

	if m.DefaultUser != nil {
		// no validation rules for DefaultUser
	}

	if len(errors) > 0 {
		return RegisterDatabaseRequestMultiError(errors)
	}

	return nil
}

// RegisterDatabaseRequestMultiError is an error wrapping multiple validation
// errors returned by RegisterDatabaseRequest.ValidateAll() if the designated
// constraints aren't met.
type RegisterDatabaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterDatabaseRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterDatabaseRequestMultiError) AllErrors() []error { return m }

// RegisterDatabaseRequestValidationError is the validation error returned by
// RegisterDatabaseRequest.Validate if the designated constraints aren't met.
type RegisterDatabaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterDatabaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterDatabaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterDatabaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterDatabaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterDatabaseRequestValidationError) ErrorName() string {
	return "RegisterDatabaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterDatabaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterDatabaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterDatabaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterDatabaseRequestValidationError{}

// Validate checks the field values on RegisterDatabaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterDatabaseResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterDatabaseResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterDatabaseResponseMultiError, or nil if none found.
func (m *RegisterDatabaseResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterDatabaseResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Database != nil {

		if all {
			switch v := interface{}(m.GetDatabase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RegisterDatabaseResponseValidationError{
						field:  "Database",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RegisterDatabaseResponseValidationError{
						field:  "Database",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDatabase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RegisterDatabaseResponseValidationError{
					field:  "Database",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RegisterDatabaseResponseMultiError(errors)
	}

	return nil
}

// RegisterDatabaseResponseMultiError is an error wrapping multiple validation
// errors returned by RegisterDatabaseResponse.ValidateAll() if the designated
// constraints aren't met.
type RegisterDatabaseResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterDatabaseResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterDatabaseResponseMultiError) AllErrors() []error { return m }

// RegisterDatabaseResponseValidationError is the validation error returned by
// RegisterDatabaseResponse.Validate if the designated constraints aren't met.
type RegisterDatabaseResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterDatabaseResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterDatabaseResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterDatabaseResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterDatabaseResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterDatabaseResponseValidationError) ErrorName() string {
	return "RegisterDatabaseResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterDatabaseResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterDatabaseResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterDatabaseResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterDatabaseResponseValidationError{}

// Validate checks the field values on ListDatabasesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListDatabasesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListDatabasesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListDatabasesRequestMultiError, or nil if none found.
func (m *ListDatabasesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListDatabasesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetLabels()) > 256 {
		err := ListDatabasesRequestValidationError{
			field:  "Labels",
			reason: "value must contain no more than 256 pair(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	{
		sorted_keys := make([]string, len(m.GetLabels()))
		i := 0
		for key := range m.GetLabels() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetLabels()[key]
			_ = val

			if utf8.RuneCountInString(key) > 255 {
				err := ListDatabasesRequestValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 255 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if utf8.RuneCountInString(val) > 63 {
				err := ListDatabasesRequestValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 63 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}
	}

	if m.Platform != nil {

		if utf8.RuneCountInString(m.GetPlatform()) > 128 {
			err := ListDatabasesRequestValidationError{
				field:  "Platform",
				reason: "value length must be at most 128 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.Engine != nil {

		if utf8.RuneCountInString(m.GetEngine()) > 128 {
			err := ListDatabasesRequestValidationError{
				field:  "Engine",
				reason: "value length must be at most 128 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return ListDatabasesRequestMultiError(errors)
	}

	return nil
}

// ListDatabasesRequestMultiError is an error wrapping multiple validation
// errors returned by ListDatabasesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListDatabasesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListDatabasesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListDatabasesRequestMultiError) AllErrors() []error { return m }

// ListDatabasesRequestValidationError is the validation error returned by
// ListDatabasesRequest.Validate if the designated constraints aren't met.
type ListDatabasesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListDatabasesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListDatabasesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListDatabasesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListDatabasesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListDatabasesRequestValidationError) ErrorName() string {
	return "ListDatabasesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListDatabasesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListDatabasesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListDatabasesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListDatabasesRequestValidationError{}

// Validate checks the field values on ListDatabasesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListDatabasesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListDatabasesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListDatabasesResponseMultiError, or nil if none found.
func (m *ListDatabasesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListDatabasesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDatabases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListDatabasesResponseValidationError{
						field:  fmt.Sprintf("Databases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListDatabasesResponseValidationError{
						field:  fmt.Sprintf("Databases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListDatabasesResponseValidationError{
					field:  fmt.Sprintf("Databases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListDatabasesResponseMultiError(errors)
	}

	return nil
}

// ListDatabasesResponseMultiError is an error wrapping multiple validation
// errors returned by ListDatabasesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListDatabasesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListDatabasesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListDatabasesResponseMultiError) AllErrors() []error { return m }

// ListDatabasesResponseValidationError is the validation error returned by
// ListDatabasesResponse.Validate if the designated constraints aren't met.
type ListDatabasesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListDatabasesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListDatabasesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListDatabasesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListDatabasesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListDatabasesResponseValidationError) ErrorName() string {
	return "ListDatabasesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListDatabasesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListDatabasesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListDatabasesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListDatabasesResponseValidationError{}

// Validate checks the field values on GetDatabaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDatabaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDatabaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDatabaseRequestMultiError, or nil if none found.
func (m *GetDatabaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDatabaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDatabaseRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDatabaseRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDatabaseRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDatabaseRequestMultiError(errors)
	}

	return nil
}

// GetDatabaseRequestMultiError is an error wrapping multiple validation errors
// returned by GetDatabaseRequest.ValidateAll() if the designated constraints
// aren't met.
type GetDatabaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDatabaseRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDatabaseRequestMultiError) AllErrors() []error { return m }

// GetDatabaseRequestValidationError is the validation error returned by
// GetDatabaseRequest.Validate if the designated constraints aren't met.
type GetDatabaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDatabaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDatabaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDatabaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDatabaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDatabaseRequestValidationError) ErrorName() string {
	return "GetDatabaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDatabaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDatabaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDatabaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDatabaseRequestValidationError{}

// Validate checks the field values on GetDatabaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDatabaseResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDatabaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDatabaseResponseMultiError, or nil if none found.
func (m *GetDatabaseResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDatabaseResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Database != nil {

		if all {
			switch v := interface{}(m.GetDatabase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetDatabaseResponseValidationError{
						field:  "Database",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetDatabaseResponseValidationError{
						field:  "Database",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDatabase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetDatabaseResponseValidationError{
					field:  "Database",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetDatabaseResponseMultiError(errors)
	}

	return nil
}

// GetDatabaseResponseMultiError is an error wrapping multiple validation
// errors returned by GetDatabaseResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDatabaseResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDatabaseResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDatabaseResponseMultiError) AllErrors() []error { return m }

// GetDatabaseResponseValidationError is the validation error returned by
// GetDatabaseResponse.Validate if the designated constraints aren't met.
type GetDatabaseResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDatabaseResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDatabaseResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDatabaseResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDatabaseResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDatabaseResponseValidationError) ErrorName() string {
	return "GetDatabaseResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDatabaseResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDatabaseResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDatabaseResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDatabaseResponseValidationError{}

// Validate checks the field values on WatchDatabaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WatchDatabaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WatchDatabaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WatchDatabaseRequestMultiError, or nil if none found.
func (m *WatchDatabaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *WatchDatabaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WatchDatabaseRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WatchDatabaseRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WatchDatabaseRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WatchDatabaseRequestMultiError(errors)
	}

	return nil
}

// WatchDatabaseRequestMultiError is an error wrapping multiple validation
// errors returned by WatchDatabaseRequest.ValidateAll() if the designated
// constraints aren't met.
type WatchDatabaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WatchDatabaseRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WatchDatabaseRequestMultiError) AllErrors() []error { return m }

// WatchDatabaseRequestValidationError is the validation error returned by
// WatchDatabaseRequest.Validate if the designated constraints aren't met.
type WatchDatabaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WatchDatabaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WatchDatabaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WatchDatabaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WatchDatabaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WatchDatabaseRequestValidationError) ErrorName() string {
	return "WatchDatabaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e WatchDatabaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWatchDatabaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WatchDatabaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WatchDatabaseRequestValidationError{}

// Validate checks the field values on WatchDatabaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WatchDatabaseResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WatchDatabaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WatchDatabaseResponseMultiError, or nil if none found.
func (m *WatchDatabaseResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *WatchDatabaseResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDatabase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WatchDatabaseResponseValidationError{
					field:  "Database",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WatchDatabaseResponseValidationError{
					field:  "Database",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDatabase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WatchDatabaseResponseValidationError{
				field:  "Database",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WatchDatabaseResponseMultiError(errors)
	}

	return nil
}

// WatchDatabaseResponseMultiError is an error wrapping multiple validation
// errors returned by WatchDatabaseResponse.ValidateAll() if the designated
// constraints aren't met.
type WatchDatabaseResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WatchDatabaseResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WatchDatabaseResponseMultiError) AllErrors() []error { return m }

// WatchDatabaseResponseValidationError is the validation error returned by
// WatchDatabaseResponse.Validate if the designated constraints aren't met.
type WatchDatabaseResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WatchDatabaseResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WatchDatabaseResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WatchDatabaseResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WatchDatabaseResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WatchDatabaseResponseValidationError) ErrorName() string {
	return "WatchDatabaseResponseValidationError"
}

// Error satisfies the builtin error interface
func (e WatchDatabaseResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWatchDatabaseResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WatchDatabaseResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WatchDatabaseResponseValidationError{}

// Validate checks the field values on DeleteDatabaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteDatabaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDatabaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDatabaseRequestMultiError, or nil if none found.
func (m *DeleteDatabaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDatabaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteDatabaseRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteDatabaseRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteDatabaseRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteDatabaseRequestMultiError(errors)
	}

	return nil
}

// DeleteDatabaseRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteDatabaseRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteDatabaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDatabaseRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDatabaseRequestMultiError) AllErrors() []error { return m }

// DeleteDatabaseRequestValidationError is the validation error returned by
// DeleteDatabaseRequest.Validate if the designated constraints aren't met.
type DeleteDatabaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDatabaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDatabaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDatabaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDatabaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDatabaseRequestValidationError) ErrorName() string {
	return "DeleteDatabaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDatabaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDatabaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDatabaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDatabaseRequestValidationError{}

// Validate checks the field values on DeleteDatabaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteDatabaseResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDatabaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDatabaseResponseMultiError, or nil if none found.
func (m *DeleteDatabaseResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDatabaseResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDatabase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteDatabaseResponseValidationError{
					field:  "Database",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteDatabaseResponseValidationError{
					field:  "Database",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDatabase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteDatabaseResponseValidationError{
				field:  "Database",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteDatabaseResponseMultiError(errors)
	}

	return nil
}

// DeleteDatabaseResponseMultiError is an error wrapping multiple validation
// errors returned by DeleteDatabaseResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteDatabaseResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDatabaseResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDatabaseResponseMultiError) AllErrors() []error { return m }

// DeleteDatabaseResponseValidationError is the validation error returned by
// DeleteDatabaseResponse.Validate if the designated constraints aren't met.
type DeleteDatabaseResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDatabaseResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDatabaseResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDatabaseResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDatabaseResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDatabaseResponseValidationError) ErrorName() string {
	return "DeleteDatabaseResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDatabaseResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDatabaseResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDatabaseResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDatabaseResponseValidationError{}

// Validate checks the field values on RestoreDatabaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RestoreDatabaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RestoreDatabaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RestoreDatabaseRequestMultiError, or nil if none found.
func (m *RestoreDatabaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RestoreDatabaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTargetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RestoreDatabaseRequestValidationError{
					field:  "TargetIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RestoreDatabaseRequestValidationError{
					field:  "TargetIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTargetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RestoreDatabaseRequestValidationError{
				field:  "TargetIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetLabels()) > 256 {
		err := RestoreDatabaseRequestValidationError{
			field:  "Labels",
			reason: "value must contain no more than 256 pair(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	{
		sorted_keys := make([]string, len(m.GetLabels()))
		i := 0
		for key := range m.GetLabels() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetLabels()[key]
			_ = val

			if utf8.RuneCountInString(key) > 255 {
				err := RestoreDatabaseRequestValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 255 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if utf8.RuneCountInString(val) > 63 {
				err := RestoreDatabaseRequestValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 63 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}
	}

	if m.SourceIdentifier != nil {

		if all {
			switch v := interface{}(m.GetSourceIdentifier()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RestoreDatabaseRequestValidationError{
						field:  "SourceIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RestoreDatabaseRequestValidationError{
						field:  "SourceIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSourceIdentifier()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RestoreDatabaseRequestValidationError{
					field:  "SourceIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.DeletionProtection != nil {
		// no validation rules for DeletionProtection
	}

	if m.PubliclyAccessible != nil {
		// no validation rules for PubliclyAccessible
	}

	if m.ParamsGroup != nil {
		// no validation rules for ParamsGroup
	}

	if m.MinCapacity != nil {
		// no validation rules for MinCapacity
	}

	if m.MaxCapacity != nil {
		// no validation rules for MaxCapacity
	}

	if len(errors) > 0 {
		return RestoreDatabaseRequestMultiError(errors)
	}

	return nil
}

// RestoreDatabaseRequestMultiError is an error wrapping multiple validation
// errors returned by RestoreDatabaseRequest.ValidateAll() if the designated
// constraints aren't met.
type RestoreDatabaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RestoreDatabaseRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RestoreDatabaseRequestMultiError) AllErrors() []error { return m }

// RestoreDatabaseRequestValidationError is the validation error returned by
// RestoreDatabaseRequest.Validate if the designated constraints aren't met.
type RestoreDatabaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RestoreDatabaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RestoreDatabaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RestoreDatabaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RestoreDatabaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RestoreDatabaseRequestValidationError) ErrorName() string {
	return "RestoreDatabaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RestoreDatabaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRestoreDatabaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RestoreDatabaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RestoreDatabaseRequestValidationError{}

// Validate checks the field values on RestoreDatabaseResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RestoreDatabaseResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RestoreDatabaseResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RestoreDatabaseResponseMultiError, or nil if none found.
func (m *RestoreDatabaseResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RestoreDatabaseResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDatabase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RestoreDatabaseResponseValidationError{
					field:  "Database",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RestoreDatabaseResponseValidationError{
					field:  "Database",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDatabase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RestoreDatabaseResponseValidationError{
				field:  "Database",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RestoreDatabaseResponseMultiError(errors)
	}

	return nil
}

// RestoreDatabaseResponseMultiError is an error wrapping multiple validation
// errors returned by RestoreDatabaseResponse.ValidateAll() if the designated
// constraints aren't met.
type RestoreDatabaseResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RestoreDatabaseResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RestoreDatabaseResponseMultiError) AllErrors() []error { return m }

// RestoreDatabaseResponseValidationError is the validation error returned by
// RestoreDatabaseResponse.Validate if the designated constraints aren't met.
type RestoreDatabaseResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RestoreDatabaseResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RestoreDatabaseResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RestoreDatabaseResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RestoreDatabaseResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RestoreDatabaseResponseValidationError) ErrorName() string {
	return "RestoreDatabaseResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RestoreDatabaseResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRestoreDatabaseResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RestoreDatabaseResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RestoreDatabaseResponseValidationError{}

// Validate checks the field values on ExecuteSqlRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ExecuteSqlRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteSqlRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExecuteSqlRequestMultiError, or nil if none found.
func (m *ExecuteSqlRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteSqlRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteSqlRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteSqlRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteSqlRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExecuteSqlRequestValidationError{
						field:  fmt.Sprintf("Actions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExecuteSqlRequestValidationError{
						field:  fmt.Sprintf("Actions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExecuteSqlRequestValidationError{
					field:  fmt.Sprintf("Actions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ExecuteSqlRequestMultiError(errors)
	}

	return nil
}

// ExecuteSqlRequestMultiError is an error wrapping multiple validation errors
// returned by ExecuteSqlRequest.ValidateAll() if the designated constraints
// aren't met.
type ExecuteSqlRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteSqlRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteSqlRequestMultiError) AllErrors() []error { return m }

// ExecuteSqlRequestValidationError is the validation error returned by
// ExecuteSqlRequest.Validate if the designated constraints aren't met.
type ExecuteSqlRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteSqlRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteSqlRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteSqlRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteSqlRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteSqlRequestValidationError) ErrorName() string {
	return "ExecuteSqlRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteSqlRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteSqlRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteSqlRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteSqlRequestValidationError{}

// Validate checks the field values on ExecuteSqlResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExecuteSqlResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteSqlResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExecuteSqlResponseMultiError, or nil if none found.
func (m *ExecuteSqlResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteSqlResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteSqlResponseValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteSqlResponseValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteSqlResponseValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOutputs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExecuteSqlResponseValidationError{
						field:  fmt.Sprintf("Outputs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExecuteSqlResponseValidationError{
						field:  fmt.Sprintf("Outputs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExecuteSqlResponseValidationError{
					field:  fmt.Sprintf("Outputs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ExecuteSqlResponseMultiError(errors)
	}

	return nil
}

// ExecuteSqlResponseMultiError is an error wrapping multiple validation errors
// returned by ExecuteSqlResponse.ValidateAll() if the designated constraints
// aren't met.
type ExecuteSqlResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteSqlResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteSqlResponseMultiError) AllErrors() []error { return m }

// ExecuteSqlResponseValidationError is the validation error returned by
// ExecuteSqlResponse.Validate if the designated constraints aren't met.
type ExecuteSqlResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteSqlResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteSqlResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteSqlResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteSqlResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteSqlResponseValidationError) ErrorName() string {
	return "ExecuteSqlResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteSqlResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteSqlResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteSqlResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteSqlResponseValidationError{}

// Validate checks the field values on ExecuteSqlRequest_DatabaseAction with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ExecuteSqlRequest_DatabaseAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteSqlRequest_DatabaseAction with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ExecuteSqlRequest_DatabaseActionMultiError, or nil if none found.
func (m *ExecuteSqlRequest_DatabaseAction) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteSqlRequest_DatabaseAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.ActionType.(type) {
	case *ExecuteSqlRequest_DatabaseAction_CreateDatabase:
		if v == nil {
			err := ExecuteSqlRequest_DatabaseActionValidationError{
				field:  "ActionType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCreateDatabase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExecuteSqlRequest_DatabaseActionValidationError{
						field:  "CreateDatabase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExecuteSqlRequest_DatabaseActionValidationError{
						field:  "CreateDatabase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateDatabase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExecuteSqlRequest_DatabaseActionValidationError{
					field:  "CreateDatabase",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ExecuteSqlRequest_DatabaseAction_ExecuteSql:
		if v == nil {
			err := ExecuteSqlRequest_DatabaseActionValidationError{
				field:  "ActionType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExecuteSql()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExecuteSqlRequest_DatabaseActionValidationError{
						field:  "ExecuteSql",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExecuteSqlRequest_DatabaseActionValidationError{
						field:  "ExecuteSql",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExecuteSql()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExecuteSqlRequest_DatabaseActionValidationError{
					field:  "ExecuteSql",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ExecuteSqlRequest_DatabaseActionMultiError(errors)
	}

	return nil
}

// ExecuteSqlRequest_DatabaseActionMultiError is an error wrapping multiple
// validation errors returned by
// ExecuteSqlRequest_DatabaseAction.ValidateAll() if the designated
// constraints aren't met.
type ExecuteSqlRequest_DatabaseActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteSqlRequest_DatabaseActionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteSqlRequest_DatabaseActionMultiError) AllErrors() []error { return m }

// ExecuteSqlRequest_DatabaseActionValidationError is the validation error
// returned by ExecuteSqlRequest_DatabaseAction.Validate if the designated
// constraints aren't met.
type ExecuteSqlRequest_DatabaseActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteSqlRequest_DatabaseActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteSqlRequest_DatabaseActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteSqlRequest_DatabaseActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteSqlRequest_DatabaseActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteSqlRequest_DatabaseActionValidationError) ErrorName() string {
	return "ExecuteSqlRequest_DatabaseActionValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteSqlRequest_DatabaseActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteSqlRequest_DatabaseAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteSqlRequest_DatabaseActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteSqlRequest_DatabaseActionValidationError{}

// Validate checks the field values on
// ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionMultiError, or nil if
// none found.
func (m *ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Database

	if m.CreateUser != nil {
		// no validation rules for CreateUser
	}

	if m.Username != nil {
		// no validation rules for Username
	}

	if len(errors) > 0 {
		return ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionMultiError(errors)
	}

	return nil
}

// ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionMultiError is an error
// wrapping multiple validation errors returned by
// ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction.ValidateAll() if the
// designated constraints aren't met.
type ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionMultiError) AllErrors() []error {
	return m
}

// ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionValidationError is the
// validation error returned by
// ExecuteSqlRequest_DatabaseAction_CreateDatabaseAction.Validate if the
// designated constraints aren't met.
type ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionValidationError) ErrorName() string {
	return "ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteSqlRequest_DatabaseAction_CreateDatabaseAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteSqlRequest_DatabaseAction_CreateDatabaseActionValidationError{}

// Validate checks the field values on
// ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionMultiError, or nil if none found.
func (m *ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Database

	if utf8.RuneCountInString(m.GetSql()) > 65536 {
		err := ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionValidationError{
			field:  "Sql",
			reason: "value length must be at most 65536 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.Username != nil {
		// no validation rules for Username
	}

	if len(errors) > 0 {
		return ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionMultiError(errors)
	}

	return nil
}

// ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionMultiError is an error
// wrapping multiple validation errors returned by
// ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction.ValidateAll() if the
// designated constraints aren't met.
type ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionMultiError) AllErrors() []error { return m }

// ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionValidationError is the
// validation error returned by
// ExecuteSqlRequest_DatabaseAction_ExecuteSqlAction.Validate if the
// designated constraints aren't met.
type ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionValidationError) ErrorName() string {
	return "ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteSqlRequest_DatabaseAction_ExecuteSqlAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteSqlRequest_DatabaseAction_ExecuteSqlActionValidationError{}

// Validate checks the field values on ExecuteSqlResponse_ActionOutput with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExecuteSqlResponse_ActionOutput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteSqlResponse_ActionOutput with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ExecuteSqlResponse_ActionOutputMultiError, or nil if none found.
func (m *ExecuteSqlResponse_ActionOutput) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteSqlResponse_ActionOutput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Database != nil {
		// no validation rules for Database
	}

	if m.Output != nil {
		// no validation rules for Output
	}

	if m.Extra != nil {

		if all {
			switch v := interface{}(m.GetExtra()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExecuteSqlResponse_ActionOutputValidationError{
						field:  "Extra",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExecuteSqlResponse_ActionOutputValidationError{
						field:  "Extra",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExtra()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExecuteSqlResponse_ActionOutputValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ExecuteSqlResponse_ActionOutputMultiError(errors)
	}

	return nil
}

// ExecuteSqlResponse_ActionOutputMultiError is an error wrapping multiple
// validation errors returned by ExecuteSqlResponse_ActionOutput.ValidateAll()
// if the designated constraints aren't met.
type ExecuteSqlResponse_ActionOutputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteSqlResponse_ActionOutputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteSqlResponse_ActionOutputMultiError) AllErrors() []error { return m }

// ExecuteSqlResponse_ActionOutputValidationError is the validation error
// returned by ExecuteSqlResponse_ActionOutput.Validate if the designated
// constraints aren't met.
type ExecuteSqlResponse_ActionOutputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteSqlResponse_ActionOutputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteSqlResponse_ActionOutputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteSqlResponse_ActionOutputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteSqlResponse_ActionOutputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteSqlResponse_ActionOutputValidationError) ErrorName() string {
	return "ExecuteSqlResponse_ActionOutputValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteSqlResponse_ActionOutputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteSqlResponse_ActionOutput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteSqlResponse_ActionOutputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteSqlResponse_ActionOutputValidationError{}
