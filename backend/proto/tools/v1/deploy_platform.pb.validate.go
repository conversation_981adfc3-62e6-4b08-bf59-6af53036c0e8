// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/tools/v1/deploy_platform.proto

package toolspb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SkipDeployTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SkipDeployTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SkipDeployTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SkipDeployTaskRequestMultiError, or nil if none found.
func (m *SkipDeployTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SkipDeployTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Reason

	if len(errors) > 0 {
		return SkipDeployTaskRequestMultiError(errors)
	}

	return nil
}

// SkipDeployTaskRequestMultiError is an error wrapping multiple validation
// errors returned by SkipDeployTaskRequest.ValidateAll() if the designated
// constraints aren't met.
type SkipDeployTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SkipDeployTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SkipDeployTaskRequestMultiError) AllErrors() []error { return m }

// SkipDeployTaskRequestValidationError is the validation error returned by
// SkipDeployTaskRequest.Validate if the designated constraints aren't met.
type SkipDeployTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SkipDeployTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SkipDeployTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SkipDeployTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SkipDeployTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SkipDeployTaskRequestValidationError) ErrorName() string {
	return "SkipDeployTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SkipDeployTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSkipDeployTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SkipDeployTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SkipDeployTaskRequestValidationError{}

// Validate checks the field values on SkipDeployTaskResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SkipDeployTaskResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SkipDeployTaskResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SkipDeployTaskResponseMultiError, or nil if none found.
func (m *SkipDeployTaskResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SkipDeployTaskResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SkipDeployTaskResponseMultiError(errors)
	}

	return nil
}

// SkipDeployTaskResponseMultiError is an error wrapping multiple validation
// errors returned by SkipDeployTaskResponse.ValidateAll() if the designated
// constraints aren't met.
type SkipDeployTaskResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SkipDeployTaskResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SkipDeployTaskResponseMultiError) AllErrors() []error { return m }

// SkipDeployTaskResponseValidationError is the validation error returned by
// SkipDeployTaskResponse.Validate if the designated constraints aren't met.
type SkipDeployTaskResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SkipDeployTaskResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SkipDeployTaskResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SkipDeployTaskResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SkipDeployTaskResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SkipDeployTaskResponseValidationError) ErrorName() string {
	return "SkipDeployTaskResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SkipDeployTaskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSkipDeployTaskResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SkipDeployTaskResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SkipDeployTaskResponseValidationError{}

// Validate checks the field values on RollbackDeployTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RollbackDeployTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RollbackDeployTaskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RollbackDeployTaskRequestMultiError, or nil if none found.
func (m *RollbackDeployTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RollbackDeployTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for RevisionId

	// no validation rules for Reason

	if len(errors) > 0 {
		return RollbackDeployTaskRequestMultiError(errors)
	}

	return nil
}

// RollbackDeployTaskRequestMultiError is an error wrapping multiple validation
// errors returned by RollbackDeployTaskRequest.ValidateAll() if the
// designated constraints aren't met.
type RollbackDeployTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RollbackDeployTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RollbackDeployTaskRequestMultiError) AllErrors() []error { return m }

// RollbackDeployTaskRequestValidationError is the validation error returned by
// RollbackDeployTaskRequest.Validate if the designated constraints aren't met.
type RollbackDeployTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RollbackDeployTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RollbackDeployTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RollbackDeployTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RollbackDeployTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RollbackDeployTaskRequestValidationError) ErrorName() string {
	return "RollbackDeployTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RollbackDeployTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRollbackDeployTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RollbackDeployTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RollbackDeployTaskRequestValidationError{}

// Validate checks the field values on RollbackDeployTaskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RollbackDeployTaskResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RollbackDeployTaskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RollbackDeployTaskResponseMultiError, or nil if none found.
func (m *RollbackDeployTaskResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RollbackDeployTaskResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RollbackDeployTaskResponseMultiError(errors)
	}

	return nil
}

// RollbackDeployTaskResponseMultiError is an error wrapping multiple
// validation errors returned by RollbackDeployTaskResponse.ValidateAll() if
// the designated constraints aren't met.
type RollbackDeployTaskResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RollbackDeployTaskResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RollbackDeployTaskResponseMultiError) AllErrors() []error { return m }

// RollbackDeployTaskResponseValidationError is the validation error returned
// by RollbackDeployTaskResponse.Validate if the designated constraints aren't met.
type RollbackDeployTaskResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RollbackDeployTaskResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RollbackDeployTaskResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RollbackDeployTaskResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RollbackDeployTaskResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RollbackDeployTaskResponseValidationError) ErrorName() string {
	return "RollbackDeployTaskResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RollbackDeployTaskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRollbackDeployTaskResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RollbackDeployTaskResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RollbackDeployTaskResponseValidationError{}

// Validate checks the field values on HandleCanaryEventWebhookEventRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *HandleCanaryEventWebhookEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleCanaryEventWebhookEventRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// HandleCanaryEventWebhookEventRequestMultiError, or nil if none found.
func (m *HandleCanaryEventWebhookEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleCanaryEventWebhookEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Namespace

	// no validation rules for Phase

	// no validation rules for Checksum

	// no validation rules for BuildId

	// no validation rules for Metadata

	if m.Type != nil {
		// no validation rules for Type
	}

	if m.FailedChecks != nil {
		// no validation rules for FailedChecks
	}

	if m.CanaryWeight != nil {
		// no validation rules for CanaryWeight
	}

	if m.Iterations != nil {
		// no validation rules for Iterations
	}

	if m.Remaining != nil {
		// no validation rules for Remaining
	}

	if len(errors) > 0 {
		return HandleCanaryEventWebhookEventRequestMultiError(errors)
	}

	return nil
}

// HandleCanaryEventWebhookEventRequestMultiError is an error wrapping multiple
// validation errors returned by
// HandleCanaryEventWebhookEventRequest.ValidateAll() if the designated
// constraints aren't met.
type HandleCanaryEventWebhookEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleCanaryEventWebhookEventRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleCanaryEventWebhookEventRequestMultiError) AllErrors() []error { return m }

// HandleCanaryEventWebhookEventRequestValidationError is the validation error
// returned by HandleCanaryEventWebhookEventRequest.Validate if the designated
// constraints aren't met.
type HandleCanaryEventWebhookEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleCanaryEventWebhookEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleCanaryEventWebhookEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleCanaryEventWebhookEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleCanaryEventWebhookEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleCanaryEventWebhookEventRequestValidationError) ErrorName() string {
	return "HandleCanaryEventWebhookEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e HandleCanaryEventWebhookEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleCanaryEventWebhookEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleCanaryEventWebhookEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleCanaryEventWebhookEventRequestValidationError{}

// Validate checks the field values on HandleCanaryEventWebhookEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *HandleCanaryEventWebhookEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleCanaryEventWebhookEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// HandleCanaryEventWebhookEventResponseMultiError, or nil if none found.
func (m *HandleCanaryEventWebhookEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleCanaryEventWebhookEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return HandleCanaryEventWebhookEventResponseMultiError(errors)
	}

	return nil
}

// HandleCanaryEventWebhookEventResponseMultiError is an error wrapping
// multiple validation errors returned by
// HandleCanaryEventWebhookEventResponse.ValidateAll() if the designated
// constraints aren't met.
type HandleCanaryEventWebhookEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleCanaryEventWebhookEventResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleCanaryEventWebhookEventResponseMultiError) AllErrors() []error { return m }

// HandleCanaryEventWebhookEventResponseValidationError is the validation error
// returned by HandleCanaryEventWebhookEventResponse.Validate if the
// designated constraints aren't met.
type HandleCanaryEventWebhookEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleCanaryEventWebhookEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleCanaryEventWebhookEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleCanaryEventWebhookEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleCanaryEventWebhookEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleCanaryEventWebhookEventResponseValidationError) ErrorName() string {
	return "HandleCanaryEventWebhookEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e HandleCanaryEventWebhookEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleCanaryEventWebhookEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleCanaryEventWebhookEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleCanaryEventWebhookEventResponseValidationError{}

// Validate checks the field values on HandleCanaryWebhookEventRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleCanaryWebhookEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleCanaryWebhookEventRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// HandleCanaryWebhookEventRequestMultiError, or nil if none found.
func (m *HandleCanaryWebhookEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleCanaryWebhookEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Namespace

	// no validation rules for Phase

	// no validation rules for Checksum

	// no validation rules for BuildId

	// no validation rules for Type

	// no validation rules for Metadata

	if len(errors) > 0 {
		return HandleCanaryWebhookEventRequestMultiError(errors)
	}

	return nil
}

// HandleCanaryWebhookEventRequestMultiError is an error wrapping multiple
// validation errors returned by HandleCanaryWebhookEventRequest.ValidateAll()
// if the designated constraints aren't met.
type HandleCanaryWebhookEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleCanaryWebhookEventRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleCanaryWebhookEventRequestMultiError) AllErrors() []error { return m }

// HandleCanaryWebhookEventRequestValidationError is the validation error
// returned by HandleCanaryWebhookEventRequest.Validate if the designated
// constraints aren't met.
type HandleCanaryWebhookEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleCanaryWebhookEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleCanaryWebhookEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleCanaryWebhookEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleCanaryWebhookEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleCanaryWebhookEventRequestValidationError) ErrorName() string {
	return "HandleCanaryWebhookEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e HandleCanaryWebhookEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleCanaryWebhookEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleCanaryWebhookEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleCanaryWebhookEventRequestValidationError{}

// Validate checks the field values on HandleCanaryWebhookEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *HandleCanaryWebhookEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleCanaryWebhookEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// HandleCanaryWebhookEventResponseMultiError, or nil if none found.
func (m *HandleCanaryWebhookEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleCanaryWebhookEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return HandleCanaryWebhookEventResponseMultiError(errors)
	}

	return nil
}

// HandleCanaryWebhookEventResponseMultiError is an error wrapping multiple
// validation errors returned by
// HandleCanaryWebhookEventResponse.ValidateAll() if the designated
// constraints aren't met.
type HandleCanaryWebhookEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleCanaryWebhookEventResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleCanaryWebhookEventResponseMultiError) AllErrors() []error { return m }

// HandleCanaryWebhookEventResponseValidationError is the validation error
// returned by HandleCanaryWebhookEventResponse.Validate if the designated
// constraints aren't met.
type HandleCanaryWebhookEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleCanaryWebhookEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleCanaryWebhookEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleCanaryWebhookEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleCanaryWebhookEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleCanaryWebhookEventResponseValidationError) ErrorName() string {
	return "HandleCanaryWebhookEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e HandleCanaryWebhookEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleCanaryWebhookEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleCanaryWebhookEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleCanaryWebhookEventResponseValidationError{}

// Validate checks the field values on DeployTask with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeployTask) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeployTask with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeployTaskMultiError, or
// nil if none found.
func (m *DeployTask) ValidateAll() error {
	return m.validate(true)
}

func (m *DeployTask) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Title

	// no validation rules for Description

	// no validation rules for Creator

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeployTaskValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeployTaskValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeployTaskValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeployTaskValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeployTaskValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeployTaskValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for State

	// no validation rules for CurrentPhase

	// no validation rules for Parameters

	for idx, item := range m.GetPhases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeployTaskValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeployTaskValidationError{
						field:  fmt.Sprintf("Phases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeployTaskValidationError{
					field:  fmt.Sprintf("Phases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetLogs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeployTaskValidationError{
						field:  fmt.Sprintf("Logs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeployTaskValidationError{
						field:  fmt.Sprintf("Logs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeployTaskValidationError{
					field:  fmt.Sprintf("Logs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DeployTaskMultiError(errors)
	}

	return nil
}

// DeployTaskMultiError is an error wrapping multiple validation errors
// returned by DeployTask.ValidateAll() if the designated constraints aren't met.
type DeployTaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeployTaskMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeployTaskMultiError) AllErrors() []error { return m }

// DeployTaskValidationError is the validation error returned by
// DeployTask.Validate if the designated constraints aren't met.
type DeployTaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeployTaskValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeployTaskValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeployTaskValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeployTaskValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeployTaskValidationError) ErrorName() string { return "DeployTaskValidationError" }

// Error satisfies the builtin error interface
func (e DeployTaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeployTask.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeployTaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeployTaskValidationError{}

// Validate checks the field values on HandleSlackInteractionsEventRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *HandleSlackInteractionsEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleSlackInteractionsEventRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// HandleSlackInteractionsEventRequestMultiError, or nil if none found.
func (m *HandleSlackInteractionsEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleSlackInteractionsEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Payload

	if len(errors) > 0 {
		return HandleSlackInteractionsEventRequestMultiError(errors)
	}

	return nil
}

// HandleSlackInteractionsEventRequestMultiError is an error wrapping multiple
// validation errors returned by
// HandleSlackInteractionsEventRequest.ValidateAll() if the designated
// constraints aren't met.
type HandleSlackInteractionsEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleSlackInteractionsEventRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleSlackInteractionsEventRequestMultiError) AllErrors() []error { return m }

// HandleSlackInteractionsEventRequestValidationError is the validation error
// returned by HandleSlackInteractionsEventRequest.Validate if the designated
// constraints aren't met.
type HandleSlackInteractionsEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleSlackInteractionsEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleSlackInteractionsEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleSlackInteractionsEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleSlackInteractionsEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleSlackInteractionsEventRequestValidationError) ErrorName() string {
	return "HandleSlackInteractionsEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e HandleSlackInteractionsEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleSlackInteractionsEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleSlackInteractionsEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleSlackInteractionsEventRequestValidationError{}

// Validate checks the field values on HandleSlackInteractionsEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *HandleSlackInteractionsEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleSlackInteractionsEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// HandleSlackInteractionsEventResponseMultiError, or nil if none found.
func (m *HandleSlackInteractionsEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleSlackInteractionsEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return HandleSlackInteractionsEventResponseMultiError(errors)
	}

	return nil
}

// HandleSlackInteractionsEventResponseMultiError is an error wrapping multiple
// validation errors returned by
// HandleSlackInteractionsEventResponse.ValidateAll() if the designated
// constraints aren't met.
type HandleSlackInteractionsEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleSlackInteractionsEventResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleSlackInteractionsEventResponseMultiError) AllErrors() []error { return m }

// HandleSlackInteractionsEventResponseValidationError is the validation error
// returned by HandleSlackInteractionsEventResponse.Validate if the designated
// constraints aren't met.
type HandleSlackInteractionsEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleSlackInteractionsEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleSlackInteractionsEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleSlackInteractionsEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleSlackInteractionsEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleSlackInteractionsEventResponseValidationError) ErrorName() string {
	return "HandleSlackInteractionsEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e HandleSlackInteractionsEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleSlackInteractionsEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleSlackInteractionsEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleSlackInteractionsEventResponseValidationError{}

// Validate checks the field values on DeployTask_DeployPhase with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeployTask_DeployPhase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeployTask_DeployPhase with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeployTask_DeployPhaseMultiError, or nil if none found.
func (m *DeployTask_DeployPhase) ValidateAll() error {
	return m.validate(true)
}

func (m *DeployTask_DeployPhase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for TaskId

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for State

	// no validation rules for Parameters

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeployTask_DeployPhaseValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeployTask_DeployPhaseValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeployTask_DeployPhaseValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.EndTime != nil {

		if all {
			switch v := interface{}(m.GetEndTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeployTask_DeployPhaseValidationError{
						field:  "EndTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeployTask_DeployPhaseValidationError{
						field:  "EndTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeployTask_DeployPhaseValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Reason != nil {
		// no validation rules for Reason
	}

	if len(errors) > 0 {
		return DeployTask_DeployPhaseMultiError(errors)
	}

	return nil
}

// DeployTask_DeployPhaseMultiError is an error wrapping multiple validation
// errors returned by DeployTask_DeployPhase.ValidateAll() if the designated
// constraints aren't met.
type DeployTask_DeployPhaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeployTask_DeployPhaseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeployTask_DeployPhaseMultiError) AllErrors() []error { return m }

// DeployTask_DeployPhaseValidationError is the validation error returned by
// DeployTask_DeployPhase.Validate if the designated constraints aren't met.
type DeployTask_DeployPhaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeployTask_DeployPhaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeployTask_DeployPhaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeployTask_DeployPhaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeployTask_DeployPhaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeployTask_DeployPhaseValidationError) ErrorName() string {
	return "DeployTask_DeployPhaseValidationError"
}

// Error satisfies the builtin error interface
func (e DeployTask_DeployPhaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeployTask_DeployPhase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeployTask_DeployPhaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeployTask_DeployPhaseValidationError{}

// Validate checks the field values on DeployTask_DeployLog with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeployTask_DeployLog) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeployTask_DeployLog with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeployTask_DeployLogMultiError, or nil if none found.
func (m *DeployTask_DeployLog) ValidateAll() error {
	return m.validate(true)
}

func (m *DeployTask_DeployLog) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for TaskId

	if all {
		switch v := interface{}(m.GetLogTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeployTask_DeployLogValidationError{
					field:  "LogTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeployTask_DeployLogValidationError{
					field:  "LogTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLogTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeployTask_DeployLogValidationError{
				field:  "LogTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	// no validation rules for State

	// no validation rules for Phase

	// no validation rules for Message

	if len(errors) > 0 {
		return DeployTask_DeployLogMultiError(errors)
	}

	return nil
}

// DeployTask_DeployLogMultiError is an error wrapping multiple validation
// errors returned by DeployTask_DeployLog.ValidateAll() if the designated
// constraints aren't met.
type DeployTask_DeployLogMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeployTask_DeployLogMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeployTask_DeployLogMultiError) AllErrors() []error { return m }

// DeployTask_DeployLogValidationError is the validation error returned by
// DeployTask_DeployLog.Validate if the designated constraints aren't met.
type DeployTask_DeployLogValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeployTask_DeployLogValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeployTask_DeployLogValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeployTask_DeployLogValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeployTask_DeployLogValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeployTask_DeployLogValidationError) ErrorName() string {
	return "DeployTask_DeployLogValidationError"
}

// Error satisfies the builtin error interface
func (e DeployTask_DeployLogValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeployTask_DeployLog.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeployTask_DeployLogValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeployTask_DeployLogValidationError{}
