// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/tools/v1/resource_models.proto

package toolspb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PlatformIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PlatformIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlatformIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PlatformIdentifierMultiError, or nil if none found.
func (m *PlatformIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *PlatformIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPlatform()) > 128 {
		err := PlatformIdentifierValidationError{
			field:  "Platform",
			reason: "value length must be at most 128 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetIdentifier()) > 1024 {
		err := PlatformIdentifierValidationError{
			field:  "Identifier",
			reason: "value length must be at most 1024 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return PlatformIdentifierMultiError(errors)
	}

	return nil
}

// PlatformIdentifierMultiError is an error wrapping multiple validation errors
// returned by PlatformIdentifier.ValidateAll() if the designated constraints
// aren't met.
type PlatformIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlatformIdentifierMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlatformIdentifierMultiError) AllErrors() []error { return m }

// PlatformIdentifierValidationError is the validation error returned by
// PlatformIdentifier.Validate if the designated constraints aren't met.
type PlatformIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlatformIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlatformIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlatformIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlatformIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlatformIdentifierValidationError) ErrorName() string {
	return "PlatformIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e PlatformIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlatformIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlatformIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlatformIdentifierValidationError{}

// Validate checks the field values on EnvironmentIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnvironmentIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnvironmentIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnvironmentIdentifierMultiError, or nil if none found.
func (m *EnvironmentIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *EnvironmentIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetCluster()) > 63 {
		err := EnvironmentIdentifierValidationError{
			field:  "Cluster",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetNamespace()) > 63 {
		err := EnvironmentIdentifierValidationError{
			field:  "Namespace",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return EnvironmentIdentifierMultiError(errors)
	}

	return nil
}

// EnvironmentIdentifierMultiError is an error wrapping multiple validation
// errors returned by EnvironmentIdentifier.ValidateAll() if the designated
// constraints aren't met.
type EnvironmentIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnvironmentIdentifierMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnvironmentIdentifierMultiError) AllErrors() []error { return m }

// EnvironmentIdentifierValidationError is the validation error returned by
// EnvironmentIdentifier.Validate if the designated constraints aren't met.
type EnvironmentIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnvironmentIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnvironmentIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnvironmentIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnvironmentIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnvironmentIdentifierValidationError) ErrorName() string {
	return "EnvironmentIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e EnvironmentIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnvironmentIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnvironmentIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnvironmentIdentifierValidationError{}

// Validate checks the field values on ResourceIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResourceIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResourceIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResourceIdentifierMultiError, or nil if none found.
func (m *ResourceIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *ResourceIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Kind

	// no validation rules for Name

	if m.ApiVersion != nil {
		// no validation rules for ApiVersion
	}

	if len(errors) > 0 {
		return ResourceIdentifierMultiError(errors)
	}

	return nil
}

// ResourceIdentifierMultiError is an error wrapping multiple validation errors
// returned by ResourceIdentifier.ValidateAll() if the designated constraints
// aren't met.
type ResourceIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourceIdentifierMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourceIdentifierMultiError) AllErrors() []error { return m }

// ResourceIdentifierValidationError is the validation error returned by
// ResourceIdentifier.Validate if the designated constraints aren't met.
type ResourceIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourceIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourceIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourceIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourceIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourceIdentifierValidationError) ErrorName() string {
	return "ResourceIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e ResourceIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResourceIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourceIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourceIdentifierValidationError{}

// Validate checks the field values on Endpoint with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Endpoint) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Endpoint with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EndpointMultiError, or nil
// if none found.
func (m *Endpoint) ValidateAll() error {
	return m.validate(true)
}

func (m *Endpoint) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetAddress()) > 1024 {
		err := EndpointValidationError{
			field:  "Address",
			reason: "value length must be at most 1024 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPort() >= 65536 {
		err := EndpointValidationError{
			field:  "Port",
			reason: "value must be less than 65536",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return EndpointMultiError(errors)
	}

	return nil
}

// EndpointMultiError is an error wrapping multiple validation errors returned
// by Endpoint.ValidateAll() if the designated constraints aren't met.
type EndpointMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EndpointMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EndpointMultiError) AllErrors() []error { return m }

// EndpointValidationError is the validation error returned by
// Endpoint.Validate if the designated constraints aren't met.
type EndpointValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EndpointValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EndpointValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EndpointValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EndpointValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EndpointValidationError) ErrorName() string { return "EndpointValidationError" }

// Error satisfies the builtin error interface
func (e EndpointValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEndpoint.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EndpointValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EndpointValidationError{}

// Validate checks the field values on Environment with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Environment) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Environment with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EnvironmentMultiError, or
// nil if none found.
func (m *Environment) ValidateAll() error {
	return m.validate(true)
}

func (m *Environment) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnvironmentValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnvironmentValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnvironmentValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for IsManaged

	for idx, item := range m.GetDatabases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EnvironmentValidationError{
						field:  fmt.Sprintf("Databases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EnvironmentValidationError{
						field:  fmt.Sprintf("Databases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EnvironmentValidationError{
					field:  fmt.Sprintf("Databases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCaches() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EnvironmentValidationError{
						field:  fmt.Sprintf("Caches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EnvironmentValidationError{
						field:  fmt.Sprintf("Caches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EnvironmentValidationError{
					field:  fmt.Sprintf("Caches[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMessageQueues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EnvironmentValidationError{
						field:  fmt.Sprintf("MessageQueues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EnvironmentValidationError{
						field:  fmt.Sprintf("MessageQueues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EnvironmentValidationError{
					field:  fmt.Sprintf("MessageQueues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Name != nil {

		if utf8.RuneCountInString(m.GetName()) > 64 {
			err := EnvironmentValidationError{
				field:  "Name",
				reason: "value length must be at most 64 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.Description != nil {

		if utf8.RuneCountInString(m.GetDescription()) > 1024 {
			err := EnvironmentValidationError{
				field:  "Description",
				reason: "value length must be at most 1024 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.Extra != nil {

		if all {
			switch v := interface{}(m.GetExtra()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EnvironmentValidationError{
						field:  "Extra",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EnvironmentValidationError{
						field:  "Extra",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExtra()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EnvironmentValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.CreateTime != nil {

		if all {
			switch v := interface{}(m.GetCreateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EnvironmentValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EnvironmentValidationError{
						field:  "CreateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EnvironmentValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.UpdateTime != nil {

		if all {
			switch v := interface{}(m.GetUpdateTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EnvironmentValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EnvironmentValidationError{
						field:  "UpdateTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EnvironmentValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.DeleteTime != nil {

		if all {
			switch v := interface{}(m.GetDeleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EnvironmentValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EnvironmentValidationError{
						field:  "DeleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EnvironmentValidationError{
					field:  "DeleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EnvironmentMultiError(errors)
	}

	return nil
}

// EnvironmentMultiError is an error wrapping multiple validation errors
// returned by Environment.ValidateAll() if the designated constraints aren't met.
type EnvironmentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnvironmentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnvironmentMultiError) AllErrors() []error { return m }

// EnvironmentValidationError is the validation error returned by
// Environment.Validate if the designated constraints aren't met.
type EnvironmentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnvironmentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnvironmentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnvironmentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnvironmentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnvironmentValidationError) ErrorName() string { return "EnvironmentValidationError" }

// Error satisfies the builtin error interface
func (e EnvironmentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnvironment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnvironmentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnvironmentValidationError{}

// Validate checks the field values on Cluster with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Cluster) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Cluster with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ClusterMultiError, or nil if none found.
func (m *Cluster) ValidateAll() error {
	return m.validate(true)
}

func (m *Cluster) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClusterValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClusterValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClusterValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Version

	// no validation rules for Endpoint

	// no validation rules for Status

	// no validation rules for Labels

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClusterValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClusterValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClusterValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ClusterMultiError(errors)
	}

	return nil
}

// ClusterMultiError is an error wrapping multiple validation errors returned
// by Cluster.ValidateAll() if the designated constraints aren't met.
type ClusterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClusterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClusterMultiError) AllErrors() []error { return m }

// ClusterValidationError is the validation error returned by Cluster.Validate
// if the designated constraints aren't met.
type ClusterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClusterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClusterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClusterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClusterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClusterValidationError) ErrorName() string { return "ClusterValidationError" }

// Error satisfies the builtin error interface
func (e ClusterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCluster.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClusterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClusterValidationError{}

// Validate checks the field values on NodeGroup with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NodeGroup) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NodeGroup with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NodeGroupMultiError, or nil
// if none found.
func (m *NodeGroup) ValidateAll() error {
	return m.validate(true)
}

func (m *NodeGroup) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NodeGroupValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NodeGroupValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NodeGroupValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for InstanceClass

	// no validation rules for Image

	if len(m.GetLabels()) > 256 {
		err := NodeGroupValidationError{
			field:  "Labels",
			reason: "value must contain no more than 256 pair(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	{
		sorted_keys := make([]string, len(m.GetLabels()))
		i := 0
		for key := range m.GetLabels() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetLabels()[key]
			_ = val

			if utf8.RuneCountInString(key) > 255 {
				err := NodeGroupValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 255 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if utf8.RuneCountInString(val) > 63 {
				err := NodeGroupValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 63 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}
	}

	if all {
		switch v := interface{}(m.GetExtra()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NodeGroupValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NodeGroupValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtra()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NodeGroupValidationError{
				field:  "Extra",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NodeGroupValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NodeGroupValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NodeGroupValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NodeGroupValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NodeGroupValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NodeGroupValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.MinSize != nil {
		// no validation rules for MinSize
	}

	if m.MaxSize != nil {
		// no validation rules for MaxSize
	}

	if m.DesiredSize != nil {
		// no validation rules for DesiredSize
	}

	if m.DiskSize != nil {
		// no validation rules for DiskSize
	}

	if len(errors) > 0 {
		return NodeGroupMultiError(errors)
	}

	return nil
}

// NodeGroupMultiError is an error wrapping multiple validation errors returned
// by NodeGroup.ValidateAll() if the designated constraints aren't met.
type NodeGroupMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NodeGroupMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NodeGroupMultiError) AllErrors() []error { return m }

// NodeGroupValidationError is the validation error returned by
// NodeGroup.Validate if the designated constraints aren't met.
type NodeGroupValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NodeGroupValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NodeGroupValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NodeGroupValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NodeGroupValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NodeGroupValidationError) ErrorName() string { return "NodeGroupValidationError" }

// Error satisfies the builtin error interface
func (e NodeGroupValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNodeGroup.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NodeGroupValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NodeGroupValidationError{}

// Validate checks the field values on Database with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Database) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Database with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DatabaseMultiError, or nil
// if none found.
func (m *Database) ValidateAll() error {
	return m.validate(true)
}

func (m *Database) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DatabaseValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DatabaseValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DatabaseValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for Engine

	// no validation rules for Version

	for idx, item := range m.GetEndpoints() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DatabaseValidationError{
						field:  fmt.Sprintf("Endpoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DatabaseValidationError{
						field:  fmt.Sprintf("Endpoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DatabaseValidationError{
					field:  fmt.Sprintf("Endpoints[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Port

	// no validation rules for InstanceClass

	// no validation rules for AllocatedSize

	// no validation rules for PubliclyAccessible

	if len(m.GetLabels()) > 256 {
		err := DatabaseValidationError{
			field:  "Labels",
			reason: "value must contain no more than 256 pair(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	{
		sorted_keys := make([]string, len(m.GetLabels()))
		i := 0
		for key := range m.GetLabels() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetLabels()[key]
			_ = val

			if utf8.RuneCountInString(key) > 255 {
				err := DatabaseValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 255 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if utf8.RuneCountInString(val) > 63 {
				err := DatabaseValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 63 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}
	}

	if all {
		switch v := interface{}(m.GetExtra()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DatabaseValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DatabaseValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtra()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DatabaseValidationError{
				field:  "Extra",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DatabaseValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DatabaseValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DatabaseValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DatabaseValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DatabaseValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DatabaseValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DatabaseMultiError(errors)
	}

	return nil
}

// DatabaseMultiError is an error wrapping multiple validation errors returned
// by Database.ValidateAll() if the designated constraints aren't met.
type DatabaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DatabaseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DatabaseMultiError) AllErrors() []error { return m }

// DatabaseValidationError is the validation error returned by
// Database.Validate if the designated constraints aren't met.
type DatabaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DatabaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DatabaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DatabaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DatabaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DatabaseValidationError) ErrorName() string { return "DatabaseValidationError" }

// Error satisfies the builtin error interface
func (e DatabaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDatabase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DatabaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DatabaseValidationError{}

// Validate checks the field values on Cache with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Cache) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Cache with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CacheMultiError, or nil if none found.
func (m *Cache) ValidateAll() error {
	return m.validate(true)
}

func (m *Cache) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CacheValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CacheValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CacheValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for Engine

	// no validation rules for Version

	for idx, item := range m.GetEndpoints() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CacheValidationError{
						field:  fmt.Sprintf("Endpoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CacheValidationError{
						field:  fmt.Sprintf("Endpoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CacheValidationError{
					field:  fmt.Sprintf("Endpoints[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Port

	// no validation rules for InstanceClass

	// no validation rules for AllocatedSize

	// no validation rules for PubliclyAccessible

	// no validation rules for Tls

	if len(m.GetLabels()) > 256 {
		err := CacheValidationError{
			field:  "Labels",
			reason: "value must contain no more than 256 pair(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	{
		sorted_keys := make([]string, len(m.GetLabels()))
		i := 0
		for key := range m.GetLabels() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetLabels()[key]
			_ = val

			if utf8.RuneCountInString(key) > 255 {
				err := CacheValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 255 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if utf8.RuneCountInString(val) > 63 {
				err := CacheValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 63 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}
	}

	if all {
		switch v := interface{}(m.GetExtra()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CacheValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CacheValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtra()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CacheValidationError{
				field:  "Extra",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CacheValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CacheValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CacheValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CacheValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CacheValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CacheValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CacheMultiError(errors)
	}

	return nil
}

// CacheMultiError is an error wrapping multiple validation errors returned by
// Cache.ValidateAll() if the designated constraints aren't met.
type CacheMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CacheMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CacheMultiError) AllErrors() []error { return m }

// CacheValidationError is the validation error returned by Cache.Validate if
// the designated constraints aren't met.
type CacheValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CacheValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CacheValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CacheValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CacheValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CacheValidationError) ErrorName() string { return "CacheValidationError" }

// Error satisfies the builtin error interface
func (e CacheValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCache.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CacheValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CacheValidationError{}

// Validate checks the field values on MessageQueue with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MessageQueue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MessageQueue with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MessageQueueMultiError, or
// nil if none found.
func (m *MessageQueue) ValidateAll() error {
	return m.validate(true)
}

func (m *MessageQueue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MessageQueueValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MessageQueueValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MessageQueueValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for Engine

	// no validation rules for Version

	for idx, item := range m.GetEndpoints() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MessageQueueValidationError{
						field:  fmt.Sprintf("Endpoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MessageQueueValidationError{
						field:  fmt.Sprintf("Endpoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MessageQueueValidationError{
					field:  fmt.Sprintf("Endpoints[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Replicas

	// no validation rules for AllocatedSize

	// no validation rules for InstanceClass

	// no validation rules for DeletionProtection

	// no validation rules for PubliclyAccessible

	if len(m.GetLabels()) > 256 {
		err := MessageQueueValidationError{
			field:  "Labels",
			reason: "value must contain no more than 256 pair(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	{
		sorted_keys := make([]string, len(m.GetLabels()))
		i := 0
		for key := range m.GetLabels() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetLabels()[key]
			_ = val

			if utf8.RuneCountInString(key) > 255 {
				err := MessageQueueValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 255 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if utf8.RuneCountInString(val) > 63 {
				err := MessageQueueValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 63 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}
	}

	if all {
		switch v := interface{}(m.GetExtra()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MessageQueueValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MessageQueueValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtra()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MessageQueueValidationError{
				field:  "Extra",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MessageQueueValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MessageQueueValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MessageQueueValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MessageQueueValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MessageQueueValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MessageQueueValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MessageQueueMultiError(errors)
	}

	return nil
}

// MessageQueueMultiError is an error wrapping multiple validation errors
// returned by MessageQueue.ValidateAll() if the designated constraints aren't met.
type MessageQueueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MessageQueueMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MessageQueueMultiError) AllErrors() []error { return m }

// MessageQueueValidationError is the validation error returned by
// MessageQueue.Validate if the designated constraints aren't met.
type MessageQueueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MessageQueueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MessageQueueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MessageQueueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MessageQueueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MessageQueueValidationError) ErrorName() string { return "MessageQueueValidationError" }

// Error satisfies the builtin error interface
func (e MessageQueueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMessageQueue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MessageQueueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MessageQueueValidationError{}

// Validate checks the field values on Topic with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Topic) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Topic with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TopicMultiError, or nil if none found.
func (m *Topic) ValidateAll() error {
	return m.validate(true)
}

func (m *Topic) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Engine

	if m.Id != nil {
		// no validation rules for Id
	}

	if m.Tenant != nil {
		// no validation rules for Tenant
	}

	if m.Namespace != nil {
		// no validation rules for Namespace
	}

	if m.Internal != nil {
		// no validation rules for Internal
	}

	if m.PartitionNumber != nil {
		// no validation rules for PartitionNumber
	}

	if m.ReplicationFactor != nil {
		// no validation rules for ReplicationFactor
	}

	if m.Config != nil {

		if all {
			switch v := interface{}(m.GetConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TopicValidationError{
						field:  "Config",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TopicValidationError{
						field:  "Config",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TopicValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TopicMultiError(errors)
	}

	return nil
}

// TopicMultiError is an error wrapping multiple validation errors returned by
// Topic.ValidateAll() if the designated constraints aren't met.
type TopicMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TopicMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TopicMultiError) AllErrors() []error { return m }

// TopicValidationError is the validation error returned by Topic.Validate if
// the designated constraints aren't met.
type TopicValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TopicValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TopicValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TopicValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TopicValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TopicValidationError) ErrorName() string { return "TopicValidationError" }

// Error satisfies the builtin error interface
func (e TopicValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTopic.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TopicValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TopicValidationError{}

// Validate checks the field values on NodeGroup_Identifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NodeGroup_Identifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NodeGroup_Identifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NodeGroup_IdentifierMultiError, or nil if none found.
func (m *NodeGroup_Identifier) ValidateAll() error {
	return m.validate(true)
}

func (m *NodeGroup_Identifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPlatform()) > 128 {
		err := NodeGroup_IdentifierValidationError{
			field:  "Platform",
			reason: "value length must be at most 128 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetCluster()) > 63 {
		err := NodeGroup_IdentifierValidationError{
			field:  "Cluster",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetName()) > 63 {
		err := NodeGroup_IdentifierValidationError{
			field:  "Name",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return NodeGroup_IdentifierMultiError(errors)
	}

	return nil
}

// NodeGroup_IdentifierMultiError is an error wrapping multiple validation
// errors returned by NodeGroup_Identifier.ValidateAll() if the designated
// constraints aren't met.
type NodeGroup_IdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NodeGroup_IdentifierMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NodeGroup_IdentifierMultiError) AllErrors() []error { return m }

// NodeGroup_IdentifierValidationError is the validation error returned by
// NodeGroup_Identifier.Validate if the designated constraints aren't met.
type NodeGroup_IdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NodeGroup_IdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NodeGroup_IdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NodeGroup_IdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NodeGroup_IdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NodeGroup_IdentifierValidationError) ErrorName() string {
	return "NodeGroup_IdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e NodeGroup_IdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNodeGroup_Identifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NodeGroup_IdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NodeGroup_IdentifierValidationError{}
