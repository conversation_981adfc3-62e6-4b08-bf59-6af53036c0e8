// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/tools/v1/cache_service.proto

package toolspb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RegisterCacheRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterCacheRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterCacheRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterCacheRequestMultiError, or nil if none found.
func (m *RegisterCacheRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterCacheRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterCacheRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterCacheRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterCacheRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RegisterCacheRequestMultiError(errors)
	}

	return nil
}

// RegisterCacheRequestMultiError is an error wrapping multiple validation
// errors returned by RegisterCacheRequest.ValidateAll() if the designated
// constraints aren't met.
type RegisterCacheRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterCacheRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterCacheRequestMultiError) AllErrors() []error { return m }

// RegisterCacheRequestValidationError is the validation error returned by
// RegisterCacheRequest.Validate if the designated constraints aren't met.
type RegisterCacheRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterCacheRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterCacheRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterCacheRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterCacheRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterCacheRequestValidationError) ErrorName() string {
	return "RegisterCacheRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterCacheRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterCacheRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterCacheRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterCacheRequestValidationError{}

// Validate checks the field values on RegisterCacheResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterCacheResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterCacheResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterCacheResponseMultiError, or nil if none found.
func (m *RegisterCacheResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterCacheResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Cache != nil {

		if all {
			switch v := interface{}(m.GetCache()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RegisterCacheResponseValidationError{
						field:  "Cache",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RegisterCacheResponseValidationError{
						field:  "Cache",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCache()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RegisterCacheResponseValidationError{
					field:  "Cache",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RegisterCacheResponseMultiError(errors)
	}

	return nil
}

// RegisterCacheResponseMultiError is an error wrapping multiple validation
// errors returned by RegisterCacheResponse.ValidateAll() if the designated
// constraints aren't met.
type RegisterCacheResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterCacheResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterCacheResponseMultiError) AllErrors() []error { return m }

// RegisterCacheResponseValidationError is the validation error returned by
// RegisterCacheResponse.Validate if the designated constraints aren't met.
type RegisterCacheResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterCacheResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterCacheResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterCacheResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterCacheResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterCacheResponseValidationError) ErrorName() string {
	return "RegisterCacheResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterCacheResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterCacheResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterCacheResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterCacheResponseValidationError{}

// Validate checks the field values on ListCachesRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListCachesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCachesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCachesRequestMultiError, or nil if none found.
func (m *ListCachesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCachesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetLabels()) > 256 {
		err := ListCachesRequestValidationError{
			field:  "Labels",
			reason: "value must contain no more than 256 pair(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	{
		sorted_keys := make([]string, len(m.GetLabels()))
		i := 0
		for key := range m.GetLabels() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetLabels()[key]
			_ = val

			if utf8.RuneCountInString(key) > 255 {
				err := ListCachesRequestValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 255 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if utf8.RuneCountInString(val) > 63 {
				err := ListCachesRequestValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 63 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}
	}

	if m.Platform != nil {

		if utf8.RuneCountInString(m.GetPlatform()) > 128 {
			err := ListCachesRequestValidationError{
				field:  "Platform",
				reason: "value length must be at most 128 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.Engine != nil {

		if utf8.RuneCountInString(m.GetEngine()) > 128 {
			err := ListCachesRequestValidationError{
				field:  "Engine",
				reason: "value length must be at most 128 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return ListCachesRequestMultiError(errors)
	}

	return nil
}

// ListCachesRequestMultiError is an error wrapping multiple validation errors
// returned by ListCachesRequest.ValidateAll() if the designated constraints
// aren't met.
type ListCachesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCachesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCachesRequestMultiError) AllErrors() []error { return m }

// ListCachesRequestValidationError is the validation error returned by
// ListCachesRequest.Validate if the designated constraints aren't met.
type ListCachesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCachesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCachesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCachesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCachesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCachesRequestValidationError) ErrorName() string {
	return "ListCachesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCachesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCachesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCachesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCachesRequestValidationError{}

// Validate checks the field values on ListCachesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCachesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCachesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCachesResponseMultiError, or nil if none found.
func (m *ListCachesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCachesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCaches() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCachesResponseValidationError{
						field:  fmt.Sprintf("Caches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCachesResponseValidationError{
						field:  fmt.Sprintf("Caches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCachesResponseValidationError{
					field:  fmt.Sprintf("Caches[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListCachesResponseMultiError(errors)
	}

	return nil
}

// ListCachesResponseMultiError is an error wrapping multiple validation errors
// returned by ListCachesResponse.ValidateAll() if the designated constraints
// aren't met.
type ListCachesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCachesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCachesResponseMultiError) AllErrors() []error { return m }

// ListCachesResponseValidationError is the validation error returned by
// ListCachesResponse.Validate if the designated constraints aren't met.
type ListCachesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCachesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCachesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCachesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCachesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCachesResponseValidationError) ErrorName() string {
	return "ListCachesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCachesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCachesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCachesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCachesResponseValidationError{}

// Validate checks the field values on GetCacheRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCacheRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCacheRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCacheRequestMultiError, or nil if none found.
func (m *GetCacheRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCacheRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCacheRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCacheRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCacheRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCacheRequestMultiError(errors)
	}

	return nil
}

// GetCacheRequestMultiError is an error wrapping multiple validation errors
// returned by GetCacheRequest.ValidateAll() if the designated constraints
// aren't met.
type GetCacheRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCacheRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCacheRequestMultiError) AllErrors() []error { return m }

// GetCacheRequestValidationError is the validation error returned by
// GetCacheRequest.Validate if the designated constraints aren't met.
type GetCacheRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCacheRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCacheRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCacheRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCacheRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCacheRequestValidationError) ErrorName() string { return "GetCacheRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetCacheRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCacheRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCacheRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCacheRequestValidationError{}

// Validate checks the field values on ExecuteCacheCommandRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExecuteCacheCommandRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteCacheCommandRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExecuteCacheCommandRequestMultiError, or nil if none found.
func (m *ExecuteCacheCommandRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteCacheCommandRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteCacheCommandRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteCacheCommandRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteCacheCommandRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.CommandsType.(type) {
	case *ExecuteCacheCommandRequest_Commands:
		if v == nil {
			err := ExecuteCacheCommandRequestValidationError{
				field:  "CommandsType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if utf8.RuneCountInString(m.GetCommands()) > 65536 {
			err := ExecuteCacheCommandRequestValidationError{
				field:  "Commands",
				reason: "value length must be at most 65536 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *ExecuteCacheCommandRequest_RedisScript_:
		if v == nil {
			err := ExecuteCacheCommandRequestValidationError{
				field:  "CommandsType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRedisScript()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExecuteCacheCommandRequestValidationError{
						field:  "RedisScript",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExecuteCacheCommandRequestValidationError{
						field:  "RedisScript",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRedisScript()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExecuteCacheCommandRequestValidationError{
					field:  "RedisScript",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ExecuteCacheCommandRequestMultiError(errors)
	}

	return nil
}

// ExecuteCacheCommandRequestMultiError is an error wrapping multiple
// validation errors returned by ExecuteCacheCommandRequest.ValidateAll() if
// the designated constraints aren't met.
type ExecuteCacheCommandRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteCacheCommandRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteCacheCommandRequestMultiError) AllErrors() []error { return m }

// ExecuteCacheCommandRequestValidationError is the validation error returned
// by ExecuteCacheCommandRequest.Validate if the designated constraints aren't met.
type ExecuteCacheCommandRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteCacheCommandRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteCacheCommandRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteCacheCommandRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteCacheCommandRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteCacheCommandRequestValidationError) ErrorName() string {
	return "ExecuteCacheCommandRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteCacheCommandRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteCacheCommandRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteCacheCommandRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteCacheCommandRequestValidationError{}

// Validate checks the field values on ExecuteCacheCommandResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExecuteCacheCommandResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteCacheCommandResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExecuteCacheCommandResponseMultiError, or nil if none found.
func (m *ExecuteCacheCommandResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteCacheCommandResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteCacheCommandResponseValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteCacheCommandResponseValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteCacheCommandResponseValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOutput()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteCacheCommandResponseValidationError{
					field:  "Output",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteCacheCommandResponseValidationError{
					field:  "Output",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOutput()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteCacheCommandResponseValidationError{
				field:  "Output",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExecuteCacheCommandResponseMultiError(errors)
	}

	return nil
}

// ExecuteCacheCommandResponseMultiError is an error wrapping multiple
// validation errors returned by ExecuteCacheCommandResponse.ValidateAll() if
// the designated constraints aren't met.
type ExecuteCacheCommandResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteCacheCommandResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteCacheCommandResponseMultiError) AllErrors() []error { return m }

// ExecuteCacheCommandResponseValidationError is the validation error returned
// by ExecuteCacheCommandResponse.Validate if the designated constraints
// aren't met.
type ExecuteCacheCommandResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteCacheCommandResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteCacheCommandResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteCacheCommandResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteCacheCommandResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteCacheCommandResponseValidationError) ErrorName() string {
	return "ExecuteCacheCommandResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteCacheCommandResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteCacheCommandResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteCacheCommandResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteCacheCommandResponseValidationError{}

// Validate checks the field values on ExecuteCacheCommandRequest_RedisScript
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ExecuteCacheCommandRequest_RedisScript) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ExecuteCacheCommandRequest_RedisScript with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ExecuteCacheCommandRequest_RedisScriptMultiError, or nil if none found.
func (m *ExecuteCacheCommandRequest_RedisScript) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteCacheCommandRequest_RedisScript) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetScript()) > 65536 {
		err := ExecuteCacheCommandRequest_RedisScriptValidationError{
			field:  "Script",
			reason: "value length must be at most 65536 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ExecuteCacheCommandRequest_RedisScriptMultiError(errors)
	}

	return nil
}

// ExecuteCacheCommandRequest_RedisScriptMultiError is an error wrapping
// multiple validation errors returned by
// ExecuteCacheCommandRequest_RedisScript.ValidateAll() if the designated
// constraints aren't met.
type ExecuteCacheCommandRequest_RedisScriptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteCacheCommandRequest_RedisScriptMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteCacheCommandRequest_RedisScriptMultiError) AllErrors() []error { return m }

// ExecuteCacheCommandRequest_RedisScriptValidationError is the validation
// error returned by ExecuteCacheCommandRequest_RedisScript.Validate if the
// designated constraints aren't met.
type ExecuteCacheCommandRequest_RedisScriptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteCacheCommandRequest_RedisScriptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteCacheCommandRequest_RedisScriptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteCacheCommandRequest_RedisScriptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteCacheCommandRequest_RedisScriptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteCacheCommandRequest_RedisScriptValidationError) ErrorName() string {
	return "ExecuteCacheCommandRequest_RedisScriptValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteCacheCommandRequest_RedisScriptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteCacheCommandRequest_RedisScript.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteCacheCommandRequest_RedisScriptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteCacheCommandRequest_RedisScriptValidationError{}
