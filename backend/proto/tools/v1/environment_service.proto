// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because there are are only three environments. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are are only three environments. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are are only three environments. --)

syntax = "proto3";

package backend.proto.tools.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.tools.v1";

import "backend/proto/tools/v1/resource_models.proto";
import "google/protobuf/struct.proto";
import "validate/validate.proto";

// EnvironmentService
service EnvironmentService {

    // register environments
    rpc RegisterEnvironments(RegisterEnvironmentsRequest) returns (RegisterEnvironmentsResponse);

    // list environments
    rpc ListEnvironments(ListEnvironmentsRequest) returns (ListEnvironmentsResponse);

    // get a environment
    rpc GetEnvironment(GetEnvironmentRequest) returns (Environment);

    // update environment
    rpc UpdateEnvironment(UpdateEnvironmentRequest) returns (Environment);

    // check environment
    rpc CheckEnvironment(CheckEnvironmentRequest) returns (CheckEnvironmentResponse);

}

// RegisterEnvironmentsRequest
message RegisterEnvironmentsRequest {
    // list of environments
    repeated Environment environments = 1;
}

// RegisterEnvironmentsResponse
message RegisterEnvironmentsResponse {
    // list of environments
    repeated Environment environments = 1;
}

// CheckEnvironmentRequest
message CheckEnvironmentRequest {
    // environment identifier
    EnvironmentIdentifier identifier = 1;
}

// CheckEnvironmentResponse
message CheckEnvironmentResponse {
    // environment identifier
    EnvironmentIdentifier environment = 1;
    // environment status
    string status = 2;
    // environment healthy
    bool healthy = 3;
}

// ListEnvironmentsRequest
message ListEnvironmentsRequest {
    // environment status
    repeated string statuses = 1 [(validate.rules).repeated = {
        unique: true,
        max_items: 5
    }];
}

// ListEnvironmentsResponse
message ListEnvironmentsResponse {
    // list of environments
    repeated Environment environments = 1;
}

// GetEnvironmentRequest
message GetEnvironmentRequest {
    // environment identifier
    EnvironmentIdentifier identifier = 1;
}

// UpdateEnvironmentRequest
message UpdateEnvironmentRequest {
    // environment identifier
    EnvironmentIdentifier identifier = 1;
    // environment status
    optional string status = 2;
    // environment is managed?
    optional bool is_managed = 3;
    // environment name, use to display
    optional string name = 4 [(validate.rules).string = {max_len: 64}];
    // environment description
    optional string description = 5 [(validate.rules).string = {max_len: 1024}];
    // list of databases
    repeated PlatformIdentifier databases = 6;
    // list of caches
    repeated PlatformIdentifier caches = 7;
    // list of message queues
    repeated PlatformIdentifier message_queues = 8;

    // extra info
    optional google.protobuf.Struct extra = 13;
}
