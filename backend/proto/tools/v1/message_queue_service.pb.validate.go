// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/tools/v1/message_queue_service.proto

package toolspb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RegisterMessageQueueRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterMessageQueueRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterMessageQueueRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterMessageQueueRequestMultiError, or nil if none found.
func (m *RegisterMessageQueueRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterMessageQueueRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterMessageQueueRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterMessageQueueRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterMessageQueueRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RegisterMessageQueueRequestMultiError(errors)
	}

	return nil
}

// RegisterMessageQueueRequestMultiError is an error wrapping multiple
// validation errors returned by RegisterMessageQueueRequest.ValidateAll() if
// the designated constraints aren't met.
type RegisterMessageQueueRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterMessageQueueRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterMessageQueueRequestMultiError) AllErrors() []error { return m }

// RegisterMessageQueueRequestValidationError is the validation error returned
// by RegisterMessageQueueRequest.Validate if the designated constraints
// aren't met.
type RegisterMessageQueueRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterMessageQueueRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterMessageQueueRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterMessageQueueRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterMessageQueueRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterMessageQueueRequestValidationError) ErrorName() string {
	return "RegisterMessageQueueRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterMessageQueueRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterMessageQueueRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterMessageQueueRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterMessageQueueRequestValidationError{}

// Validate checks the field values on RegisterMessageQueueResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterMessageQueueResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterMessageQueueResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterMessageQueueResponseMultiError, or nil if none found.
func (m *RegisterMessageQueueResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterMessageQueueResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.MessageQueue != nil {

		if all {
			switch v := interface{}(m.GetMessageQueue()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RegisterMessageQueueResponseValidationError{
						field:  "MessageQueue",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RegisterMessageQueueResponseValidationError{
						field:  "MessageQueue",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMessageQueue()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RegisterMessageQueueResponseValidationError{
					field:  "MessageQueue",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RegisterMessageQueueResponseMultiError(errors)
	}

	return nil
}

// RegisterMessageQueueResponseMultiError is an error wrapping multiple
// validation errors returned by RegisterMessageQueueResponse.ValidateAll() if
// the designated constraints aren't met.
type RegisterMessageQueueResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterMessageQueueResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterMessageQueueResponseMultiError) AllErrors() []error { return m }

// RegisterMessageQueueResponseValidationError is the validation error returned
// by RegisterMessageQueueResponse.Validate if the designated constraints
// aren't met.
type RegisterMessageQueueResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterMessageQueueResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterMessageQueueResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterMessageQueueResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterMessageQueueResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterMessageQueueResponseValidationError) ErrorName() string {
	return "RegisterMessageQueueResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterMessageQueueResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterMessageQueueResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterMessageQueueResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterMessageQueueResponseValidationError{}

// Validate checks the field values on ListMessageQueuesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMessageQueuesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMessageQueuesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMessageQueuesRequestMultiError, or nil if none found.
func (m *ListMessageQueuesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMessageQueuesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetLabels()) > 256 {
		err := ListMessageQueuesRequestValidationError{
			field:  "Labels",
			reason: "value must contain no more than 256 pair(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	{
		sorted_keys := make([]string, len(m.GetLabels()))
		i := 0
		for key := range m.GetLabels() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetLabels()[key]
			_ = val

			if utf8.RuneCountInString(key) > 255 {
				err := ListMessageQueuesRequestValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 255 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if utf8.RuneCountInString(val) > 63 {
				err := ListMessageQueuesRequestValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 63 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}
	}

	if m.Platform != nil {

		if utf8.RuneCountInString(m.GetPlatform()) > 128 {
			err := ListMessageQueuesRequestValidationError{
				field:  "Platform",
				reason: "value length must be at most 128 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.Engine != nil {

		if utf8.RuneCountInString(m.GetEngine()) > 128 {
			err := ListMessageQueuesRequestValidationError{
				field:  "Engine",
				reason: "value length must be at most 128 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return ListMessageQueuesRequestMultiError(errors)
	}

	return nil
}

// ListMessageQueuesRequestMultiError is an error wrapping multiple validation
// errors returned by ListMessageQueuesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListMessageQueuesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMessageQueuesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMessageQueuesRequestMultiError) AllErrors() []error { return m }

// ListMessageQueuesRequestValidationError is the validation error returned by
// ListMessageQueuesRequest.Validate if the designated constraints aren't met.
type ListMessageQueuesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMessageQueuesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMessageQueuesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMessageQueuesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMessageQueuesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMessageQueuesRequestValidationError) ErrorName() string {
	return "ListMessageQueuesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListMessageQueuesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMessageQueuesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMessageQueuesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMessageQueuesRequestValidationError{}

// Validate checks the field values on ListMessageQueuesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMessageQueuesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMessageQueuesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMessageQueuesResponseMultiError, or nil if none found.
func (m *ListMessageQueuesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMessageQueuesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetMessageQueues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListMessageQueuesResponseValidationError{
						field:  fmt.Sprintf("MessageQueues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListMessageQueuesResponseValidationError{
						field:  fmt.Sprintf("MessageQueues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListMessageQueuesResponseValidationError{
					field:  fmt.Sprintf("MessageQueues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListMessageQueuesResponseMultiError(errors)
	}

	return nil
}

// ListMessageQueuesResponseMultiError is an error wrapping multiple validation
// errors returned by ListMessageQueuesResponse.ValidateAll() if the
// designated constraints aren't met.
type ListMessageQueuesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMessageQueuesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMessageQueuesResponseMultiError) AllErrors() []error { return m }

// ListMessageQueuesResponseValidationError is the validation error returned by
// ListMessageQueuesResponse.Validate if the designated constraints aren't met.
type ListMessageQueuesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMessageQueuesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMessageQueuesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMessageQueuesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMessageQueuesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMessageQueuesResponseValidationError) ErrorName() string {
	return "ListMessageQueuesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListMessageQueuesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMessageQueuesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMessageQueuesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMessageQueuesResponseValidationError{}

// Validate checks the field values on GetMessageQueueRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMessageQueueRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMessageQueueRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMessageQueueRequestMultiError, or nil if none found.
func (m *GetMessageQueueRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMessageQueueRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMessageQueueRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMessageQueueRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMessageQueueRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMessageQueueRequestMultiError(errors)
	}

	return nil
}

// GetMessageQueueRequestMultiError is an error wrapping multiple validation
// errors returned by GetMessageQueueRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMessageQueueRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMessageQueueRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMessageQueueRequestMultiError) AllErrors() []error { return m }

// GetMessageQueueRequestValidationError is the validation error returned by
// GetMessageQueueRequest.Validate if the designated constraints aren't met.
type GetMessageQueueRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMessageQueueRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMessageQueueRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMessageQueueRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMessageQueueRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMessageQueueRequestValidationError) ErrorName() string {
	return "GetMessageQueueRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMessageQueueRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMessageQueueRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMessageQueueRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMessageQueueRequestValidationError{}

// Validate checks the field values on ListTopicsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListTopicsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTopicsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTopicsRequestMultiError, or nil if none found.
func (m *ListTopicsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTopicsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListTopicsRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListTopicsRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListTopicsRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Tenant != nil {
		// no validation rules for Tenant
	}

	if m.Namespace != nil {
		// no validation rules for Namespace
	}

	if m.Internal != nil {
		// no validation rules for Internal
	}

	if len(errors) > 0 {
		return ListTopicsRequestMultiError(errors)
	}

	return nil
}

// ListTopicsRequestMultiError is an error wrapping multiple validation errors
// returned by ListTopicsRequest.ValidateAll() if the designated constraints
// aren't met.
type ListTopicsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTopicsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTopicsRequestMultiError) AllErrors() []error { return m }

// ListTopicsRequestValidationError is the validation error returned by
// ListTopicsRequest.Validate if the designated constraints aren't met.
type ListTopicsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTopicsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTopicsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTopicsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTopicsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTopicsRequestValidationError) ErrorName() string {
	return "ListTopicsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListTopicsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTopicsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTopicsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTopicsRequestValidationError{}

// Validate checks the field values on ListTopicsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTopicsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTopicsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTopicsResponseMultiError, or nil if none found.
func (m *ListTopicsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTopicsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTopics() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListTopicsResponseValidationError{
						field:  fmt.Sprintf("Topics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListTopicsResponseValidationError{
						field:  fmt.Sprintf("Topics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListTopicsResponseValidationError{
					field:  fmt.Sprintf("Topics[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListTopicsResponseMultiError(errors)
	}

	return nil
}

// ListTopicsResponseMultiError is an error wrapping multiple validation errors
// returned by ListTopicsResponse.ValidateAll() if the designated constraints
// aren't met.
type ListTopicsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTopicsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTopicsResponseMultiError) AllErrors() []error { return m }

// ListTopicsResponseValidationError is the validation error returned by
// ListTopicsResponse.Validate if the designated constraints aren't met.
type ListTopicsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTopicsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTopicsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTopicsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTopicsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTopicsResponseValidationError) ErrorName() string {
	return "ListTopicsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListTopicsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTopicsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTopicsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTopicsResponseValidationError{}

// Validate checks the field values on CreateTopicsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTopicsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTopicsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTopicsRequestMultiError, or nil if none found.
func (m *CreateTopicsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTopicsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTopicsRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTopicsRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTopicsRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTopics() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateTopicsRequestValidationError{
						field:  fmt.Sprintf("Topics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateTopicsRequestValidationError{
						field:  fmt.Sprintf("Topics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateTopicsRequestValidationError{
					field:  fmt.Sprintf("Topics[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateTopicsRequestMultiError(errors)
	}

	return nil
}

// CreateTopicsRequestMultiError is an error wrapping multiple validation
// errors returned by CreateTopicsRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateTopicsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTopicsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTopicsRequestMultiError) AllErrors() []error { return m }

// CreateTopicsRequestValidationError is the validation error returned by
// CreateTopicsRequest.Validate if the designated constraints aren't met.
type CreateTopicsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTopicsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTopicsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTopicsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTopicsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTopicsRequestValidationError) ErrorName() string {
	return "CreateTopicsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTopicsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTopicsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTopicsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTopicsRequestValidationError{}

// Validate checks the field values on CreateTopicsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTopicsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTopicsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTopicsResponseMultiError, or nil if none found.
func (m *CreateTopicsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTopicsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTopics() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateTopicsResponseValidationError{
						field:  fmt.Sprintf("Topics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateTopicsResponseValidationError{
						field:  fmt.Sprintf("Topics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateTopicsResponseValidationError{
					field:  fmt.Sprintf("Topics[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateTopicsResponseMultiError(errors)
	}

	return nil
}

// CreateTopicsResponseMultiError is an error wrapping multiple validation
// errors returned by CreateTopicsResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateTopicsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTopicsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTopicsResponseMultiError) AllErrors() []error { return m }

// CreateTopicsResponseValidationError is the validation error returned by
// CreateTopicsResponse.Validate if the designated constraints aren't met.
type CreateTopicsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTopicsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTopicsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTopicsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTopicsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTopicsResponseValidationError) ErrorName() string {
	return "CreateTopicsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTopicsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTopicsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTopicsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTopicsResponseValidationError{}

// Validate checks the field values on DeleteTopicsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteTopicsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteTopicsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteTopicsRequestMultiError, or nil if none found.
func (m *DeleteTopicsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteTopicsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteTopicsRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteTopicsRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteTopicsRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteTopicsRequestMultiError(errors)
	}

	return nil
}

// DeleteTopicsRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteTopicsRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteTopicsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteTopicsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteTopicsRequestMultiError) AllErrors() []error { return m }

// DeleteTopicsRequestValidationError is the validation error returned by
// DeleteTopicsRequest.Validate if the designated constraints aren't met.
type DeleteTopicsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteTopicsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteTopicsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteTopicsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteTopicsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteTopicsRequestValidationError) ErrorName() string {
	return "DeleteTopicsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteTopicsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteTopicsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteTopicsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteTopicsRequestValidationError{}

// Validate checks the field values on UpdateTopicRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTopicRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTopicRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTopicRequestMultiError, or nil if none found.
func (m *UpdateTopicRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTopicRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTopicRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTopicRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTopicRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Name

	if m.PartitionNumber != nil {
		// no validation rules for PartitionNumber
	}

	if m.ReplicationFactor != nil {
		// no validation rules for ReplicationFactor
	}

	if m.Config != nil {

		if all {
			switch v := interface{}(m.GetConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateTopicRequestValidationError{
						field:  "Config",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateTopicRequestValidationError{
						field:  "Config",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateTopicRequestValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateTopicRequestMultiError(errors)
	}

	return nil
}

// UpdateTopicRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateTopicRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateTopicRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTopicRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTopicRequestMultiError) AllErrors() []error { return m }

// UpdateTopicRequestValidationError is the validation error returned by
// UpdateTopicRequest.Validate if the designated constraints aren't met.
type UpdateTopicRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTopicRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTopicRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTopicRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTopicRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTopicRequestValidationError) ErrorName() string {
	return "UpdateTopicRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTopicRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTopicRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTopicRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTopicRequestValidationError{}
