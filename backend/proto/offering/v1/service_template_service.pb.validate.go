// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/offering/v1/service_template_service.proto

package offeringpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on DeleteServiceTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteServiceTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteServiceTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteServiceTemplateRequestMultiError, or nil if none found.
func (m *DeleteServiceTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteServiceTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceTemplateId

	if len(errors) > 0 {
		return DeleteServiceTemplateRequestMultiError(errors)
	}

	return nil
}

// DeleteServiceTemplateRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteServiceTemplateRequest.ValidateAll() if
// the designated constraints aren't met.
type DeleteServiceTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteServiceTemplateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteServiceTemplateRequestMultiError) AllErrors() []error { return m }

// DeleteServiceTemplateRequestValidationError is the validation error returned
// by DeleteServiceTemplateRequest.Validate if the designated constraints
// aren't met.
type DeleteServiceTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteServiceTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteServiceTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteServiceTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteServiceTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteServiceTemplateRequestValidationError) ErrorName() string {
	return "DeleteServiceTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteServiceTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteServiceTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteServiceTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteServiceTemplateRequestValidationError{}

// Validate checks the field values on DeleteServiceTemplateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteServiceTemplateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteServiceTemplateResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeleteServiceTemplateResponseMultiError, or nil if none found.
func (m *DeleteServiceTemplateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteServiceTemplateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteServiceTemplateResponseMultiError(errors)
	}

	return nil
}

// DeleteServiceTemplateResponseMultiError is an error wrapping multiple
// validation errors returned by DeleteServiceTemplateResponse.ValidateAll()
// if the designated constraints aren't met.
type DeleteServiceTemplateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteServiceTemplateResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteServiceTemplateResponseMultiError) AllErrors() []error { return m }

// DeleteServiceTemplateResponseValidationError is the validation error
// returned by DeleteServiceTemplateResponse.Validate if the designated
// constraints aren't met.
type DeleteServiceTemplateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteServiceTemplateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteServiceTemplateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteServiceTemplateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteServiceTemplateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteServiceTemplateResponseValidationError) ErrorName() string {
	return "DeleteServiceTemplateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteServiceTemplateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteServiceTemplateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteServiceTemplateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteServiceTemplateResponseValidationError{}

// Validate checks the field values on UpdateServiceTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateServiceTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateServiceTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateServiceTemplateRequestMultiError, or nil if none found.
func (m *UpdateServiceTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateServiceTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetServiceTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateServiceTemplateRequestValidationError{
					field:  "ServiceTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateServiceTemplateRequestValidationError{
					field:  "ServiceTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetServiceTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateServiceTemplateRequestValidationError{
				field:  "ServiceTemplate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateServiceTemplateRequestMultiError(errors)
	}

	return nil
}

// UpdateServiceTemplateRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateServiceTemplateRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateServiceTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateServiceTemplateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateServiceTemplateRequestMultiError) AllErrors() []error { return m }

// UpdateServiceTemplateRequestValidationError is the validation error returned
// by UpdateServiceTemplateRequest.Validate if the designated constraints
// aren't met.
type UpdateServiceTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateServiceTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateServiceTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateServiceTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateServiceTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateServiceTemplateRequestValidationError) ErrorName() string {
	return "UpdateServiceTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateServiceTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateServiceTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateServiceTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateServiceTemplateRequestValidationError{}

// Validate checks the field values on UpdateServiceTemplateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateServiceTemplateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateServiceTemplateResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateServiceTemplateResponseMultiError, or nil if none found.
func (m *UpdateServiceTemplateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateServiceTemplateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateServiceTemplateResponseMultiError(errors)
	}

	return nil
}

// UpdateServiceTemplateResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateServiceTemplateResponse.ValidateAll()
// if the designated constraints aren't met.
type UpdateServiceTemplateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateServiceTemplateResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateServiceTemplateResponseMultiError) AllErrors() []error { return m }

// UpdateServiceTemplateResponseValidationError is the validation error
// returned by UpdateServiceTemplateResponse.Validate if the designated
// constraints aren't met.
type UpdateServiceTemplateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateServiceTemplateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateServiceTemplateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateServiceTemplateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateServiceTemplateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateServiceTemplateResponseValidationError) ErrorName() string {
	return "UpdateServiceTemplateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateServiceTemplateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateServiceTemplateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateServiceTemplateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateServiceTemplateResponseValidationError{}

// Validate checks the field values on CreateServiceTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateServiceTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateServiceTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateServiceTemplateRequestMultiError, or nil if none found.
func (m *CreateServiceTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateServiceTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetServiceTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateServiceTemplateRequestValidationError{
					field:  "ServiceTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateServiceTemplateRequestValidationError{
					field:  "ServiceTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetServiceTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateServiceTemplateRequestValidationError{
				field:  "ServiceTemplate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateServiceTemplateRequestMultiError(errors)
	}

	return nil
}

// CreateServiceTemplateRequestMultiError is an error wrapping multiple
// validation errors returned by CreateServiceTemplateRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateServiceTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateServiceTemplateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateServiceTemplateRequestMultiError) AllErrors() []error { return m }

// CreateServiceTemplateRequestValidationError is the validation error returned
// by CreateServiceTemplateRequest.Validate if the designated constraints
// aren't met.
type CreateServiceTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateServiceTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateServiceTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateServiceTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateServiceTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateServiceTemplateRequestValidationError) ErrorName() string {
	return "CreateServiceTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateServiceTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateServiceTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateServiceTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateServiceTemplateRequestValidationError{}

// Validate checks the field values on CreateServiceTemplateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateServiceTemplateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateServiceTemplateResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateServiceTemplateResponseMultiError, or nil if none found.
func (m *CreateServiceTemplateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateServiceTemplateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceTemplateId

	if len(errors) > 0 {
		return CreateServiceTemplateResponseMultiError(errors)
	}

	return nil
}

// CreateServiceTemplateResponseMultiError is an error wrapping multiple
// validation errors returned by CreateServiceTemplateResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateServiceTemplateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateServiceTemplateResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateServiceTemplateResponseMultiError) AllErrors() []error { return m }

// CreateServiceTemplateResponseValidationError is the validation error
// returned by CreateServiceTemplateResponse.Validate if the designated
// constraints aren't met.
type CreateServiceTemplateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateServiceTemplateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateServiceTemplateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateServiceTemplateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateServiceTemplateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateServiceTemplateResponseValidationError) ErrorName() string {
	return "CreateServiceTemplateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateServiceTemplateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateServiceTemplateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateServiceTemplateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateServiceTemplateResponseValidationError{}
