syntax = "proto3";

package backend.proto.offering.v1;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "backend/proto/offering/v1/common.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// Defines the structure for a service template, which acts as a blueprint for creating specific service instances.
message ServiceTemplate {
  // Primary key ID of the service template
  int64 id = 1;
  // The ID of the company this service template belongs to.
  int64 company_id = 2;
  // The ID of the care type associated with this service template.
  int64 care_type_id = 3;
  // The ID of the category this service template belongs to.
  int64 category_id = 4;
  // Name of the service template, unique within the same company
  string name = 5;
  // Optional description of the service
  string description = 6;
  // A color code associated with the service for UI purposes.
  string color_code = 7;
  // The sorting order of the service template.
  int64 sort = 8;
  // A list of image URLs for the service.
  repeated string images = 9;
  // The source of the service template.
  backend.proto.offering.v1.ServiceSource source = 10;
  // Whether the service template is currently active
  bool is_active = 11;
  // The timestamp when the service template was created.
  google.protobuf.Timestamp create_time = 12;
  // The timestamp when the service template was last updated.
  google.protobuf.Timestamp update_time = 13;
  // The timestamp when the service template was deleted.
  google.protobuf.Timestamp delete_time = 14;

  // A map of attribute values for this service template.
  // The key is the string representation of the AttributeKey enum.
  map<string, google.protobuf.Value> attributes = 99;
}
