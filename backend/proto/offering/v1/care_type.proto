syntax = "proto3";

package backend.proto.offering.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/struct.proto";
import "backend/proto/offering/v1/common.proto";
import "google/protobuf/timestamp.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// Defines a Care Type Config.
message CareType {
  // The unique ID of the care type.
  int64 id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The level of the organization (e.g., "enterprise", "company").
  OrganizationType organization_type = 2 [(google.api.field_behavior) = IMMUTABLE];

  // The ID of the organization.
  int64 organization_id = 3 [(google.api.field_behavior) = IMMUTABLE];

  // The name of the care type.
  string name = 4 [(google.api.field_behavior) = REQUIRED];

  // The system code of the care type (e.g. "grooming", "daycare").
  SystemCareType care_type = 5 [(google.api.field_behavior) = REQUIRED];

  // An optional description.
  string description = 6 [(google.api.field_behavior) = OPTIONAL];

  // The time the care type was created.
  google.protobuf.Timestamp create_time = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
  // The time the care type was last updated.
  google.protobuf.Timestamp update_time = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
  // The time the care type was deleted.
  google.protobuf.Timestamp delete_time = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Defines an attribute for a care type.
message CareTypeAttribute {
  // The unique ID of the attribute.
  int64 id = 1;

  // The ID of the care type this attribute belongs to.
  int64 care_type_id = 2;

  // The unique key of the attribute.
  AttributeKey attribute_key = 3;

  // The display label for the UI (e.g., "Pet Size").
  string label = 4;

  // The value type of the attribute (e.g., STRING, NUMBER).
  backend.proto.offering.v1.ValueType value_type = 5;

  // A list of options, used when value_type is ENUM.
  google.protobuf.Struct options = 6;

  // An optional description.
  string description = 7;

  // Whether this attribute is required.
  bool is_required = 8;

  // The default value for this attribute.
  google.protobuf.Value default_value = 9;

  // The time the attribute was created.
  google.protobuf.Timestamp create_time = 10;
  // The time the attribute was last updated.
  google.protobuf.Timestamp update_time = 11;
  // The time the attribute was deleted.
  google.protobuf.Timestamp delete_time = 12;
}
