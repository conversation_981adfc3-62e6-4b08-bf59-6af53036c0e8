syntax = "proto3";

package backend.proto.offering.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "backend/proto/offering/v1/service_template.proto";
import "backend/proto/offering/v1/common.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0135::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// Service for managing ServiceTemplate resources.
service ServiceTemplateService {
    // 创建一个服务
    rpc CreateServiceTemplate(CreateServiceTemplateRequest) returns (CreateServiceTemplateResponse);

    // 更新服务模板
    rpc UpdateServiceTemplate(UpdateServiceTemplateRequest) returns (UpdateServiceTemplateResponse);

    // 删除服务模板
    rpc DeleteServiceTemplate(DeleteServiceTemplateRequest) returns (DeleteServiceTemplateResponse);
}

// 删除服务模板请求
message DeleteServiceTemplateRequest {
  // 服务模板ID
  int64 service_template_id = 1;
}

// 删除服务模板响应
message DeleteServiceTemplateResponse{

}

// 更新服务模板请求
message UpdateServiceTemplateRequest{
  // 服务模板
  ServiceTemplate service_template = 1;
}

// 更新服务模板响应
message UpdateServiceTemplateResponse{
}

// 创建服务工厂请求
message CreateServiceTemplateRequest{
  // 服务工厂
  ServiceTemplate service_template = 1;
}

// 创建服务工厂响应
message CreateServiceTemplateResponse{
  // 服务模板ID
  // (-- api-linter: core::0141::forbidden-types=disabled
  //     aip.dev/not-precedent: uint64 is appropriate for ID field --)
  uint64 service_template_id = 1;
}