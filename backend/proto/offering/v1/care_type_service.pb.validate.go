// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/offering/v1/care_type_service.proto

package offeringpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCareTypeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCareTypeRequestMultiError, or nil if none found.
func (m *CreateCareTypeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCareTypeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCareType()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCareTypeRequestValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCareTypeRequestValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCareType()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCareTypeRequestValidationError{
				field:  "CareType",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCareTypeRequestMultiError(errors)
	}

	return nil
}

// CreateCareTypeRequestMultiError is an error wrapping multiple validation
// errors returned by CreateCareTypeRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateCareTypeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCareTypeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCareTypeRequestMultiError) AllErrors() []error { return m }

// CreateCareTypeRequestValidationError is the validation error returned by
// CreateCareTypeRequest.Validate if the designated constraints aren't met.
type CreateCareTypeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCareTypeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCareTypeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCareTypeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCareTypeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCareTypeRequestValidationError) ErrorName() string {
	return "CreateCareTypeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCareTypeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCareTypeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCareTypeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCareTypeRequestValidationError{}

// Validate checks the field values on CreateCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCareTypeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCareTypeResponseMultiError, or nil if none found.
func (m *CreateCareTypeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCareTypeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCareType()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCareTypeResponseValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCareTypeResponseValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCareType()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCareTypeResponseValidationError{
				field:  "CareType",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCareTypeResponseMultiError(errors)
	}

	return nil
}

// CreateCareTypeResponseMultiError is an error wrapping multiple validation
// errors returned by CreateCareTypeResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateCareTypeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCareTypeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCareTypeResponseMultiError) AllErrors() []error { return m }

// CreateCareTypeResponseValidationError is the validation error returned by
// CreateCareTypeResponse.Validate if the designated constraints aren't met.
type CreateCareTypeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCareTypeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCareTypeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCareTypeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCareTypeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCareTypeResponseValidationError) ErrorName() string {
	return "CreateCareTypeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCareTypeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCareTypeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCareTypeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCareTypeResponseValidationError{}

// Validate checks the field values on GetCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCareTypeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCareTypeRequestMultiError, or nil if none found.
func (m *GetCareTypeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCareTypeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetCareTypeRequestMultiError(errors)
	}

	return nil
}

// GetCareTypeRequestMultiError is an error wrapping multiple validation errors
// returned by GetCareTypeRequest.ValidateAll() if the designated constraints
// aren't met.
type GetCareTypeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCareTypeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCareTypeRequestMultiError) AllErrors() []error { return m }

// GetCareTypeRequestValidationError is the validation error returned by
// GetCareTypeRequest.Validate if the designated constraints aren't met.
type GetCareTypeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCareTypeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCareTypeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCareTypeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCareTypeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCareTypeRequestValidationError) ErrorName() string {
	return "GetCareTypeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCareTypeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCareTypeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCareTypeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCareTypeRequestValidationError{}

// Validate checks the field values on GetCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCareTypeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCareTypeResponseMultiError, or nil if none found.
func (m *GetCareTypeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCareTypeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCareType()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCareTypeResponseValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCareTypeResponseValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCareType()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCareTypeResponseValidationError{
				field:  "CareType",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCareTypeResponseMultiError(errors)
	}

	return nil
}

// GetCareTypeResponseMultiError is an error wrapping multiple validation
// errors returned by GetCareTypeResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCareTypeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCareTypeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCareTypeResponseMultiError) AllErrors() []error { return m }

// GetCareTypeResponseValidationError is the validation error returned by
// GetCareTypeResponse.Validate if the designated constraints aren't met.
type GetCareTypeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCareTypeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCareTypeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCareTypeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCareTypeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCareTypeResponseValidationError) ErrorName() string {
	return "GetCareTypeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCareTypeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCareTypeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCareTypeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCareTypeResponseValidationError{}

// Validate checks the field values on ListCareTypesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCareTypesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCareTypesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCareTypesRequestMultiError, or nil if none found.
func (m *ListCareTypesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCareTypesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrganizationType

	// no validation rules for OrganizationId

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCareTypesRequestValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCareTypesRequestValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCareTypesRequestValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListCareTypesRequestMultiError(errors)
	}

	return nil
}

// ListCareTypesRequestMultiError is an error wrapping multiple validation
// errors returned by ListCareTypesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListCareTypesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCareTypesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCareTypesRequestMultiError) AllErrors() []error { return m }

// ListCareTypesRequestValidationError is the validation error returned by
// ListCareTypesRequest.Validate if the designated constraints aren't met.
type ListCareTypesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCareTypesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCareTypesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCareTypesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCareTypesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCareTypesRequestValidationError) ErrorName() string {
	return "ListCareTypesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCareTypesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCareTypesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCareTypesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCareTypesRequestValidationError{}

// Validate checks the field values on ListCareTypesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCareTypesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCareTypesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCareTypesResponseMultiError, or nil if none found.
func (m *ListCareTypesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCareTypesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCareTypes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCareTypesResponseValidationError{
						field:  fmt.Sprintf("CareTypes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCareTypesResponseValidationError{
						field:  fmt.Sprintf("CareTypes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCareTypesResponseValidationError{
					field:  fmt.Sprintf("CareTypes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCareTypesResponseValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCareTypesResponseValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCareTypesResponseValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListCareTypesResponseMultiError(errors)
	}

	return nil
}

// ListCareTypesResponseMultiError is an error wrapping multiple validation
// errors returned by ListCareTypesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListCareTypesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCareTypesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCareTypesResponseMultiError) AllErrors() []error { return m }

// ListCareTypesResponseValidationError is the validation error returned by
// ListCareTypesResponse.Validate if the designated constraints aren't met.
type ListCareTypesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCareTypesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCareTypesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCareTypesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCareTypesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCareTypesResponseValidationError) ErrorName() string {
	return "ListCareTypesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCareTypesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCareTypesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCareTypesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCareTypesResponseValidationError{}

// Validate checks the field values on UpdateCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCareTypeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCareTypeRequestMultiError, or nil if none found.
func (m *UpdateCareTypeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCareTypeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCareType()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCareTypeRequestValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCareTypeRequestValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCareType()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCareTypeRequestValidationError{
				field:  "CareType",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCareTypeRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCareTypeRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCareTypeRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCareTypeRequestMultiError(errors)
	}

	return nil
}

// UpdateCareTypeRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateCareTypeRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateCareTypeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCareTypeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCareTypeRequestMultiError) AllErrors() []error { return m }

// UpdateCareTypeRequestValidationError is the validation error returned by
// UpdateCareTypeRequest.Validate if the designated constraints aren't met.
type UpdateCareTypeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCareTypeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCareTypeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCareTypeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCareTypeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCareTypeRequestValidationError) ErrorName() string {
	return "UpdateCareTypeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCareTypeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCareTypeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCareTypeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCareTypeRequestValidationError{}

// Validate checks the field values on UpdateCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCareTypeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCareTypeResponseMultiError, or nil if none found.
func (m *UpdateCareTypeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCareTypeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCareType()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCareTypeResponseValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCareTypeResponseValidationError{
					field:  "CareType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCareType()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCareTypeResponseValidationError{
				field:  "CareType",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCareTypeResponseMultiError(errors)
	}

	return nil
}

// UpdateCareTypeResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateCareTypeResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateCareTypeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCareTypeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCareTypeResponseMultiError) AllErrors() []error { return m }

// UpdateCareTypeResponseValidationError is the validation error returned by
// UpdateCareTypeResponse.Validate if the designated constraints aren't met.
type UpdateCareTypeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCareTypeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCareTypeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCareTypeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCareTypeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCareTypeResponseValidationError) ErrorName() string {
	return "UpdateCareTypeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCareTypeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCareTypeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCareTypeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCareTypeResponseValidationError{}

// Validate checks the field values on DeleteCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteCareTypeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCareTypeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteCareTypeRequestMultiError, or nil if none found.
func (m *DeleteCareTypeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCareTypeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteCareTypeRequestMultiError(errors)
	}

	return nil
}

// DeleteCareTypeRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteCareTypeRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteCareTypeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCareTypeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCareTypeRequestMultiError) AllErrors() []error { return m }

// DeleteCareTypeRequestValidationError is the validation error returned by
// DeleteCareTypeRequest.Validate if the designated constraints aren't met.
type DeleteCareTypeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCareTypeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCareTypeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCareTypeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCareTypeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCareTypeRequestValidationError) ErrorName() string {
	return "DeleteCareTypeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCareTypeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCareTypeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCareTypeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCareTypeRequestValidationError{}

// Validate checks the field values on DeleteCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteCareTypeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCareTypeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteCareTypeResponseMultiError, or nil if none found.
func (m *DeleteCareTypeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCareTypeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteCareTypeResponseMultiError(errors)
	}

	return nil
}

// DeleteCareTypeResponseMultiError is an error wrapping multiple validation
// errors returned by DeleteCareTypeResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteCareTypeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCareTypeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCareTypeResponseMultiError) AllErrors() []error { return m }

// DeleteCareTypeResponseValidationError is the validation error returned by
// DeleteCareTypeResponse.Validate if the designated constraints aren't met.
type DeleteCareTypeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCareTypeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCareTypeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCareTypeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCareTypeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCareTypeResponseValidationError) ErrorName() string {
	return "DeleteCareTypeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCareTypeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCareTypeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCareTypeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCareTypeResponseValidationError{}
