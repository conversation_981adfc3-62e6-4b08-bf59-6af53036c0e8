// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/offering/v1/care_type.proto

package offeringpb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Defines a Care Type Config.
type CareType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique ID of the care type.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The level of the organization (e.g., "enterprise", "company").
	OrganizationType OrganizationType `protobuf:"varint,2,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.offering.v1.OrganizationType" json:"organization_type,omitempty"`
	// The ID of the organization.
	OrganizationId int64 `protobuf:"varint,3,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// The name of the care type.
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// The system code of the care type (e.g. "grooming", "daycare").
	CareType SystemCareType `protobuf:"varint,5,opt,name=care_type,json=careType,proto3,enum=backend.proto.offering.v1.SystemCareType" json:"care_type,omitempty"`
	// An optional description.
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// The time the care type was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The time the care type was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// The time the care type was deleted.
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CareType) Reset() {
	*x = CareType{}
	mi := &file_backend_proto_offering_v1_care_type_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CareType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CareType) ProtoMessage() {}

func (x *CareType) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CareType.ProtoReflect.Descriptor instead.
func (*CareType) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_proto_rawDescGZIP(), []int{0}
}

func (x *CareType) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CareType) GetOrganizationType() OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return OrganizationType_ORGANIZATION_TYPE_UNSPECIFIED
}

func (x *CareType) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *CareType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CareType) GetCareType() SystemCareType {
	if x != nil {
		return x.CareType
	}
	return SystemCareType_SYSTEM_CARE_TYPE_UNSPECIFIED
}

func (x *CareType) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CareType) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *CareType) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *CareType) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// Defines an attribute for a care type.
type CareTypeAttribute struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique ID of the attribute.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The ID of the care type this attribute belongs to.
	CareTypeId int64 `protobuf:"varint,2,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// The unique key of the attribute.
	AttributeKey AttributeKey `protobuf:"varint,3,opt,name=attribute_key,json=attributeKey,proto3,enum=backend.proto.offering.v1.AttributeKey" json:"attribute_key,omitempty"`
	// The display label for the UI (e.g., "Pet Size").
	Label string `protobuf:"bytes,4,opt,name=label,proto3" json:"label,omitempty"`
	// The value type of the attribute (e.g., STRING, NUMBER).
	ValueType ValueType `protobuf:"varint,5,opt,name=value_type,json=valueType,proto3,enum=backend.proto.offering.v1.ValueType" json:"value_type,omitempty"`
	// A list of options, used when value_type is ENUM.
	Options *structpb.Struct `protobuf:"bytes,6,opt,name=options,proto3" json:"options,omitempty"`
	// An optional description.
	Description string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	// Whether this attribute is required.
	IsRequired bool `protobuf:"varint,8,opt,name=is_required,json=isRequired,proto3" json:"is_required,omitempty"`
	// The default value for this attribute.
	DefaultValue *structpb.Value `protobuf:"bytes,9,opt,name=default_value,json=defaultValue,proto3" json:"default_value,omitempty"`
	// The time the attribute was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The time the attribute was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// The time the attribute was deleted.
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CareTypeAttribute) Reset() {
	*x = CareTypeAttribute{}
	mi := &file_backend_proto_offering_v1_care_type_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CareTypeAttribute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CareTypeAttribute) ProtoMessage() {}

func (x *CareTypeAttribute) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CareTypeAttribute.ProtoReflect.Descriptor instead.
func (*CareTypeAttribute) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_proto_rawDescGZIP(), []int{1}
}

func (x *CareTypeAttribute) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CareTypeAttribute) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *CareTypeAttribute) GetAttributeKey() AttributeKey {
	if x != nil {
		return x.AttributeKey
	}
	return AttributeKey_ATTRIBUTE_KEY_UNSPECIFIED
}

func (x *CareTypeAttribute) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *CareTypeAttribute) GetValueType() ValueType {
	if x != nil {
		return x.ValueType
	}
	return ValueType_VALUE_TYPE_UNSPECIFIED
}

func (x *CareTypeAttribute) GetOptions() *structpb.Struct {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *CareTypeAttribute) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CareTypeAttribute) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *CareTypeAttribute) GetDefaultValue() *structpb.Value {
	if x != nil {
		return x.DefaultValue
	}
	return nil
}

func (x *CareTypeAttribute) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *CareTypeAttribute) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *CareTypeAttribute) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

var File_backend_proto_offering_v1_care_type_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_care_type_proto_rawDesc = "" +
	"\n" +
	")backend/proto/offering/v1/care_type.proto\x12\x19backend.proto.offering.v1\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a&backend/proto/offering/v1/common.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xff\x03\n" +
	"\bCareType\x12\x13\n" +
	"\x02id\x18\x01 \x01(\x03B\x03\xe0A\x03R\x02id\x12]\n" +
	"\x11organization_type\x18\x02 \x01(\x0e2+.backend.proto.offering.v1.OrganizationTypeB\x03\xe0A\x05R\x10organizationType\x12,\n" +
	"\x0forganization_id\x18\x03 \x01(\x03B\x03\xe0A\x05R\x0eorganizationId\x12\x17\n" +
	"\x04name\x18\x04 \x01(\tB\x03\xe0A\x02R\x04name\x12K\n" +
	"\tcare_type\x18\x05 \x01(\x0e2).backend.proto.offering.v1.SystemCareTypeB\x03\xe0A\x02R\bcareType\x12%\n" +
	"\vdescription\x18\x06 \x01(\tB\x03\xe0A\x01R\vdescription\x12@\n" +
	"\vcreate_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"createTime\x12@\n" +
	"\vupdate_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"updateTime\x12@\n" +
	"\vdelete_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"deleteTime\"\xd8\x04\n" +
	"\x11CareTypeAttribute\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12 \n" +
	"\fcare_type_id\x18\x02 \x01(\x03R\n" +
	"careTypeId\x12L\n" +
	"\rattribute_key\x18\x03 \x01(\x0e2'.backend.proto.offering.v1.AttributeKeyR\fattributeKey\x12\x14\n" +
	"\x05label\x18\x04 \x01(\tR\x05label\x12C\n" +
	"\n" +
	"value_type\x18\x05 \x01(\x0e2$.backend.proto.offering.v1.ValueTypeR\tvalueType\x121\n" +
	"\aoptions\x18\x06 \x01(\v2\x17.google.protobuf.StructR\aoptions\x12 \n" +
	"\vdescription\x18\a \x01(\tR\vdescription\x12\x1f\n" +
	"\vis_required\x18\b \x01(\bR\n" +
	"isRequired\x12;\n" +
	"\rdefault_value\x18\t \x01(\v2\x16.google.protobuf.ValueR\fdefaultValue\x12;\n" +
	"\vcreate_time\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vdelete_time\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deleteTimeBk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_care_type_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_care_type_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_care_type_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_care_type_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_care_type_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_care_type_proto_rawDesc), len(file_backend_proto_offering_v1_care_type_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_care_type_proto_rawDescData
}

var file_backend_proto_offering_v1_care_type_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_offering_v1_care_type_proto_goTypes = []any{
	(*CareType)(nil),              // 0: backend.proto.offering.v1.CareType
	(*CareTypeAttribute)(nil),     // 1: backend.proto.offering.v1.CareTypeAttribute
	(OrganizationType)(0),         // 2: backend.proto.offering.v1.OrganizationType
	(SystemCareType)(0),           // 3: backend.proto.offering.v1.SystemCareType
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
	(AttributeKey)(0),             // 5: backend.proto.offering.v1.AttributeKey
	(ValueType)(0),                // 6: backend.proto.offering.v1.ValueType
	(*structpb.Struct)(nil),       // 7: google.protobuf.Struct
	(*structpb.Value)(nil),        // 8: google.protobuf.Value
}
var file_backend_proto_offering_v1_care_type_proto_depIdxs = []int32{
	2,  // 0: backend.proto.offering.v1.CareType.organization_type:type_name -> backend.proto.offering.v1.OrganizationType
	3,  // 1: backend.proto.offering.v1.CareType.care_type:type_name -> backend.proto.offering.v1.SystemCareType
	4,  // 2: backend.proto.offering.v1.CareType.create_time:type_name -> google.protobuf.Timestamp
	4,  // 3: backend.proto.offering.v1.CareType.update_time:type_name -> google.protobuf.Timestamp
	4,  // 4: backend.proto.offering.v1.CareType.delete_time:type_name -> google.protobuf.Timestamp
	5,  // 5: backend.proto.offering.v1.CareTypeAttribute.attribute_key:type_name -> backend.proto.offering.v1.AttributeKey
	6,  // 6: backend.proto.offering.v1.CareTypeAttribute.value_type:type_name -> backend.proto.offering.v1.ValueType
	7,  // 7: backend.proto.offering.v1.CareTypeAttribute.options:type_name -> google.protobuf.Struct
	8,  // 8: backend.proto.offering.v1.CareTypeAttribute.default_value:type_name -> google.protobuf.Value
	4,  // 9: backend.proto.offering.v1.CareTypeAttribute.create_time:type_name -> google.protobuf.Timestamp
	4,  // 10: backend.proto.offering.v1.CareTypeAttribute.update_time:type_name -> google.protobuf.Timestamp
	4,  // 11: backend.proto.offering.v1.CareTypeAttribute.delete_time:type_name -> google.protobuf.Timestamp
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_care_type_proto_init() }
func file_backend_proto_offering_v1_care_type_proto_init() {
	if File_backend_proto_offering_v1_care_type_proto != nil {
		return
	}
	file_backend_proto_offering_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_care_type_proto_rawDesc), len(file_backend_proto_offering_v1_care_type_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_v1_care_type_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_care_type_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_v1_care_type_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_care_type_proto = out.File
	file_backend_proto_offering_v1_care_type_proto_goTypes = nil
	file_backend_proto_offering_v1_care_type_proto_depIdxs = nil
}
