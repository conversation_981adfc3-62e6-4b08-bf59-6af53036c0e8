// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/offering/v1/service_template_service.proto

package offeringpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ServiceTemplateService_CreateServiceTemplate_FullMethodName = "/backend.proto.offering.v1.ServiceTemplateService/CreateServiceTemplate"
	ServiceTemplateService_UpdateServiceTemplate_FullMethodName = "/backend.proto.offering.v1.ServiceTemplateService/UpdateServiceTemplate"
	ServiceTemplateService_DeleteServiceTemplate_FullMethodName = "/backend.proto.offering.v1.ServiceTemplateService/DeleteServiceTemplate"
)

// ServiceTemplateServiceClient is the client API for ServiceTemplateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// (-- api-linter: core::0131::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0133::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0134::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0135::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// Service for managing ServiceTemplate resources.
type ServiceTemplateServiceClient interface {
	// 创建一个服务
	CreateServiceTemplate(ctx context.Context, in *CreateServiceTemplateRequest, opts ...grpc.CallOption) (*CreateServiceTemplateResponse, error)
	// 更新服务模板
	UpdateServiceTemplate(ctx context.Context, in *UpdateServiceTemplateRequest, opts ...grpc.CallOption) (*UpdateServiceTemplateResponse, error)
	// 删除服务模板
	DeleteServiceTemplate(ctx context.Context, in *DeleteServiceTemplateRequest, opts ...grpc.CallOption) (*DeleteServiceTemplateResponse, error)
}

type serviceTemplateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceTemplateServiceClient(cc grpc.ClientConnInterface) ServiceTemplateServiceClient {
	return &serviceTemplateServiceClient{cc}
}

func (c *serviceTemplateServiceClient) CreateServiceTemplate(ctx context.Context, in *CreateServiceTemplateRequest, opts ...grpc.CallOption) (*CreateServiceTemplateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateServiceTemplateResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_CreateServiceTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceTemplateServiceClient) UpdateServiceTemplate(ctx context.Context, in *UpdateServiceTemplateRequest, opts ...grpc.CallOption) (*UpdateServiceTemplateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateServiceTemplateResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_UpdateServiceTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceTemplateServiceClient) DeleteServiceTemplate(ctx context.Context, in *DeleteServiceTemplateRequest, opts ...grpc.CallOption) (*DeleteServiceTemplateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteServiceTemplateResponse)
	err := c.cc.Invoke(ctx, ServiceTemplateService_DeleteServiceTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceTemplateServiceServer is the server API for ServiceTemplateService service.
// All implementations must embed UnimplementedServiceTemplateServiceServer
// for forward compatibility.
//
// (-- api-linter: core::0131::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0133::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0134::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0135::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// Service for managing ServiceTemplate resources.
type ServiceTemplateServiceServer interface {
	// 创建一个服务
	CreateServiceTemplate(context.Context, *CreateServiceTemplateRequest) (*CreateServiceTemplateResponse, error)
	// 更新服务模板
	UpdateServiceTemplate(context.Context, *UpdateServiceTemplateRequest) (*UpdateServiceTemplateResponse, error)
	// 删除服务模板
	DeleteServiceTemplate(context.Context, *DeleteServiceTemplateRequest) (*DeleteServiceTemplateResponse, error)
	mustEmbedUnimplementedServiceTemplateServiceServer()
}

// UnimplementedServiceTemplateServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedServiceTemplateServiceServer struct{}

func (UnimplementedServiceTemplateServiceServer) CreateServiceTemplate(context.Context, *CreateServiceTemplateRequest) (*CreateServiceTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateServiceTemplate not implemented")
}
func (UnimplementedServiceTemplateServiceServer) UpdateServiceTemplate(context.Context, *UpdateServiceTemplateRequest) (*UpdateServiceTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServiceTemplate not implemented")
}
func (UnimplementedServiceTemplateServiceServer) DeleteServiceTemplate(context.Context, *DeleteServiceTemplateRequest) (*DeleteServiceTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteServiceTemplate not implemented")
}
func (UnimplementedServiceTemplateServiceServer) mustEmbedUnimplementedServiceTemplateServiceServer() {
}
func (UnimplementedServiceTemplateServiceServer) testEmbeddedByValue() {}

// UnsafeServiceTemplateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceTemplateServiceServer will
// result in compilation errors.
type UnsafeServiceTemplateServiceServer interface {
	mustEmbedUnimplementedServiceTemplateServiceServer()
}

func RegisterServiceTemplateServiceServer(s grpc.ServiceRegistrar, srv ServiceTemplateServiceServer) {
	// If the following call pancis, it indicates UnimplementedServiceTemplateServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ServiceTemplateService_ServiceDesc, srv)
}

func _ServiceTemplateService_CreateServiceTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).CreateServiceTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_CreateServiceTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).CreateServiceTemplate(ctx, req.(*CreateServiceTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceTemplateService_UpdateServiceTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServiceTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).UpdateServiceTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_UpdateServiceTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).UpdateServiceTemplate(ctx, req.(*UpdateServiceTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceTemplateService_DeleteServiceTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServiceTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceTemplateServiceServer).DeleteServiceTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceTemplateService_DeleteServiceTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceTemplateServiceServer).DeleteServiceTemplate(ctx, req.(*DeleteServiceTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServiceTemplateService_ServiceDesc is the grpc.ServiceDesc for ServiceTemplateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServiceTemplateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.offering.v1.ServiceTemplateService",
	HandlerType: (*ServiceTemplateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateServiceTemplate",
			Handler:    _ServiceTemplateService_CreateServiceTemplate_Handler,
		},
		{
			MethodName: "UpdateServiceTemplate",
			Handler:    _ServiceTemplateService_UpdateServiceTemplate_Handler,
		},
		{
			MethodName: "DeleteServiceTemplate",
			Handler:    _ServiceTemplateService_DeleteServiceTemplate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/offering/v1/service_template_service.proto",
}
