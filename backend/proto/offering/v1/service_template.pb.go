// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/offering/v1/service_template.proto

package offeringpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Defines the structure for a service template, which acts as a blueprint for creating specific service instances.
type ServiceTemplate struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Primary key ID of the service template
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The ID of the company this service template belongs to.
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// The ID of the care type associated with this service template.
	CareTypeId int64 `protobuf:"varint,3,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// The ID of the category this service template belongs to.
	CategoryId int64 `protobuf:"varint,4,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// Name of the service template, unique within the same company
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// Optional description of the service
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// A color code associated with the service for UI purposes.
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// The sorting order of the service template.
	Sort int64 `protobuf:"varint,8,opt,name=sort,proto3" json:"sort,omitempty"`
	// A list of image URLs for the service.
	Images []string `protobuf:"bytes,9,rep,name=images,proto3" json:"images,omitempty"`
	// The source of the service template.
	Source ServiceSource `protobuf:"varint,10,opt,name=source,proto3,enum=backend.proto.offering.v1.ServiceSource" json:"source,omitempty"`
	// Whether the service template is currently active
	IsActive bool `protobuf:"varint,11,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// The timestamp when the service template was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The timestamp when the service template was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// The timestamp when the service template was deleted.
	DeleteTime *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	// A map of attribute values for this service template.
	// The key is the string representation of the AttributeKey enum.
	Attributes    map[string]*structpb.Value `protobuf:"bytes,99,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceTemplate) Reset() {
	*x = ServiceTemplate{}
	mi := &file_backend_proto_offering_v1_service_template_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceTemplate) ProtoMessage() {}

func (x *ServiceTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_template_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceTemplate.ProtoReflect.Descriptor instead.
func (*ServiceTemplate) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_template_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceTemplate) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceTemplate) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ServiceTemplate) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *ServiceTemplate) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ServiceTemplate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceTemplate) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ServiceTemplate) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *ServiceTemplate) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *ServiceTemplate) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ServiceTemplate) GetSource() ServiceSource {
	if x != nil {
		return x.Source
	}
	return ServiceSource_SERVICE_SOURCE_UNSPECIFIED
}

func (x *ServiceTemplate) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *ServiceTemplate) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ServiceTemplate) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ServiceTemplate) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

func (x *ServiceTemplate) GetAttributes() map[string]*structpb.Value {
	if x != nil {
		return x.Attributes
	}
	return nil
}

var File_backend_proto_offering_v1_service_template_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_service_template_proto_rawDesc = "" +
	"\n" +
	"0backend/proto/offering/v1/service_template.proto\x12\x19backend.proto.offering.v1\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a&backend/proto/offering/v1/common.proto\"\xcd\x05\n" +
	"\x0fServiceTemplate\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03R\tcompanyId\x12 \n" +
	"\fcare_type_id\x18\x03 \x01(\x03R\n" +
	"careTypeId\x12\x1f\n" +
	"\vcategory_id\x18\x04 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x06 \x01(\tR\vdescription\x12\x1d\n" +
	"\n" +
	"color_code\x18\a \x01(\tR\tcolorCode\x12\x12\n" +
	"\x04sort\x18\b \x01(\x03R\x04sort\x12\x16\n" +
	"\x06images\x18\t \x03(\tR\x06images\x12@\n" +
	"\x06source\x18\n" +
	" \x01(\x0e2(.backend.proto.offering.v1.ServiceSourceR\x06source\x12\x1b\n" +
	"\tis_active\x18\v \x01(\bR\bisActive\x12;\n" +
	"\vcreate_time\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vdelete_time\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deleteTime\x12Z\n" +
	"\n" +
	"attributes\x18c \x03(\v2:.backend.proto.offering.v1.ServiceTemplate.AttributesEntryR\n" +
	"attributes\x1aU\n" +
	"\x0fAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12,\n" +
	"\x05value\x18\x02 \x01(\v2\x16.google.protobuf.ValueR\x05value:\x028\x01Bk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_service_template_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_service_template_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_service_template_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_service_template_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_service_template_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_template_proto_rawDesc), len(file_backend_proto_offering_v1_service_template_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_service_template_proto_rawDescData
}

var file_backend_proto_offering_v1_service_template_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_offering_v1_service_template_proto_goTypes = []any{
	(*ServiceTemplate)(nil),       // 0: backend.proto.offering.v1.ServiceTemplate
	nil,                           // 1: backend.proto.offering.v1.ServiceTemplate.AttributesEntry
	(ServiceSource)(0),            // 2: backend.proto.offering.v1.ServiceSource
	(*timestamppb.Timestamp)(nil), // 3: google.protobuf.Timestamp
	(*structpb.Value)(nil),        // 4: google.protobuf.Value
}
var file_backend_proto_offering_v1_service_template_proto_depIdxs = []int32{
	2, // 0: backend.proto.offering.v1.ServiceTemplate.source:type_name -> backend.proto.offering.v1.ServiceSource
	3, // 1: backend.proto.offering.v1.ServiceTemplate.create_time:type_name -> google.protobuf.Timestamp
	3, // 2: backend.proto.offering.v1.ServiceTemplate.update_time:type_name -> google.protobuf.Timestamp
	3, // 3: backend.proto.offering.v1.ServiceTemplate.delete_time:type_name -> google.protobuf.Timestamp
	1, // 4: backend.proto.offering.v1.ServiceTemplate.attributes:type_name -> backend.proto.offering.v1.ServiceTemplate.AttributesEntry
	4, // 5: backend.proto.offering.v1.ServiceTemplate.AttributesEntry.value:type_name -> google.protobuf.Value
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_service_template_proto_init() }
func file_backend_proto_offering_v1_service_template_proto_init() {
	if File_backend_proto_offering_v1_service_template_proto != nil {
		return
	}
	file_backend_proto_offering_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_template_proto_rawDesc), len(file_backend_proto_offering_v1_service_template_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_v1_service_template_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_service_template_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_v1_service_template_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_service_template_proto = out.File
	file_backend_proto_offering_v1_service_template_proto_goTypes = nil
	file_backend_proto_offering_v1_service_template_proto_depIdxs = nil
}
