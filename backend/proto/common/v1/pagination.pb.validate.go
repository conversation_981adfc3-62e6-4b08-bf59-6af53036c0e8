// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/common/v1/pagination.proto

package commonpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Pagination with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Pagination) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Pagination with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PaginationMultiError, or
// nil if none found.
func (m *Pagination) ValidateAll() error {
	return m.validate(true)
}

func (m *Pagination) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageSize(); val < 1 || val > 1000 {
		err := PaginationValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.PageToken != nil {

		if l := utf8.RuneCountInString(m.GetPageToken()); l < 0 || l > 64 {
			err := PaginationValidationError{
				field:  "PageToken",
				reason: "value length must be between 0 and 64 runes, inclusive",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return PaginationMultiError(errors)
	}

	return nil
}

// PaginationMultiError is an error wrapping multiple validation errors
// returned by Pagination.ValidateAll() if the designated constraints aren't met.
type PaginationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaginationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaginationMultiError) AllErrors() []error { return m }

// PaginationValidationError is the validation error returned by
// Pagination.Validate if the designated constraints aren't met.
type PaginationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaginationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaginationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaginationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaginationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaginationValidationError) ErrorName() string { return "PaginationValidationError" }

// Error satisfies the builtin error interface
func (e PaginationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPagination.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaginationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaginationValidationError{}

// Validate checks the field values on PaginatedResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PaginatedResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PaginatedResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PaginatedResponseMultiError, or nil if none found.
func (m *PaginatedResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PaginatedResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NextPageTokens

	if m.TotalSize != nil {
		// no validation rules for TotalSize
	}

	if len(errors) > 0 {
		return PaginatedResponseMultiError(errors)
	}

	return nil
}

// PaginatedResponseMultiError is an error wrapping multiple validation errors
// returned by PaginatedResponse.ValidateAll() if the designated constraints
// aren't met.
type PaginatedResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaginatedResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaginatedResponseMultiError) AllErrors() []error { return m }

// PaginatedResponseValidationError is the validation error returned by
// PaginatedResponse.Validate if the designated constraints aren't met.
type PaginatedResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaginatedResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaginatedResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaginatedResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaginatedResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaginatedResponseValidationError) ErrorName() string {
	return "PaginatedResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PaginatedResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPaginatedResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaginatedResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaginatedResponseValidationError{}
