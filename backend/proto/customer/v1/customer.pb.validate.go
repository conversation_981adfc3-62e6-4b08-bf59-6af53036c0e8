// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/customer/v1/customer.proto

package customerpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Customer with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Customer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Customer with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CustomerMultiError, or nil
// if none found.
func (m *Customer) ValidateAll() error {
	return m.validate(true)
}

func (m *Customer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CompanyId

	// no validation rules for PreferredBusinessId

	// no validation rules for AccountId

	// no validation rules for PhoneNumber

	// no validation rules for CustomerCode

	// no validation rules for LifeCycle

	// no validation rules for ActionState

	// no validation rules for Source

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AvatarPath

	// no validation rules for GivenName

	// no validation rules for FamilyName

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetBirthTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "BirthTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "BirthTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBirthTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "BirthTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContact()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Contact",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Contact",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContact()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "Contact",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AllocateStaffId

	if all {
		switch v := interface{}(m.GetAdditionalInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "AdditionalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "AdditionalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "AdditionalInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for State

	// no validation rules for CustomizeLifeCycleId

	// no validation rules for CustomizeActionStateId

	// no validation rules for ClientColor

	if len(errors) > 0 {
		return CustomerMultiError(errors)
	}

	return nil
}

// CustomerMultiError is an error wrapping multiple validation errors returned
// by Customer.ValidateAll() if the designated constraints aren't met.
type CustomerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerMultiError) AllErrors() []error { return m }

// CustomerValidationError is the validation error returned by
// Customer.Validate if the designated constraints aren't met.
type CustomerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerValidationError) ErrorName() string { return "CustomerValidationError" }

// Error satisfies the builtin error interface
func (e CustomerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerValidationError{}

// Validate checks the field values on Address with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Address) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Address with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AddressMultiError, or nil if none found.
func (m *Address) ValidateAll() error {
	return m.validate(true)
}

func (m *Address) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CompanyId

	// no validation rules for CustomerId

	// no validation rules for Address1

	// no validation rules for Address2

	// no validation rules for City

	// no validation rules for State

	// no validation rules for RegionCode

	// no validation rules for Zipcode

	// no validation rules for Lat

	// no validation rules for Lng

	// no validation rules for Status

	// no validation rules for IsPrimary

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddressMultiError(errors)
	}

	return nil
}

// AddressMultiError is an error wrapping multiple validation errors returned
// by Address.ValidateAll() if the designated constraints aren't met.
type AddressMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddressMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddressMultiError) AllErrors() []error { return m }

// AddressValidationError is the validation error returned by Address.Validate
// if the designated constraints aren't met.
type AddressValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddressValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddressValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddressValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddressValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddressValidationError) ErrorName() string { return "AddressValidationError" }

// Error satisfies the builtin error interface
func (e AddressValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddress.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddressValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddressValidationError{}

// Validate checks the field values on CustomerContact with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CustomerContact) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerContact with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerContactMultiError, or nil if none found.
func (m *CustomerContact) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerContact) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for BusinessId

	// no validation rules for CustomerId

	// no validation rules for GivenName

	// no validation rules for FamilyName

	// no validation rules for PhoneNumber

	// no validation rules for Email

	// no validation rules for Title

	// no validation rules for Type

	// no validation rules for IsPrimary

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerContactValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerContactValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerContactValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerContactValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerContactValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerContactValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CompanyId

	// no validation rules for E164PhoneNumber

	if len(errors) > 0 {
		return CustomerContactMultiError(errors)
	}

	return nil
}

// CustomerContactMultiError is an error wrapping multiple validation errors
// returned by CustomerContact.ValidateAll() if the designated constraints
// aren't met.
type CustomerContactMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerContactMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerContactMultiError) AllErrors() []error { return m }

// CustomerContactValidationError is the validation error returned by
// CustomerContact.Validate if the designated constraints aren't met.
type CustomerContactValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerContactValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerContactValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerContactValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerContactValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerContactValidationError) ErrorName() string { return "CustomerContactValidationError" }

// Error satisfies the builtin error interface
func (e CustomerContactValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerContact.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerContactValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerContactValidationError{}

// Validate checks the field values on Task with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Task) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Task with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TaskMultiError, or nil if none found.
func (m *Task) ValidateAll() error {
	return m.validate(true)
}

func (m *Task) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for State

	if m.AllocateStaffId != nil {
		// no validation rules for AllocateStaffId
	}

	if m.CompleteTime != nil {

		if all {
			switch v := interface{}(m.GetCompleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TaskValidationError{
						field:  "CompleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TaskValidationError{
						field:  "CompleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCompleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TaskValidationError{
					field:  "CompleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TaskMultiError(errors)
	}

	return nil
}

// TaskMultiError is an error wrapping multiple validation errors returned by
// Task.ValidateAll() if the designated constraints aren't met.
type TaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskMultiError) AllErrors() []error { return m }

// TaskValidationError is the validation error returned by Task.Validate if the
// designated constraints aren't met.
type TaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskValidationError) ErrorName() string { return "TaskValidationError" }

// Error satisfies the builtin error interface
func (e TaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTask.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskValidationError{}

// Validate checks the field values on HistoryLog with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HistoryLog) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HistoryLog with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HistoryLogMultiError, or
// nil if none found.
func (m *HistoryLog) ValidateAll() error {
	return m.validate(true)
}

func (m *HistoryLog) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CustomerId

	// no validation rules for CustomerName

	// no validation rules for CustomerPhoneNumber

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HistoryLogValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HistoryLogValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HistoryLogValidationError{
				field:  "Action",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StaffId

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HistoryLogValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HistoryLogValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HistoryLogValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Source

	// no validation rules for SourceId

	// no validation rules for SourceName

	if len(errors) > 0 {
		return HistoryLogMultiError(errors)
	}

	return nil
}

// HistoryLogMultiError is an error wrapping multiple validation errors
// returned by HistoryLog.ValidateAll() if the designated constraints aren't met.
type HistoryLogMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HistoryLogMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HistoryLogMultiError) AllErrors() []error { return m }

// HistoryLogValidationError is the validation error returned by
// HistoryLog.Validate if the designated constraints aren't met.
type HistoryLogValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HistoryLogValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HistoryLogValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HistoryLogValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HistoryLogValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HistoryLogValidationError) ErrorName() string { return "HistoryLogValidationError" }

// Error satisfies the builtin error interface
func (e HistoryLogValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHistoryLog.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HistoryLogValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HistoryLogValidationError{}

// Validate checks the field values on CustomizeLifeCycle with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomizeLifeCycle) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomizeLifeCycle with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomizeLifeCycleMultiError, or nil if none found.
func (m *CustomizeLifeCycle) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomizeLifeCycle) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Sort

	// no validation rules for IsDefault

	if len(errors) > 0 {
		return CustomizeLifeCycleMultiError(errors)
	}

	return nil
}

// CustomizeLifeCycleMultiError is an error wrapping multiple validation errors
// returned by CustomizeLifeCycle.ValidateAll() if the designated constraints
// aren't met.
type CustomizeLifeCycleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomizeLifeCycleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomizeLifeCycleMultiError) AllErrors() []error { return m }

// CustomizeLifeCycleValidationError is the validation error returned by
// CustomizeLifeCycle.Validate if the designated constraints aren't met.
type CustomizeLifeCycleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomizeLifeCycleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomizeLifeCycleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomizeLifeCycleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomizeLifeCycleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomizeLifeCycleValidationError) ErrorName() string {
	return "CustomizeLifeCycleValidationError"
}

// Error satisfies the builtin error interface
func (e CustomizeLifeCycleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomizeLifeCycle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomizeLifeCycleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomizeLifeCycleValidationError{}

// Validate checks the field values on CustomizeActionState with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomizeActionState) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomizeActionState with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomizeActionStateMultiError, or nil if none found.
func (m *CustomizeActionState) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomizeActionState) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Sort

	// no validation rules for Color

	if len(errors) > 0 {
		return CustomizeActionStateMultiError(errors)
	}

	return nil
}

// CustomizeActionStateMultiError is an error wrapping multiple validation
// errors returned by CustomizeActionState.ValidateAll() if the designated
// constraints aren't met.
type CustomizeActionStateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomizeActionStateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomizeActionStateMultiError) AllErrors() []error { return m }

// CustomizeActionStateValidationError is the validation error returned by
// CustomizeActionState.Validate if the designated constraints aren't met.
type CustomizeActionStateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomizeActionStateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomizeActionStateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomizeActionStateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomizeActionStateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomizeActionStateValidationError) ErrorName() string {
	return "CustomizeActionStateValidationError"
}

// Error satisfies the builtin error interface
func (e CustomizeActionStateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomizeActionState.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomizeActionStateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomizeActionStateValidationError{}

// Validate checks the field values on Customer_AdditionalInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Customer_AdditionalInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Customer_AdditionalInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Customer_AdditionalInfoMultiError, or nil if none found.
func (m *Customer_AdditionalInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *Customer_AdditionalInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReferralSourceId

	// no validation rules for ReferralSourceDesc

	// no validation rules for PreferredGroomerId

	// no validation rules for PreferredFrequencyDay

	// no validation rules for PreferredFrequencyType

	if len(errors) > 0 {
		return Customer_AdditionalInfoMultiError(errors)
	}

	return nil
}

// Customer_AdditionalInfoMultiError is an error wrapping multiple validation
// errors returned by Customer_AdditionalInfo.ValidateAll() if the designated
// constraints aren't met.
type Customer_AdditionalInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Customer_AdditionalInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Customer_AdditionalInfoMultiError) AllErrors() []error { return m }

// Customer_AdditionalInfoValidationError is the validation error returned by
// Customer_AdditionalInfo.Validate if the designated constraints aren't met.
type Customer_AdditionalInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Customer_AdditionalInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Customer_AdditionalInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Customer_AdditionalInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Customer_AdditionalInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Customer_AdditionalInfoValidationError) ErrorName() string {
	return "Customer_AdditionalInfoValidationError"
}

// Error satisfies the builtin error interface
func (e Customer_AdditionalInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomer_AdditionalInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Customer_AdditionalInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Customer_AdditionalInfoValidationError{}

// Validate checks the field values on HistoryLog_Message with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HistoryLog_Message) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HistoryLog_Message with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HistoryLog_MessageMultiError, or nil if none found.
func (m *HistoryLog_Message) ValidateAll() error {
	return m.validate(true)
}

func (m *HistoryLog_Message) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MessageId

	// no validation rules for Text

	// no validation rules for State

	// no validation rules for FailReason

	// no validation rules for Direction

	if len(errors) > 0 {
		return HistoryLog_MessageMultiError(errors)
	}

	return nil
}

// HistoryLog_MessageMultiError is an error wrapping multiple validation errors
// returned by HistoryLog_Message.ValidateAll() if the designated constraints
// aren't met.
type HistoryLog_MessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HistoryLog_MessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HistoryLog_MessageMultiError) AllErrors() []error { return m }

// HistoryLog_MessageValidationError is the validation error returned by
// HistoryLog_Message.Validate if the designated constraints aren't met.
type HistoryLog_MessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HistoryLog_MessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HistoryLog_MessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HistoryLog_MessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HistoryLog_MessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HistoryLog_MessageValidationError) ErrorName() string {
	return "HistoryLog_MessageValidationError"
}

// Error satisfies the builtin error interface
func (e HistoryLog_MessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHistoryLog_Message.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HistoryLog_MessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HistoryLog_MessageValidationError{}

// Validate checks the field values on HistoryLog_Call with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HistoryLog_Call) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HistoryLog_Call with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HistoryLog_CallMultiError, or nil if none found.
func (m *HistoryLog_Call) ValidateAll() error {
	return m.validate(true)
}

func (m *HistoryLog_Call) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CallId

	// no validation rules for Text

	// no validation rules for State

	// no validation rules for FailReason

	// no validation rules for Direction

	if len(errors) > 0 {
		return HistoryLog_CallMultiError(errors)
	}

	return nil
}

// HistoryLog_CallMultiError is an error wrapping multiple validation errors
// returned by HistoryLog_Call.ValidateAll() if the designated constraints
// aren't met.
type HistoryLog_CallMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HistoryLog_CallMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HistoryLog_CallMultiError) AllErrors() []error { return m }

// HistoryLog_CallValidationError is the validation error returned by
// HistoryLog_Call.Validate if the designated constraints aren't met.
type HistoryLog_CallValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HistoryLog_CallValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HistoryLog_CallValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HistoryLog_CallValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HistoryLog_CallValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HistoryLog_CallValidationError) ErrorName() string { return "HistoryLog_CallValidationError" }

// Error satisfies the builtin error interface
func (e HistoryLog_CallValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHistoryLog_Call.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HistoryLog_CallValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HistoryLog_CallValidationError{}

// Validate checks the field values on HistoryLog_Note with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HistoryLog_Note) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HistoryLog_Note with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HistoryLog_NoteMultiError, or nil if none found.
func (m *HistoryLog_Note) ValidateAll() error {
	return m.validate(true)
}

func (m *HistoryLog_Note) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	if len(errors) > 0 {
		return HistoryLog_NoteMultiError(errors)
	}

	return nil
}

// HistoryLog_NoteMultiError is an error wrapping multiple validation errors
// returned by HistoryLog_Note.ValidateAll() if the designated constraints
// aren't met.
type HistoryLog_NoteMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HistoryLog_NoteMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HistoryLog_NoteMultiError) AllErrors() []error { return m }

// HistoryLog_NoteValidationError is the validation error returned by
// HistoryLog_Note.Validate if the designated constraints aren't met.
type HistoryLog_NoteValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HistoryLog_NoteValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HistoryLog_NoteValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HistoryLog_NoteValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HistoryLog_NoteValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HistoryLog_NoteValidationError) ErrorName() string { return "HistoryLog_NoteValidationError" }

// Error satisfies the builtin error interface
func (e HistoryLog_NoteValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHistoryLog_Note.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HistoryLog_NoteValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HistoryLog_NoteValidationError{}

// Validate checks the field values on HistoryLog_Task with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HistoryLog_Task) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HistoryLog_Task with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HistoryLog_TaskMultiError, or nil if none found.
func (m *HistoryLog_Task) ValidateAll() error {
	return m.validate(true)
}

func (m *HistoryLog_Task) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetTask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HistoryLog_TaskValidationError{
					field:  "Task",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HistoryLog_TaskValidationError{
					field:  "Task",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HistoryLog_TaskValidationError{
				field:  "Task",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HistoryLog_TaskMultiError(errors)
	}

	return nil
}

// HistoryLog_TaskMultiError is an error wrapping multiple validation errors
// returned by HistoryLog_Task.ValidateAll() if the designated constraints
// aren't met.
type HistoryLog_TaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HistoryLog_TaskMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HistoryLog_TaskMultiError) AllErrors() []error { return m }

// HistoryLog_TaskValidationError is the validation error returned by
// HistoryLog_Task.Validate if the designated constraints aren't met.
type HistoryLog_TaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HistoryLog_TaskValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HistoryLog_TaskValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HistoryLog_TaskValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HistoryLog_TaskValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HistoryLog_TaskValidationError) ErrorName() string { return "HistoryLog_TaskValidationError" }

// Error satisfies the builtin error interface
func (e HistoryLog_TaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHistoryLog_Task.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HistoryLog_TaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HistoryLog_TaskValidationError{}

// Validate checks the field values on HistoryLog_Convert with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HistoryLog_Convert) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HistoryLog_Convert with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HistoryLog_ConvertMultiError, or nil if none found.
func (m *HistoryLog_Convert) ValidateAll() error {
	return m.validate(true)
}

func (m *HistoryLog_Convert) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OriginType

	// no validation rules for TargetType

	if len(errors) > 0 {
		return HistoryLog_ConvertMultiError(errors)
	}

	return nil
}

// HistoryLog_ConvertMultiError is an error wrapping multiple validation errors
// returned by HistoryLog_Convert.ValidateAll() if the designated constraints
// aren't met.
type HistoryLog_ConvertMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HistoryLog_ConvertMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HistoryLog_ConvertMultiError) AllErrors() []error { return m }

// HistoryLog_ConvertValidationError is the validation error returned by
// HistoryLog_Convert.Validate if the designated constraints aren't met.
type HistoryLog_ConvertValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HistoryLog_ConvertValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HistoryLog_ConvertValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HistoryLog_ConvertValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HistoryLog_ConvertValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HistoryLog_ConvertValidationError) ErrorName() string {
	return "HistoryLog_ConvertValidationError"
}

// Error satisfies the builtin error interface
func (e HistoryLog_ConvertValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHistoryLog_Convert.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HistoryLog_ConvertValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HistoryLog_ConvertValidationError{}

// Validate checks the field values on HistoryLog_Create with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HistoryLog_Create) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HistoryLog_Create with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HistoryLog_CreateMultiError, or nil if none found.
func (m *HistoryLog_Create) ValidateAll() error {
	return m.validate(true)
}

func (m *HistoryLog_Create) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return HistoryLog_CreateMultiError(errors)
	}

	return nil
}

// HistoryLog_CreateMultiError is an error wrapping multiple validation errors
// returned by HistoryLog_Create.ValidateAll() if the designated constraints
// aren't met.
type HistoryLog_CreateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HistoryLog_CreateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HistoryLog_CreateMultiError) AllErrors() []error { return m }

// HistoryLog_CreateValidationError is the validation error returned by
// HistoryLog_Create.Validate if the designated constraints aren't met.
type HistoryLog_CreateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HistoryLog_CreateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HistoryLog_CreateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HistoryLog_CreateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HistoryLog_CreateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HistoryLog_CreateValidationError) ErrorName() string {
	return "HistoryLog_CreateValidationError"
}

// Error satisfies the builtin error interface
func (e HistoryLog_CreateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHistoryLog_Create.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HistoryLog_CreateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HistoryLog_CreateValidationError{}

// Validate checks the field values on HistoryLog_Action with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HistoryLog_Action) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HistoryLog_Action with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HistoryLog_ActionMultiError, or nil if none found.
func (m *HistoryLog_Action) ValidateAll() error {
	return m.validate(true)
}

func (m *HistoryLog_Action) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Action.(type) {
	case *HistoryLog_Action_Message:
		if v == nil {
			err := HistoryLog_ActionValidationError{
				field:  "Action",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMessage()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HistoryLog_ActionValidationError{
						field:  "Message",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HistoryLog_ActionValidationError{
						field:  "Message",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMessage()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HistoryLog_ActionValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *HistoryLog_Action_Call:
		if v == nil {
			err := HistoryLog_ActionValidationError{
				field:  "Action",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCall()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HistoryLog_ActionValidationError{
						field:  "Call",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HistoryLog_ActionValidationError{
						field:  "Call",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCall()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HistoryLog_ActionValidationError{
					field:  "Call",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *HistoryLog_Action_Note:
		if v == nil {
			err := HistoryLog_ActionValidationError{
				field:  "Action",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNote()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HistoryLog_ActionValidationError{
						field:  "Note",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HistoryLog_ActionValidationError{
						field:  "Note",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNote()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HistoryLog_ActionValidationError{
					field:  "Note",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *HistoryLog_Action_Task:
		if v == nil {
			err := HistoryLog_ActionValidationError{
				field:  "Action",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTask()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HistoryLog_ActionValidationError{
						field:  "Task",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HistoryLog_ActionValidationError{
						field:  "Task",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTask()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HistoryLog_ActionValidationError{
					field:  "Task",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *HistoryLog_Action_Convert:
		if v == nil {
			err := HistoryLog_ActionValidationError{
				field:  "Action",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetConvert()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HistoryLog_ActionValidationError{
						field:  "Convert",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HistoryLog_ActionValidationError{
						field:  "Convert",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetConvert()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HistoryLog_ActionValidationError{
					field:  "Convert",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *HistoryLog_Action_Create:
		if v == nil {
			err := HistoryLog_ActionValidationError{
				field:  "Action",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCreate()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HistoryLog_ActionValidationError{
						field:  "Create",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HistoryLog_ActionValidationError{
						field:  "Create",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreate()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HistoryLog_ActionValidationError{
					field:  "Create",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return HistoryLog_ActionMultiError(errors)
	}

	return nil
}

// HistoryLog_ActionMultiError is an error wrapping multiple validation errors
// returned by HistoryLog_Action.ValidateAll() if the designated constraints
// aren't met.
type HistoryLog_ActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HistoryLog_ActionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HistoryLog_ActionMultiError) AllErrors() []error { return m }

// HistoryLog_ActionValidationError is the validation error returned by
// HistoryLog_Action.Validate if the designated constraints aren't met.
type HistoryLog_ActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HistoryLog_ActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HistoryLog_ActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HistoryLog_ActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HistoryLog_ActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HistoryLog_ActionValidationError) ErrorName() string {
	return "HistoryLog_ActionValidationError"
}

// Error satisfies the builtin error interface
func (e HistoryLog_ActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHistoryLog_Action.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HistoryLog_ActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HistoryLog_ActionValidationError{}
