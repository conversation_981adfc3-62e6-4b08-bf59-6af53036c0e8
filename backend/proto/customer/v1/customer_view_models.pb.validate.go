// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/customer/v1/customer_view_models.proto

package customerpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CustomerView with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CustomerView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerView with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CustomerViewMultiError, or
// nil if none found.
func (m *CustomerView) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CompanyId

	// no validation rules for StaffId

	// no validation rules for IsDefault

	// no validation rules for Title

	if all {
		switch v := interface{}(m.GetOrderBy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerViewValidationError{
					field:  "OrderBy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerViewValidationError{
					field:  "OrderBy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderBy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerViewValidationError{
				field:  "OrderBy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerViewValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerViewValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerViewValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	if len(errors) > 0 {
		return CustomerViewMultiError(errors)
	}

	return nil
}

// CustomerViewMultiError is an error wrapping multiple validation errors
// returned by CustomerView.ValidateAll() if the designated constraints aren't met.
type CustomerViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerViewMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerViewMultiError) AllErrors() []error { return m }

// CustomerViewValidationError is the validation error returned by
// CustomerView.Validate if the designated constraints aren't met.
type CustomerViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerViewValidationError) ErrorName() string { return "CustomerViewValidationError" }

// Error satisfies the builtin error interface
func (e CustomerViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerViewValidationError{}

// Validate checks the field values on CustomerView_OrderBy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomerView_OrderBy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerView_OrderBy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerView_OrderByMultiError, or nil if none found.
func (m *CustomerView_OrderBy) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerView_OrderBy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Property

	// no validation rules for Order

	if len(errors) > 0 {
		return CustomerView_OrderByMultiError(errors)
	}

	return nil
}

// CustomerView_OrderByMultiError is an error wrapping multiple validation
// errors returned by CustomerView_OrderBy.ValidateAll() if the designated
// constraints aren't met.
type CustomerView_OrderByMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerView_OrderByMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerView_OrderByMultiError) AllErrors() []error { return m }

// CustomerView_OrderByValidationError is the validation error returned by
// CustomerView_OrderBy.Validate if the designated constraints aren't met.
type CustomerView_OrderByValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerView_OrderByValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerView_OrderByValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerView_OrderByValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerView_OrderByValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerView_OrderByValidationError) ErrorName() string {
	return "CustomerView_OrderByValidationError"
}

// Error satisfies the builtin error interface
func (e CustomerView_OrderByValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerView_OrderBy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerView_OrderByValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerView_OrderByValidationError{}

// Validate checks the field values on CustomerView_Filter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomerView_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerView_Filter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerView_FilterMultiError, or nil if none found.
func (m *CustomerView_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerView_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomerView_FilterValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomerView_FilterValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomerView_FilterValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Type != nil {
		// no validation rules for Type
	}

	if m.Operator != nil {
		// no validation rules for Operator
	}

	if m.Property != nil {
		// no validation rules for Property
	}

	if m.Value != nil {
		// no validation rules for Value
	}

	if len(errors) > 0 {
		return CustomerView_FilterMultiError(errors)
	}

	return nil
}

// CustomerView_FilterMultiError is an error wrapping multiple validation
// errors returned by CustomerView_Filter.ValidateAll() if the designated
// constraints aren't met.
type CustomerView_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerView_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerView_FilterMultiError) AllErrors() []error { return m }

// CustomerView_FilterValidationError is the validation error returned by
// CustomerView_Filter.Validate if the designated constraints aren't met.
type CustomerView_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerView_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerView_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerView_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerView_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerView_FilterValidationError) ErrorName() string {
	return "CustomerView_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e CustomerView_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerView_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerView_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerView_FilterValidationError{}
