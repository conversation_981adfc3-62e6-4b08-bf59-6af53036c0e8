// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/customer/v2/common.proto

package customerpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on OrganizationRef with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OrganizationRef) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OrganizationRef with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OrganizationRefMultiError, or nil if none found.
func (m *OrganizationRef) ValidateAll() error {
	return m.validate(true)
}

func (m *OrganizationRef) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Id

	if len(errors) > 0 {
		return OrganizationRefMultiError(errors)
	}

	return nil
}

// OrganizationRefMultiError is an error wrapping multiple validation errors
// returned by OrganizationRef.ValidateAll() if the designated constraints
// aren't met.
type OrganizationRefMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrganizationRefMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrganizationRefMultiError) AllErrors() []error { return m }

// OrganizationRefValidationError is the validation error returned by
// OrganizationRef.Validate if the designated constraints aren't met.
type OrganizationRefValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrganizationRefValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrganizationRefValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrganizationRefValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrganizationRefValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrganizationRefValidationError) ErrorName() string { return "OrganizationRefValidationError" }

// Error satisfies the builtin error interface
func (e OrganizationRefValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrganizationRef.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrganizationRefValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrganizationRefValidationError{}

// Validate checks the field values on EntityRelation with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EntityRelation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityRelation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EntityRelationMultiError,
// or nil if none found.
func (m *EntityRelation) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityRelation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PrimaryType

	// no validation rules for PrimaryId

	// no validation rules for RelatedType

	// no validation rules for RelatedId

	if len(errors) > 0 {
		return EntityRelationMultiError(errors)
	}

	return nil
}

// EntityRelationMultiError is an error wrapping multiple validation errors
// returned by EntityRelation.ValidateAll() if the designated constraints
// aren't met.
type EntityRelationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityRelationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityRelationMultiError) AllErrors() []error { return m }

// EntityRelationValidationError is the validation error returned by
// EntityRelation.Validate if the designated constraints aren't met.
type EntityRelationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityRelationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityRelationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityRelationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityRelationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityRelationValidationError) ErrorName() string { return "EntityRelationValidationError" }

// Error satisfies the builtin error interface
func (e EntityRelationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityRelation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityRelationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityRelationValidationError{}
