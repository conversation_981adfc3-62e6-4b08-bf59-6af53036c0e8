

syntax = "proto3";

package backend.proto.customer.v2;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.customer.v2";

// 这个pb 是customer 域下共用的结构
//
// universal organizational structure
message OrganizationRef {
  // organization type enumeration
  enum Type {
    // 0 is reserved for unspecified
    TYPE_UNSPECIFIED = 0;
    // 1 is reserved for business
    BUSINESS = 1;
    // 2 is reserved for company
    COMPANY = 2;
    // 3 is reserved for enterprise
    ENTERPRISE = 3;
  }

  // type of the organization
  Type type = 1;
  // id
  int64 id = 2;
}
// entity relation
// 这个实体用来表示关联关系
//
// 如何表示：
// 举例子这个实体在tag里，且这个tag是contact的tag
// 那么primary_type就是tag，primary_id就是tag的id
// 那么related_type就是contact，related_id就是contact的id
message EntityRelation {
  // entity type enumeration
  enum Type {
    // 0 is reserved for unspecified
    TYPE_UNSPECIFIED = 0;
    // contact
    CONTACT = 1;
    // lead
    LEAD = 2;
    // opportunity
    OPPORTUNITY = 3;
    // tag
    TAG = 4;
    // source
    SOURCE = 5;
    // custom field
    CUSTOM_FIELD = 6;
    // address
    ADDRESS = 7;
    // activity log
    ACTIVITY_LOG = 8;
  }
  // primary type
  Type primary_type = 1;
  // primary id
  int64 primary_id = 2;

  // related type
  Type related_type = 3;
  // related id
  int64 related_id = 4;
}

