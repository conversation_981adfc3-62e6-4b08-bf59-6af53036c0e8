// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/customer/v2/common.proto

package customerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// organization type enumeration
type OrganizationRef_Type int32

const (
	// 0 is reserved for unspecified
	OrganizationRef_TYPE_UNSPECIFIED OrganizationRef_Type = 0
	// 1 is reserved for business
	OrganizationRef_BUSINESS OrganizationRef_Type = 1
	// 2 is reserved for company
	OrganizationRef_COMPANY OrganizationRef_Type = 2
	// 3 is reserved for enterprise
	OrganizationRef_ENTERPRISE OrganizationRef_Type = 3
)

// Enum value maps for OrganizationRef_Type.
var (
	OrganizationRef_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "BUSINESS",
		2: "COMPANY",
		3: "ENTERPRISE",
	}
	OrganizationRef_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"BUSINESS":         1,
		"COMPANY":          2,
		"ENTERPRISE":       3,
	}
)

func (x OrganizationRef_Type) Enum() *OrganizationRef_Type {
	p := new(OrganizationRef_Type)
	*p = x
	return p
}

func (x OrganizationRef_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrganizationRef_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_common_proto_enumTypes[0].Descriptor()
}

func (OrganizationRef_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_common_proto_enumTypes[0]
}

func (x OrganizationRef_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrganizationRef_Type.Descriptor instead.
func (OrganizationRef_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_common_proto_rawDescGZIP(), []int{0, 0}
}

// entity type enumeration
type EntityRelation_Type int32

const (
	// 0 is reserved for unspecified
	EntityRelation_TYPE_UNSPECIFIED EntityRelation_Type = 0
	// contact
	EntityRelation_CONTACT EntityRelation_Type = 1
	// lead
	EntityRelation_LEAD EntityRelation_Type = 2
	// opportunity
	EntityRelation_OPPORTUNITY EntityRelation_Type = 3
	// tag
	EntityRelation_TAG EntityRelation_Type = 4
	// source
	EntityRelation_SOURCE EntityRelation_Type = 5
	// custom field
	EntityRelation_CUSTOM_FIELD EntityRelation_Type = 6
	// address
	EntityRelation_ADDRESS EntityRelation_Type = 7
	// activity log
	EntityRelation_ACTIVITY_LOG EntityRelation_Type = 8
)

// Enum value maps for EntityRelation_Type.
var (
	EntityRelation_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "CONTACT",
		2: "LEAD",
		3: "OPPORTUNITY",
		4: "TAG",
		5: "SOURCE",
		6: "CUSTOM_FIELD",
		7: "ADDRESS",
		8: "ACTIVITY_LOG",
	}
	EntityRelation_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"CONTACT":          1,
		"LEAD":             2,
		"OPPORTUNITY":      3,
		"TAG":              4,
		"SOURCE":           5,
		"CUSTOM_FIELD":     6,
		"ADDRESS":          7,
		"ACTIVITY_LOG":     8,
	}
)

func (x EntityRelation_Type) Enum() *EntityRelation_Type {
	p := new(EntityRelation_Type)
	*p = x
	return p
}

func (x EntityRelation_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EntityRelation_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_common_proto_enumTypes[1].Descriptor()
}

func (EntityRelation_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_common_proto_enumTypes[1]
}

func (x EntityRelation_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EntityRelation_Type.Descriptor instead.
func (EntityRelation_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_common_proto_rawDescGZIP(), []int{1, 0}
}

// 这个pb 是customer 域下共用的结构
//
// universal organizational structure
type OrganizationRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// type of the organization
	Type OrganizationRef_Type `protobuf:"varint,1,opt,name=type,proto3,enum=backend.proto.customer.v2.OrganizationRef_Type" json:"type,omitempty"`
	// id
	Id            int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrganizationRef) Reset() {
	*x = OrganizationRef{}
	mi := &file_backend_proto_customer_v2_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrganizationRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrganizationRef) ProtoMessage() {}

func (x *OrganizationRef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrganizationRef.ProtoReflect.Descriptor instead.
func (*OrganizationRef) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_common_proto_rawDescGZIP(), []int{0}
}

func (x *OrganizationRef) GetType() OrganizationRef_Type {
	if x != nil {
		return x.Type
	}
	return OrganizationRef_TYPE_UNSPECIFIED
}

func (x *OrganizationRef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// entity relation
// 这个实体用来表示关联关系
//
// 如何表示：
// 举例子这个实体在tag里，且这个tag是contact的tag
// 那么primary_type就是tag，primary_id就是tag的id
// 那么related_type就是contact，related_id就是contact的id
type EntityRelation struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// primary type
	PrimaryType EntityRelation_Type `protobuf:"varint,1,opt,name=primary_type,json=primaryType,proto3,enum=backend.proto.customer.v2.EntityRelation_Type" json:"primary_type,omitempty"`
	// primary id
	PrimaryId int64 `protobuf:"varint,2,opt,name=primary_id,json=primaryId,proto3" json:"primary_id,omitempty"`
	// related type
	RelatedType EntityRelation_Type `protobuf:"varint,3,opt,name=related_type,json=relatedType,proto3,enum=backend.proto.customer.v2.EntityRelation_Type" json:"related_type,omitempty"`
	// related id
	RelatedId     int64 `protobuf:"varint,4,opt,name=related_id,json=relatedId,proto3" json:"related_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EntityRelation) Reset() {
	*x = EntityRelation{}
	mi := &file_backend_proto_customer_v2_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityRelation) ProtoMessage() {}

func (x *EntityRelation) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityRelation.ProtoReflect.Descriptor instead.
func (*EntityRelation) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_common_proto_rawDescGZIP(), []int{1}
}

func (x *EntityRelation) GetPrimaryType() EntityRelation_Type {
	if x != nil {
		return x.PrimaryType
	}
	return EntityRelation_TYPE_UNSPECIFIED
}

func (x *EntityRelation) GetPrimaryId() int64 {
	if x != nil {
		return x.PrimaryId
	}
	return 0
}

func (x *EntityRelation) GetRelatedType() EntityRelation_Type {
	if x != nil {
		return x.RelatedType
	}
	return EntityRelation_TYPE_UNSPECIFIED
}

func (x *EntityRelation) GetRelatedId() int64 {
	if x != nil {
		return x.RelatedId
	}
	return 0
}

var File_backend_proto_customer_v2_common_proto protoreflect.FileDescriptor

const file_backend_proto_customer_v2_common_proto_rawDesc = "" +
	"\n" +
	"&backend/proto/customer/v2/common.proto\x12\x19backend.proto.customer.v2\"\xaf\x01\n" +
	"\x0fOrganizationRef\x12C\n" +
	"\x04type\x18\x01 \x01(\x0e2/.backend.proto.customer.v2.OrganizationRef.TypeR\x04type\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x03R\x02id\"G\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bBUSINESS\x10\x01\x12\v\n" +
	"\aCOMPANY\x10\x02\x12\x0e\n" +
	"\n" +
	"ENTERPRISE\x10\x03\"\x81\x03\n" +
	"\x0eEntityRelation\x12Q\n" +
	"\fprimary_type\x18\x01 \x01(\x0e2..backend.proto.customer.v2.EntityRelation.TypeR\vprimaryType\x12\x1d\n" +
	"\n" +
	"primary_id\x18\x02 \x01(\x03R\tprimaryId\x12Q\n" +
	"\frelated_type\x18\x03 \x01(\x0e2..backend.proto.customer.v2.EntityRelation.TypeR\vrelatedType\x12\x1d\n" +
	"\n" +
	"related_id\x18\x04 \x01(\x03R\trelatedId\"\x8a\x01\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aCONTACT\x10\x01\x12\b\n" +
	"\x04LEAD\x10\x02\x12\x0f\n" +
	"\vOPPORTUNITY\x10\x03\x12\a\n" +
	"\x03TAG\x10\x04\x12\n" +
	"\n" +
	"\x06SOURCE\x10\x05\x12\x10\n" +
	"\fCUSTOM_FIELD\x10\x06\x12\v\n" +
	"\aADDRESS\x10\a\x12\x10\n" +
	"\fACTIVITY_LOG\x10\bBk\n" +
	"#com.moego.backend.proto.customer.v2P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpbb\x06proto3"

var (
	file_backend_proto_customer_v2_common_proto_rawDescOnce sync.Once
	file_backend_proto_customer_v2_common_proto_rawDescData []byte
)

func file_backend_proto_customer_v2_common_proto_rawDescGZIP() []byte {
	file_backend_proto_customer_v2_common_proto_rawDescOnce.Do(func() {
		file_backend_proto_customer_v2_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_common_proto_rawDesc), len(file_backend_proto_customer_v2_common_proto_rawDesc)))
	})
	return file_backend_proto_customer_v2_common_proto_rawDescData
}

var file_backend_proto_customer_v2_common_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_backend_proto_customer_v2_common_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_customer_v2_common_proto_goTypes = []any{
	(OrganizationRef_Type)(0), // 0: backend.proto.customer.v2.OrganizationRef.Type
	(EntityRelation_Type)(0),  // 1: backend.proto.customer.v2.EntityRelation.Type
	(*OrganizationRef)(nil),   // 2: backend.proto.customer.v2.OrganizationRef
	(*EntityRelation)(nil),    // 3: backend.proto.customer.v2.EntityRelation
}
var file_backend_proto_customer_v2_common_proto_depIdxs = []int32{
	0, // 0: backend.proto.customer.v2.OrganizationRef.type:type_name -> backend.proto.customer.v2.OrganizationRef.Type
	1, // 1: backend.proto.customer.v2.EntityRelation.primary_type:type_name -> backend.proto.customer.v2.EntityRelation.Type
	1, // 2: backend.proto.customer.v2.EntityRelation.related_type:type_name -> backend.proto.customer.v2.EntityRelation.Type
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_backend_proto_customer_v2_common_proto_init() }
func file_backend_proto_customer_v2_common_proto_init() {
	if File_backend_proto_customer_v2_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_common_proto_rawDesc), len(file_backend_proto_customer_v2_common_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_customer_v2_common_proto_goTypes,
		DependencyIndexes: file_backend_proto_customer_v2_common_proto_depIdxs,
		EnumInfos:         file_backend_proto_customer_v2_common_proto_enumTypes,
		MessageInfos:      file_backend_proto_customer_v2_common_proto_msgTypes,
	}.Build()
	File_backend_proto_customer_v2_common_proto = out.File
	file_backend_proto_customer_v2_common_proto_goTypes = nil
	file_backend_proto_customer_v2_common_proto_depIdxs = nil
}
