// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/pet/v1/pet_service.proto

package petpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SearchPetRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchPetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchPetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchPetRequestMultiError, or nil if none found.
func (m *SearchPetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchPetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageSize(); val < 1 || val > 1000 {
		err := SearchPetRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.SearchCriteria.(type) {
	case *SearchPetRequest_Term:
		if v == nil {
			err := SearchPetRequestValidationError{
				field:  "SearchCriteria",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if l := utf8.RuneCountInString(m.GetTerm()); l < 1 || l > 100 {
			err := SearchPetRequestValidationError{
				field:  "Term",
				reason: "value length must be between 1 and 100 runes, inclusive",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *SearchPetRequest_PetFilter:
		if v == nil {
			err := SearchPetRequestValidationError{
				field:  "SearchCriteria",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPetFilter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchPetRequestValidationError{
						field:  "PetFilter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchPetRequestValidationError{
						field:  "PetFilter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPetFilter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchPetRequestValidationError{
					field:  "PetFilter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if m.CompanyId != nil {
		// no validation rules for CompanyId
	}

	if m.PageToken != nil {
		// no validation rules for PageToken
	}

	if len(errors) > 0 {
		return SearchPetRequestMultiError(errors)
	}

	return nil
}

// SearchPetRequestMultiError is an error wrapping multiple validation errors
// returned by SearchPetRequest.ValidateAll() if the designated constraints
// aren't met.
type SearchPetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchPetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchPetRequestMultiError) AllErrors() []error { return m }

// SearchPetRequestValidationError is the validation error returned by
// SearchPetRequest.Validate if the designated constraints aren't met.
type SearchPetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchPetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchPetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchPetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchPetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchPetRequestValidationError) ErrorName() string { return "SearchPetRequestValidationError" }

// Error satisfies the builtin error interface
func (e SearchPetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchPetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchPetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchPetRequestValidationError{}

// Validate checks the field values on SearchPetResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchPetResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchPetResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchPetResponseMultiError, or nil if none found.
func (m *SearchPetResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchPetResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchPetResponseValidationError{
						field:  fmt.Sprintf("Pets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchPetResponseValidationError{
						field:  fmt.Sprintf("Pets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchPetResponseValidationError{
					field:  fmt.Sprintf("Pets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if len(errors) > 0 {
		return SearchPetResponseMultiError(errors)
	}

	return nil
}

// SearchPetResponseMultiError is an error wrapping multiple validation errors
// returned by SearchPetResponse.ValidateAll() if the designated constraints
// aren't met.
type SearchPetResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchPetResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchPetResponseMultiError) AllErrors() []error { return m }

// SearchPetResponseValidationError is the validation error returned by
// SearchPetResponse.Validate if the designated constraints aren't met.
type SearchPetResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchPetResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchPetResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchPetResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchPetResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchPetResponseValidationError) ErrorName() string {
	return "SearchPetResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SearchPetResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchPetResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchPetResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchPetResponseValidationError{}

// Validate checks the field values on IndexPetDocumentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IndexPetDocumentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IndexPetDocumentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IndexPetDocumentRequestMultiError, or nil if none found.
func (m *IndexPetDocumentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IndexPetDocumentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Flags.(type) {
	case *IndexPetDocumentRequest_Company_:
		if v == nil {
			err := IndexPetDocumentRequestValidationError{
				field:  "Flags",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCompany()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, IndexPetDocumentRequestValidationError{
						field:  "Company",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, IndexPetDocumentRequestValidationError{
						field:  "Company",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCompany()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return IndexPetDocumentRequestValidationError{
					field:  "Company",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *IndexPetDocumentRequest_Customer_:
		if v == nil {
			err := IndexPetDocumentRequestValidationError{
				field:  "Flags",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCustomer()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, IndexPetDocumentRequestValidationError{
						field:  "Customer",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, IndexPetDocumentRequestValidationError{
						field:  "Customer",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCustomer()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return IndexPetDocumentRequestValidationError{
					field:  "Customer",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *IndexPetDocumentRequest_Pet_:
		if v == nil {
			err := IndexPetDocumentRequestValidationError{
				field:  "Flags",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPet()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, IndexPetDocumentRequestValidationError{
						field:  "Pet",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, IndexPetDocumentRequestValidationError{
						field:  "Pet",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPet()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return IndexPetDocumentRequestValidationError{
					field:  "Pet",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return IndexPetDocumentRequestMultiError(errors)
	}

	return nil
}

// IndexPetDocumentRequestMultiError is an error wrapping multiple validation
// errors returned by IndexPetDocumentRequest.ValidateAll() if the designated
// constraints aren't met.
type IndexPetDocumentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IndexPetDocumentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IndexPetDocumentRequestMultiError) AllErrors() []error { return m }

// IndexPetDocumentRequestValidationError is the validation error returned by
// IndexPetDocumentRequest.Validate if the designated constraints aren't met.
type IndexPetDocumentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IndexPetDocumentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IndexPetDocumentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IndexPetDocumentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IndexPetDocumentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IndexPetDocumentRequestValidationError) ErrorName() string {
	return "IndexPetDocumentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IndexPetDocumentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIndexPetDocumentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IndexPetDocumentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IndexPetDocumentRequestValidationError{}

// Validate checks the field values on IndexPetDocumentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IndexPetDocumentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IndexPetDocumentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IndexPetDocumentResponseMultiError, or nil if none found.
func (m *IndexPetDocumentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IndexPetDocumentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetErrors() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, IndexPetDocumentResponseValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, IndexPetDocumentResponseValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return IndexPetDocumentResponseValidationError{
					field:  fmt.Sprintf("Errors[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return IndexPetDocumentResponseMultiError(errors)
	}

	return nil
}

// IndexPetDocumentResponseMultiError is an error wrapping multiple validation
// errors returned by IndexPetDocumentResponse.ValidateAll() if the designated
// constraints aren't met.
type IndexPetDocumentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IndexPetDocumentResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IndexPetDocumentResponseMultiError) AllErrors() []error { return m }

// IndexPetDocumentResponseValidationError is the validation error returned by
// IndexPetDocumentResponse.Validate if the designated constraints aren't met.
type IndexPetDocumentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IndexPetDocumentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IndexPetDocumentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IndexPetDocumentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IndexPetDocumentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IndexPetDocumentResponseValidationError) ErrorName() string {
	return "IndexPetDocumentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IndexPetDocumentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIndexPetDocumentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IndexPetDocumentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IndexPetDocumentResponseValidationError{}

// Validate checks the field values on CreatePetRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreatePetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePetRequestMultiError, or nil if none found.
func (m *CreatePetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreatePetRequestValidationError{
					field:  "Pet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreatePetRequestValidationError{
					field:  "Pet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreatePetRequestValidationError{
				field:  "Pet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreatePetRequestMultiError(errors)
	}

	return nil
}

// CreatePetRequestMultiError is an error wrapping multiple validation errors
// returned by CreatePetRequest.ValidateAll() if the designated constraints
// aren't met.
type CreatePetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePetRequestMultiError) AllErrors() []error { return m }

// CreatePetRequestValidationError is the validation error returned by
// CreatePetRequest.Validate if the designated constraints aren't met.
type CreatePetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePetRequestValidationError) ErrorName() string { return "CreatePetRequestValidationError" }

// Error satisfies the builtin error interface
func (e CreatePetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePetRequestValidationError{}

// Validate checks the field values on BatchCreatePetsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchCreatePetsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchCreatePetsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchCreatePetsRequestMultiError, or nil if none found.
func (m *BatchCreatePetsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchCreatePetsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchCreatePetsRequestValidationError{
						field:  fmt.Sprintf("Requests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchCreatePetsRequestValidationError{
						field:  fmt.Sprintf("Requests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchCreatePetsRequestValidationError{
					field:  fmt.Sprintf("Requests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Parent

	if len(errors) > 0 {
		return BatchCreatePetsRequestMultiError(errors)
	}

	return nil
}

// BatchCreatePetsRequestMultiError is an error wrapping multiple validation
// errors returned by BatchCreatePetsRequest.ValidateAll() if the designated
// constraints aren't met.
type BatchCreatePetsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchCreatePetsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchCreatePetsRequestMultiError) AllErrors() []error { return m }

// BatchCreatePetsRequestValidationError is the validation error returned by
// BatchCreatePetsRequest.Validate if the designated constraints aren't met.
type BatchCreatePetsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchCreatePetsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchCreatePetsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchCreatePetsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchCreatePetsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchCreatePetsRequestValidationError) ErrorName() string {
	return "BatchCreatePetsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchCreatePetsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchCreatePetsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchCreatePetsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchCreatePetsRequestValidationError{}

// Validate checks the field values on BatchCreatePetsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchCreatePetsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchCreatePetsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchCreatePetsResponseMultiError, or nil if none found.
func (m *BatchCreatePetsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchCreatePetsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchCreatePetsResponseValidationError{
						field:  fmt.Sprintf("Pets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchCreatePetsResponseValidationError{
						field:  fmt.Sprintf("Pets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchCreatePetsResponseValidationError{
					field:  fmt.Sprintf("Pets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchCreatePetsResponseMultiError(errors)
	}

	return nil
}

// BatchCreatePetsResponseMultiError is an error wrapping multiple validation
// errors returned by BatchCreatePetsResponse.ValidateAll() if the designated
// constraints aren't met.
type BatchCreatePetsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchCreatePetsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchCreatePetsResponseMultiError) AllErrors() []error { return m }

// BatchCreatePetsResponseValidationError is the validation error returned by
// BatchCreatePetsResponse.Validate if the designated constraints aren't met.
type BatchCreatePetsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchCreatePetsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchCreatePetsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchCreatePetsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchCreatePetsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchCreatePetsResponseValidationError) ErrorName() string {
	return "BatchCreatePetsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BatchCreatePetsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchCreatePetsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchCreatePetsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchCreatePetsResponseValidationError{}

// Validate checks the field values on UpdatePetRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdatePetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdatePetRequestMultiError, or nil if none found.
func (m *UpdatePetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdatePetRequestValidationError{
					field:  "Pet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdatePetRequestValidationError{
					field:  "Pet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdatePetRequestValidationError{
				field:  "Pet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdatePetRequestMultiError(errors)
	}

	return nil
}

// UpdatePetRequestMultiError is an error wrapping multiple validation errors
// returned by UpdatePetRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdatePetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePetRequestMultiError) AllErrors() []error { return m }

// UpdatePetRequestValidationError is the validation error returned by
// UpdatePetRequest.Validate if the designated constraints aren't met.
type UpdatePetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePetRequestValidationError) ErrorName() string { return "UpdatePetRequestValidationError" }

// Error satisfies the builtin error interface
func (e UpdatePetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePetRequestValidationError{}

// Validate checks the field values on ListPetRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListPetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPetRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListPetRequestMultiError,
// or nil if none found.
func (m *ListPetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListPetRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListPetRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListPetRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetParent() <= 0 {
		err := ListPetRequestValidationError{
			field:  "Parent",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val < 1 || val > 1000 {
		err := ListPetRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.Pagination.(type) {
	case *ListPetRequest_PageToken:
		if v == nil {
			err := ListPetRequestValidationError{
				field:  "Pagination",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PageToken
	case *ListPetRequest_Page:
		if v == nil {
			err := ListPetRequestValidationError{
				field:  "Pagination",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetPage() < 1 {
			err := ListPetRequestValidationError{
				field:  "Page",
				reason: "value must be greater than or equal to 1",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	default:
		_ = v // ensures v is used
	}

	if m.OrderBy != nil {
		// no validation rules for OrderBy
	}

	if len(errors) > 0 {
		return ListPetRequestMultiError(errors)
	}

	return nil
}

// ListPetRequestMultiError is an error wrapping multiple validation errors
// returned by ListPetRequest.ValidateAll() if the designated constraints
// aren't met.
type ListPetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPetRequestMultiError) AllErrors() []error { return m }

// ListPetRequestValidationError is the validation error returned by
// ListPetRequest.Validate if the designated constraints aren't met.
type ListPetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPetRequestValidationError) ErrorName() string { return "ListPetRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListPetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPetRequestValidationError{}

// Validate checks the field values on ListPetResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListPetResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPetResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPetResponseMultiError, or nil if none found.
func (m *ListPetResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPetResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListPetResponseValidationError{
						field:  fmt.Sprintf("Pets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListPetResponseValidationError{
						field:  fmt.Sprintf("Pets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListPetResponseValidationError{
					field:  fmt.Sprintf("Pets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	switch v := m.Pagination.(type) {
	case *ListPetResponse_Total:
		if v == nil {
			err := ListPetResponseValidationError{
				field:  "Pagination",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Total
	case *ListPetResponse_NextPageToken:
		if v == nil {
			err := ListPetResponseValidationError{
				field:  "Pagination",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for NextPageToken
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ListPetResponseMultiError(errors)
	}

	return nil
}

// ListPetResponseMultiError is an error wrapping multiple validation errors
// returned by ListPetResponse.ValidateAll() if the designated constraints
// aren't met.
type ListPetResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPetResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPetResponseMultiError) AllErrors() []error { return m }

// ListPetResponseValidationError is the validation error returned by
// ListPetResponse.Validate if the designated constraints aren't met.
type ListPetResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPetResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPetResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPetResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPetResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPetResponseValidationError) ErrorName() string { return "ListPetResponseValidationError" }

// Error satisfies the builtin error interface
func (e ListPetResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPetResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPetResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPetResponseValidationError{}

// Validate checks the field values on DeletePetRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeletePetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletePetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeletePetRequestMultiError, or nil if none found.
func (m *DeletePetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletePetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := DeletePetRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeletePetRequestMultiError(errors)
	}

	return nil
}

// DeletePetRequestMultiError is an error wrapping multiple validation errors
// returned by DeletePetRequest.ValidateAll() if the designated constraints
// aren't met.
type DeletePetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletePetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletePetRequestMultiError) AllErrors() []error { return m }

// DeletePetRequestValidationError is the validation error returned by
// DeletePetRequest.Validate if the designated constraints aren't met.
type DeletePetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletePetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletePetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletePetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletePetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletePetRequestValidationError) ErrorName() string { return "DeletePetRequestValidationError" }

// Error satisfies the builtin error interface
func (e DeletePetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletePetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletePetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletePetRequestValidationError{}

// Validate checks the field values on SearchPetRequest_Filter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchPetRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchPetRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchPetRequest_FilterMultiError, or nil if none found.
func (m *SearchPetRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchPetRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetPetIds()) < 1 {
		err := SearchPetRequest_FilterValidationError{
			field:  "PetIds",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	_SearchPetRequest_Filter_PetIds_Unique := make(map[int64]struct{}, len(m.GetPetIds()))

	for idx, item := range m.GetPetIds() {
		_, _ = idx, item

		if _, exists := _SearchPetRequest_Filter_PetIds_Unique[item]; exists {
			err := SearchPetRequest_FilterValidationError{
				field:  fmt.Sprintf("PetIds[%v]", idx),
				reason: "repeated value must contain unique items",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		} else {
			_SearchPetRequest_Filter_PetIds_Unique[item] = struct{}{}
		}

		// no validation rules for PetIds[idx]
	}

	if len(errors) > 0 {
		return SearchPetRequest_FilterMultiError(errors)
	}

	return nil
}

// SearchPetRequest_FilterMultiError is an error wrapping multiple validation
// errors returned by SearchPetRequest_Filter.ValidateAll() if the designated
// constraints aren't met.
type SearchPetRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchPetRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchPetRequest_FilterMultiError) AllErrors() []error { return m }

// SearchPetRequest_FilterValidationError is the validation error returned by
// SearchPetRequest_Filter.Validate if the designated constraints aren't met.
type SearchPetRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchPetRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchPetRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchPetRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchPetRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchPetRequest_FilterValidationError) ErrorName() string {
	return "SearchPetRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e SearchPetRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchPetRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchPetRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchPetRequest_FilterValidationError{}

// Validate checks the field values on SearchPetResponse_Pet with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchPetResponse_Pet) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchPetResponse_Pet with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchPetResponse_PetMultiError, or nil if none found.
func (m *SearchPetResponse_Pet) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchPetResponse_Pet) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CustomerId

	// no validation rules for Name

	// no validation rules for AvatarPath

	// no validation rules for PetType

	// no validation rules for Breed

	// no validation rules for Weight

	// no validation rules for CoatType

	if all {
		switch v := interface{}(m.GetClient()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchPetResponse_PetValidationError{
					field:  "Client",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchPetResponse_PetValidationError{
					field:  "Client",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClient()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchPetResponse_PetValidationError{
				field:  "Client",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SearchPetResponse_PetMultiError(errors)
	}

	return nil
}

// SearchPetResponse_PetMultiError is an error wrapping multiple validation
// errors returned by SearchPetResponse_Pet.ValidateAll() if the designated
// constraints aren't met.
type SearchPetResponse_PetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchPetResponse_PetMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchPetResponse_PetMultiError) AllErrors() []error { return m }

// SearchPetResponse_PetValidationError is the validation error returned by
// SearchPetResponse_Pet.Validate if the designated constraints aren't met.
type SearchPetResponse_PetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchPetResponse_PetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchPetResponse_PetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchPetResponse_PetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchPetResponse_PetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchPetResponse_PetValidationError) ErrorName() string {
	return "SearchPetResponse_PetValidationError"
}

// Error satisfies the builtin error interface
func (e SearchPetResponse_PetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchPetResponse_Pet.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchPetResponse_PetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchPetResponse_PetValidationError{}

// Validate checks the field values on SearchPetResponse_Pet_Client with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchPetResponse_Pet_Client) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchPetResponse_Pet_Client with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchPetResponse_Pet_ClientMultiError, or nil if none found.
func (m *SearchPetResponse_Pet_Client) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchPetResponse_Pet_Client) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for GivenName

	// no validation rules for FamilyName

	// no validation rules for Name

	if len(errors) > 0 {
		return SearchPetResponse_Pet_ClientMultiError(errors)
	}

	return nil
}

// SearchPetResponse_Pet_ClientMultiError is an error wrapping multiple
// validation errors returned by SearchPetResponse_Pet_Client.ValidateAll() if
// the designated constraints aren't met.
type SearchPetResponse_Pet_ClientMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchPetResponse_Pet_ClientMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchPetResponse_Pet_ClientMultiError) AllErrors() []error { return m }

// SearchPetResponse_Pet_ClientValidationError is the validation error returned
// by SearchPetResponse_Pet_Client.Validate if the designated constraints
// aren't met.
type SearchPetResponse_Pet_ClientValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchPetResponse_Pet_ClientValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchPetResponse_Pet_ClientValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchPetResponse_Pet_ClientValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchPetResponse_Pet_ClientValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchPetResponse_Pet_ClientValidationError) ErrorName() string {
	return "SearchPetResponse_Pet_ClientValidationError"
}

// Error satisfies the builtin error interface
func (e SearchPetResponse_Pet_ClientValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchPetResponse_Pet_Client.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchPetResponse_Pet_ClientValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchPetResponse_Pet_ClientValidationError{}

// Validate checks the field values on IndexPetDocumentRequest_Company with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IndexPetDocumentRequest_Company) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IndexPetDocumentRequest_Company with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// IndexPetDocumentRequest_CompanyMultiError, or nil if none found.
func (m *IndexPetDocumentRequest_Company) ValidateAll() error {
	return m.validate(true)
}

func (m *IndexPetDocumentRequest_Company) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return IndexPetDocumentRequest_CompanyMultiError(errors)
	}

	return nil
}

// IndexPetDocumentRequest_CompanyMultiError is an error wrapping multiple
// validation errors returned by IndexPetDocumentRequest_Company.ValidateAll()
// if the designated constraints aren't met.
type IndexPetDocumentRequest_CompanyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IndexPetDocumentRequest_CompanyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IndexPetDocumentRequest_CompanyMultiError) AllErrors() []error { return m }

// IndexPetDocumentRequest_CompanyValidationError is the validation error
// returned by IndexPetDocumentRequest_Company.Validate if the designated
// constraints aren't met.
type IndexPetDocumentRequest_CompanyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IndexPetDocumentRequest_CompanyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IndexPetDocumentRequest_CompanyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IndexPetDocumentRequest_CompanyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IndexPetDocumentRequest_CompanyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IndexPetDocumentRequest_CompanyValidationError) ErrorName() string {
	return "IndexPetDocumentRequest_CompanyValidationError"
}

// Error satisfies the builtin error interface
func (e IndexPetDocumentRequest_CompanyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIndexPetDocumentRequest_Company.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IndexPetDocumentRequest_CompanyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IndexPetDocumentRequest_CompanyValidationError{}

// Validate checks the field values on IndexPetDocumentRequest_Customer with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *IndexPetDocumentRequest_Customer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IndexPetDocumentRequest_Customer with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// IndexPetDocumentRequest_CustomerMultiError, or nil if none found.
func (m *IndexPetDocumentRequest_Customer) ValidateAll() error {
	return m.validate(true)
}

func (m *IndexPetDocumentRequest_Customer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return IndexPetDocumentRequest_CustomerMultiError(errors)
	}

	return nil
}

// IndexPetDocumentRequest_CustomerMultiError is an error wrapping multiple
// validation errors returned by
// IndexPetDocumentRequest_Customer.ValidateAll() if the designated
// constraints aren't met.
type IndexPetDocumentRequest_CustomerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IndexPetDocumentRequest_CustomerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IndexPetDocumentRequest_CustomerMultiError) AllErrors() []error { return m }

// IndexPetDocumentRequest_CustomerValidationError is the validation error
// returned by IndexPetDocumentRequest_Customer.Validate if the designated
// constraints aren't met.
type IndexPetDocumentRequest_CustomerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IndexPetDocumentRequest_CustomerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IndexPetDocumentRequest_CustomerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IndexPetDocumentRequest_CustomerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IndexPetDocumentRequest_CustomerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IndexPetDocumentRequest_CustomerValidationError) ErrorName() string {
	return "IndexPetDocumentRequest_CustomerValidationError"
}

// Error satisfies the builtin error interface
func (e IndexPetDocumentRequest_CustomerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIndexPetDocumentRequest_Customer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IndexPetDocumentRequest_CustomerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IndexPetDocumentRequest_CustomerValidationError{}

// Validate checks the field values on IndexPetDocumentRequest_Pet with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IndexPetDocumentRequest_Pet) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IndexPetDocumentRequest_Pet with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IndexPetDocumentRequest_PetMultiError, or nil if none found.
func (m *IndexPetDocumentRequest_Pet) ValidateAll() error {
	return m.validate(true)
}

func (m *IndexPetDocumentRequest_Pet) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return IndexPetDocumentRequest_PetMultiError(errors)
	}

	return nil
}

// IndexPetDocumentRequest_PetMultiError is an error wrapping multiple
// validation errors returned by IndexPetDocumentRequest_Pet.ValidateAll() if
// the designated constraints aren't met.
type IndexPetDocumentRequest_PetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IndexPetDocumentRequest_PetMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IndexPetDocumentRequest_PetMultiError) AllErrors() []error { return m }

// IndexPetDocumentRequest_PetValidationError is the validation error returned
// by IndexPetDocumentRequest_Pet.Validate if the designated constraints
// aren't met.
type IndexPetDocumentRequest_PetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IndexPetDocumentRequest_PetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IndexPetDocumentRequest_PetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IndexPetDocumentRequest_PetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IndexPetDocumentRequest_PetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IndexPetDocumentRequest_PetValidationError) ErrorName() string {
	return "IndexPetDocumentRequest_PetValidationError"
}

// Error satisfies the builtin error interface
func (e IndexPetDocumentRequest_PetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIndexPetDocumentRequest_Pet.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IndexPetDocumentRequest_PetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IndexPetDocumentRequest_PetValidationError{}

// Validate checks the field values on IndexPetDocumentResponse_Error with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IndexPetDocumentResponse_Error) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IndexPetDocumentResponse_Error with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// IndexPetDocumentResponse_ErrorMultiError, or nil if none found.
func (m *IndexPetDocumentResponse_Error) ValidateAll() error {
	return m.validate(true)
}

func (m *IndexPetDocumentResponse_Error) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrorType

	// no validation rules for ErrorReason

	// no validation rules for Id

	if len(errors) > 0 {
		return IndexPetDocumentResponse_ErrorMultiError(errors)
	}

	return nil
}

// IndexPetDocumentResponse_ErrorMultiError is an error wrapping multiple
// validation errors returned by IndexPetDocumentResponse_Error.ValidateAll()
// if the designated constraints aren't met.
type IndexPetDocumentResponse_ErrorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IndexPetDocumentResponse_ErrorMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IndexPetDocumentResponse_ErrorMultiError) AllErrors() []error { return m }

// IndexPetDocumentResponse_ErrorValidationError is the validation error
// returned by IndexPetDocumentResponse_Error.Validate if the designated
// constraints aren't met.
type IndexPetDocumentResponse_ErrorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IndexPetDocumentResponse_ErrorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IndexPetDocumentResponse_ErrorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IndexPetDocumentResponse_ErrorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IndexPetDocumentResponse_ErrorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IndexPetDocumentResponse_ErrorValidationError) ErrorName() string {
	return "IndexPetDocumentResponse_ErrorValidationError"
}

// Error satisfies the builtin error interface
func (e IndexPetDocumentResponse_ErrorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIndexPetDocumentResponse_Error.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IndexPetDocumentResponse_ErrorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IndexPetDocumentResponse_ErrorValidationError{}

// Validate checks the field values on UpdatePetRequest_UpdatePet with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdatePetRequest_UpdatePet) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePetRequest_UpdatePet with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdatePetRequest_UpdatePetMultiError, or nil if none found.
func (m *UpdatePetRequest_UpdatePet) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePetRequest_UpdatePet) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.PetType != nil {
		// no validation rules for PetType
	}

	if m.PetGender != nil {
		// no validation rules for PetGender
	}

	if m.Breed != nil {
		// no validation rules for Breed
	}

	if len(errors) > 0 {
		return UpdatePetRequest_UpdatePetMultiError(errors)
	}

	return nil
}

// UpdatePetRequest_UpdatePetMultiError is an error wrapping multiple
// validation errors returned by UpdatePetRequest_UpdatePet.ValidateAll() if
// the designated constraints aren't met.
type UpdatePetRequest_UpdatePetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePetRequest_UpdatePetMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePetRequest_UpdatePetMultiError) AllErrors() []error { return m }

// UpdatePetRequest_UpdatePetValidationError is the validation error returned
// by UpdatePetRequest_UpdatePet.Validate if the designated constraints aren't met.
type UpdatePetRequest_UpdatePetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePetRequest_UpdatePetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePetRequest_UpdatePetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePetRequest_UpdatePetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePetRequest_UpdatePetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePetRequest_UpdatePetValidationError) ErrorName() string {
	return "UpdatePetRequest_UpdatePetValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePetRequest_UpdatePetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePetRequest_UpdatePet.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePetRequest_UpdatePetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePetRequest_UpdatePetValidationError{}

// Validate checks the field values on ListPetRequest_Filter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPetRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPetRequest_Filter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPetRequest_FilterMultiError, or nil if none found.
func (m *ListPetRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPetRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	_ListPetRequest_Filter_PetIds_Unique := make(map[int64]struct{}, len(m.GetPetIds()))

	for idx, item := range m.GetPetIds() {
		_, _ = idx, item

		if _, exists := _ListPetRequest_Filter_PetIds_Unique[item]; exists {
			err := ListPetRequest_FilterValidationError{
				field:  fmt.Sprintf("PetIds[%v]", idx),
				reason: "repeated value must contain unique items",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		} else {
			_ListPetRequest_Filter_PetIds_Unique[item] = struct{}{}
		}

		// no validation rules for PetIds[idx]
	}

	if m.PetType != nil {
		// no validation rules for PetType
	}

	if len(errors) > 0 {
		return ListPetRequest_FilterMultiError(errors)
	}

	return nil
}

// ListPetRequest_FilterMultiError is an error wrapping multiple validation
// errors returned by ListPetRequest_Filter.ValidateAll() if the designated
// constraints aren't met.
type ListPetRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPetRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPetRequest_FilterMultiError) AllErrors() []error { return m }

// ListPetRequest_FilterValidationError is the validation error returned by
// ListPetRequest_Filter.Validate if the designated constraints aren't met.
type ListPetRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPetRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPetRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPetRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPetRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPetRequest_FilterValidationError) ErrorName() string {
	return "ListPetRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListPetRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPetRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPetRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPetRequest_FilterValidationError{}
