// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/pet/v1/pet.proto

package petpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on <PERSON> with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Pet) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on <PERSON> with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PetMultiError, or nil if none found.
func (m *Pet) ValidateAll() error {
	return m.validate(true)
}

func (m *Pet) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CompanyId

	// no validation rules for CustomerId

	// no validation rules for BusinessId

	// no validation rules for Name

	// no validation rules for PetType

	// no validation rules for Breed

	// no validation rules for Gender

	// no validation rules for Mixed

	// no validation rules for State

	if len(errors) > 0 {
		return PetMultiError(errors)
	}

	return nil
}

// PetMultiError is an error wrapping multiple validation errors returned by
// Pet.ValidateAll() if the designated constraints aren't met.
type PetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PetMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PetMultiError) AllErrors() []error { return m }

// PetValidationError is the validation error returned by Pet.Validate if the
// designated constraints aren't met.
type PetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PetValidationError) ErrorName() string { return "PetValidationError" }

// Error satisfies the builtin error interface
func (e PetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPet.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PetValidationError{}
