// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/helloworld/v1/helloworld_service.proto

package helloworldpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SendPingRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SendPingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendPingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendPingRequestMultiError, or nil if none found.
func (m *SendPingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendPingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPing()) < 1 {
		err := SendPingRequestValidationError{
			field:  "Ping",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SendPingRequestMultiError(errors)
	}

	return nil
}

// SendPingRequestMultiError is an error wrapping multiple validation errors
// returned by SendPingRequest.ValidateAll() if the designated constraints
// aren't met.
type SendPingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendPingRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendPingRequestMultiError) AllErrors() []error { return m }

// SendPingRequestValidationError is the validation error returned by
// SendPingRequest.Validate if the designated constraints aren't met.
type SendPingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendPingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendPingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendPingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendPingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendPingRequestValidationError) ErrorName() string { return "SendPingRequestValidationError" }

// Error satisfies the builtin error interface
func (e SendPingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendPingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendPingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendPingRequestValidationError{}

// Validate checks the field values on SendPingResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SendPingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendPingResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendPingResponseMultiError, or nil if none found.
func (m *SendPingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SendPingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Pong

	if len(errors) > 0 {
		return SendPingResponseMultiError(errors)
	}

	return nil
}

// SendPingResponseMultiError is an error wrapping multiple validation errors
// returned by SendPingResponse.ValidateAll() if the designated constraints
// aren't met.
type SendPingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendPingResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendPingResponseMultiError) AllErrors() []error { return m }

// SendPingResponseValidationError is the validation error returned by
// SendPingResponse.Validate if the designated constraints aren't met.
type SendPingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendPingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendPingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendPingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendPingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendPingResponseValidationError) ErrorName() string { return "SendPingResponseValidationError" }

// Error satisfies the builtin error interface
func (e SendPingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendPingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendPingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendPingResponseValidationError{}
