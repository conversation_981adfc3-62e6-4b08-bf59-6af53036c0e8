// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/helloworld/v1/echo_service.proto

package helloworldpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on EchoRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EchoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EchoRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EchoRequestMultiError, or
// nil if none found.
func (m *EchoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EchoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Echo

	if len(errors) > 0 {
		return EchoRequestMultiError(errors)
	}

	return nil
}

// EchoRequestMultiError is an error wrapping multiple validation errors
// returned by EchoRequest.ValidateAll() if the designated constraints aren't met.
type EchoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EchoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EchoRequestMultiError) AllErrors() []error { return m }

// EchoRequestValidationError is the validation error returned by
// EchoRequest.Validate if the designated constraints aren't met.
type EchoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EchoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EchoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EchoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EchoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EchoRequestValidationError) ErrorName() string { return "EchoRequestValidationError" }

// Error satisfies the builtin error interface
func (e EchoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEchoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EchoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EchoRequestValidationError{}

// Validate checks the field values on EchoResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EchoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EchoResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EchoResponseMultiError, or
// nil if none found.
func (m *EchoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EchoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Echo

	if len(errors) > 0 {
		return EchoResponseMultiError(errors)
	}

	return nil
}

// EchoResponseMultiError is an error wrapping multiple validation errors
// returned by EchoResponse.ValidateAll() if the designated constraints aren't met.
type EchoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EchoResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EchoResponseMultiError) AllErrors() []error { return m }

// EchoResponseValidationError is the validation error returned by
// EchoResponse.Validate if the designated constraints aren't met.
type EchoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EchoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EchoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EchoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EchoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EchoResponseValidationError) ErrorName() string { return "EchoResponseValidationError" }

// Error satisfies the builtin error interface
func (e EchoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEchoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EchoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EchoResponseValidationError{}
