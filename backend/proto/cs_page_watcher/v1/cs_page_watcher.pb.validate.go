// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/cs_page_watcher/v1/cs_page_watcher.proto

package cs_page_watcher

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CompleteJiraRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CompleteJiraRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CompleteJiraRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CompleteJiraRequestMultiError, or nil if none found.
func (m *CompleteJiraRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CompleteJiraRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for JiraKey

	if len(errors) > 0 {
		return CompleteJiraRequestMultiError(errors)
	}

	return nil
}

// CompleteJiraRequestMultiError is an error wrapping multiple validation
// errors returned by CompleteJiraRequest.ValidateAll() if the designated
// constraints aren't met.
type CompleteJiraRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CompleteJiraRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CompleteJiraRequestMultiError) AllErrors() []error { return m }

// CompleteJiraRequestValidationError is the validation error returned by
// CompleteJiraRequest.Validate if the designated constraints aren't met.
type CompleteJiraRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CompleteJiraRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CompleteJiraRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CompleteJiraRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CompleteJiraRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CompleteJiraRequestValidationError) ErrorName() string {
	return "CompleteJiraRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CompleteJiraRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCompleteJiraRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CompleteJiraRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CompleteJiraRequestValidationError{}

// Validate checks the field values on CompleteIncidentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CompleteIncidentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CompleteIncidentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CompleteIncidentRequestMultiError, or nil if none found.
func (m *CompleteIncidentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CompleteIncidentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Title

	if len(errors) > 0 {
		return CompleteIncidentRequestMultiError(errors)
	}

	return nil
}

// CompleteIncidentRequestMultiError is an error wrapping multiple validation
// errors returned by CompleteIncidentRequest.ValidateAll() if the designated
// constraints aren't met.
type CompleteIncidentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CompleteIncidentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CompleteIncidentRequestMultiError) AllErrors() []error { return m }

// CompleteIncidentRequestValidationError is the validation error returned by
// CompleteIncidentRequest.Validate if the designated constraints aren't met.
type CompleteIncidentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CompleteIncidentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CompleteIncidentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CompleteIncidentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CompleteIncidentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CompleteIncidentRequestValidationError) ErrorName() string {
	return "CompleteIncidentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CompleteIncidentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCompleteIncidentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CompleteIncidentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CompleteIncidentRequestValidationError{}

// Validate checks the field values on DefaultResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DefaultResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DefaultResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DefaultResponseMultiError, or nil if none found.
func (m *DefaultResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DefaultResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Message

	// no validation rules for ErrorCode

	if len(errors) > 0 {
		return DefaultResponseMultiError(errors)
	}

	return nil
}

// DefaultResponseMultiError is an error wrapping multiple validation errors
// returned by DefaultResponse.ValidateAll() if the designated constraints
// aren't met.
type DefaultResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DefaultResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DefaultResponseMultiError) AllErrors() []error { return m }

// DefaultResponseValidationError is the validation error returned by
// DefaultResponse.Validate if the designated constraints aren't met.
type DefaultResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DefaultResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DefaultResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DefaultResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DefaultResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DefaultResponseValidationError) ErrorName() string { return "DefaultResponseValidationError" }

// Error satisfies the builtin error interface
func (e DefaultResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDefaultResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DefaultResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DefaultResponseValidationError{}

// Validate checks the field values on TriggerPageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerPageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerPageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TriggerPageRequestMultiError, or nil if none found.
func (m *TriggerPageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerPageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for JiraKey

	if len(errors) > 0 {
		return TriggerPageRequestMultiError(errors)
	}

	return nil
}

// TriggerPageRequestMultiError is an error wrapping multiple validation errors
// returned by TriggerPageRequest.ValidateAll() if the designated constraints
// aren't met.
type TriggerPageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerPageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerPageRequestMultiError) AllErrors() []error { return m }

// TriggerPageRequestValidationError is the validation error returned by
// TriggerPageRequest.Validate if the designated constraints aren't met.
type TriggerPageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerPageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerPageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerPageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerPageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerPageRequestValidationError) ErrorName() string {
	return "TriggerPageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerPageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerPageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerPageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerPageRequestValidationError{}
