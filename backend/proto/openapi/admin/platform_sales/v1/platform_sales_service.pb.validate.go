// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/openapi/admin/platform_sales/v1/platform_sales_service.proto

package platformsalespb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateSalesLinkRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSalesLinkRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSalesLinkRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSalesLinkRequestMultiError, or nil if none found.
func (m *CreateSalesLinkRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSalesLinkRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetCreator()) < 1 {
		err := CreateSalesLinkRequestValidationError{
			field:  "Creator",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetEmail()) < 1 {
		err := CreateSalesLinkRequestValidationError{
			field:  "Email",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateSalesLinkRequest_SubscriptionPlan_InLookup[m.GetSubscriptionPlan()]; !ok {
		err := CreateSalesLinkRequestValidationError{
			field:  "SubscriptionPlan",
			reason: "value must be in list [growth ultimate]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EnableCustomRate

	if all {
		switch v := interface{}(m.GetInPersonRate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSalesLinkRequestValidationError{
					field:  "InPersonRate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSalesLinkRequestValidationError{
					field:  "InPersonRate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInPersonRate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSalesLinkRequestValidationError{
				field:  "InPersonRate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInPersonFee()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSalesLinkRequestValidationError{
					field:  "InPersonFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSalesLinkRequestValidationError{
					field:  "InPersonFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInPersonFee()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSalesLinkRequestValidationError{
				field:  "InPersonFee",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOnlineRate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSalesLinkRequestValidationError{
					field:  "OnlineRate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSalesLinkRequestValidationError{
					field:  "OnlineRate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOnlineRate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSalesLinkRequestValidationError{
				field:  "OnlineRate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOnlineFee()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSalesLinkRequestValidationError{
					field:  "OnlineFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSalesLinkRequestValidationError{
					field:  "OnlineFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOnlineFee()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSalesLinkRequestValidationError{
				field:  "OnlineFee",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinMonthlyTransactionVolume()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSalesLinkRequestValidationError{
					field:  "MinMonthlyTransactionVolume",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSalesLinkRequestValidationError{
					field:  "MinMonthlyTransactionVolume",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinMonthlyTransactionVolume()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSalesLinkRequestValidationError{
				field:  "MinMonthlyTransactionVolume",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VanCount

	// no validation rules for GroomingLocationCount

	// no validation rules for BdLocationCount

	// no validation rules for IsAnnualPlan

	// no validation rules for ContractTermMonths

	if all {
		switch v := interface{}(m.GetAnnualPlanDiscount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSalesLinkRequestValidationError{
					field:  "AnnualPlanDiscount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSalesLinkRequestValidationError{
					field:  "AnnualPlanDiscount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnualPlanDiscount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSalesLinkRequestValidationError{
				field:  "AnnualPlanDiscount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IncludeHardware

	if all {
		switch v := interface{}(m.GetHardwareDiscount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSalesLinkRequestValidationError{
					field:  "HardwareDiscount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSalesLinkRequestValidationError{
					field:  "HardwareDiscount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHardwareDiscount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSalesLinkRequestValidationError{
				field:  "HardwareDiscount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IncludeAccounting

	if len(errors) > 0 {
		return CreateSalesLinkRequestMultiError(errors)
	}

	return nil
}

// CreateSalesLinkRequestMultiError is an error wrapping multiple validation
// errors returned by CreateSalesLinkRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateSalesLinkRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSalesLinkRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSalesLinkRequestMultiError) AllErrors() []error { return m }

// CreateSalesLinkRequestValidationError is the validation error returned by
// CreateSalesLinkRequest.Validate if the designated constraints aren't met.
type CreateSalesLinkRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSalesLinkRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSalesLinkRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSalesLinkRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSalesLinkRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSalesLinkRequestValidationError) ErrorName() string {
	return "CreateSalesLinkRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSalesLinkRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSalesLinkRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSalesLinkRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSalesLinkRequestValidationError{}

var _CreateSalesLinkRequest_SubscriptionPlan_InLookup = map[string]struct{}{
	"growth":   {},
	"ultimate": {},
}

// Validate checks the field values on CreateSalesLinkResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSalesLinkResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSalesLinkResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSalesLinkResponseMultiError, or nil if none found.
func (m *CreateSalesLinkResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSalesLinkResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SalesLink

	if len(errors) > 0 {
		return CreateSalesLinkResponseMultiError(errors)
	}

	return nil
}

// CreateSalesLinkResponseMultiError is an error wrapping multiple validation
// errors returned by CreateSalesLinkResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateSalesLinkResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSalesLinkResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSalesLinkResponseMultiError) AllErrors() []error { return m }

// CreateSalesLinkResponseValidationError is the validation error returned by
// CreateSalesLinkResponse.Validate if the designated constraints aren't met.
type CreateSalesLinkResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSalesLinkResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSalesLinkResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSalesLinkResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSalesLinkResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSalesLinkResponseValidationError) ErrorName() string {
	return "CreateSalesLinkResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSalesLinkResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSalesLinkResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSalesLinkResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSalesLinkResponseValidationError{}
