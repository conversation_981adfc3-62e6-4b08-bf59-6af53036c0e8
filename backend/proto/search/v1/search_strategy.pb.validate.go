// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/search/v1/search_strategy.proto

package searchpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TermStrategy with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TermStrategy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TermStrategy with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TermStrategyMultiError, or
// nil if none found.
func (m *TermStrategy) ValidateAll() error {
	return m.validate(true)
}

func (m *TermStrategy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetField()) < 1 {
		err := TermStrategyValidationError{
			field:  "Field",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetValue() == nil {
		err := TermStrategyValidationError{
			field:  "Value",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if a := m.GetValue(); a != nil {

	}

	if len(errors) > 0 {
		return TermStrategyMultiError(errors)
	}

	return nil
}

// TermStrategyMultiError is an error wrapping multiple validation errors
// returned by TermStrategy.ValidateAll() if the designated constraints aren't met.
type TermStrategyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TermStrategyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TermStrategyMultiError) AllErrors() []error { return m }

// TermStrategyValidationError is the validation error returned by
// TermStrategy.Validate if the designated constraints aren't met.
type TermStrategyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TermStrategyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TermStrategyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TermStrategyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TermStrategyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TermStrategyValidationError) ErrorName() string { return "TermStrategyValidationError" }

// Error satisfies the builtin error interface
func (e TermStrategyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTermStrategy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TermStrategyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TermStrategyValidationError{}

// Validate checks the field values on MatchStrategy with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MatchStrategy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MatchStrategy with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MatchStrategyMultiError, or
// nil if none found.
func (m *MatchStrategy) ValidateAll() error {
	return m.validate(true)
}

func (m *MatchStrategy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetField()) < 1 {
		err := MatchStrategyValidationError{
			field:  "Field",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetQuery()) < 1 {
		err := MatchStrategyValidationError{
			field:  "Query",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Boost

	// no validation rules for Operator

	if len(errors) > 0 {
		return MatchStrategyMultiError(errors)
	}

	return nil
}

// MatchStrategyMultiError is an error wrapping multiple validation errors
// returned by MatchStrategy.ValidateAll() if the designated constraints
// aren't met.
type MatchStrategyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MatchStrategyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MatchStrategyMultiError) AllErrors() []error { return m }

// MatchStrategyValidationError is the validation error returned by
// MatchStrategy.Validate if the designated constraints aren't met.
type MatchStrategyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MatchStrategyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MatchStrategyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MatchStrategyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MatchStrategyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MatchStrategyValidationError) ErrorName() string { return "MatchStrategyValidationError" }

// Error satisfies the builtin error interface
func (e MatchStrategyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMatchStrategy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MatchStrategyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MatchStrategyValidationError{}

// Validate checks the field values on RangeStrategy with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RangeStrategy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RangeStrategy with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RangeStrategyMultiError, or
// nil if none found.
func (m *RangeStrategy) ValidateAll() error {
	return m.validate(true)
}

func (m *RangeStrategy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetField()) < 1 {
		err := RangeStrategyValidationError{
			field:  "Field",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetGte()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RangeStrategyValidationError{
					field:  "Gte",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RangeStrategyValidationError{
					field:  "Gte",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGte()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RangeStrategyValidationError{
				field:  "Gte",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLte()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RangeStrategyValidationError{
					field:  "Lte",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RangeStrategyValidationError{
					field:  "Lte",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLte()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RangeStrategyValidationError{
				field:  "Lte",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RangeStrategyMultiError(errors)
	}

	return nil
}

// RangeStrategyMultiError is an error wrapping multiple validation errors
// returned by RangeStrategy.ValidateAll() if the designated constraints
// aren't met.
type RangeStrategyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RangeStrategyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RangeStrategyMultiError) AllErrors() []error { return m }

// RangeStrategyValidationError is the validation error returned by
// RangeStrategy.Validate if the designated constraints aren't met.
type RangeStrategyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RangeStrategyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RangeStrategyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RangeStrategyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RangeStrategyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RangeStrategyValidationError) ErrorName() string { return "RangeStrategyValidationError" }

// Error satisfies the builtin error interface
func (e RangeStrategyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRangeStrategy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RangeStrategyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RangeStrategyValidationError{}

// Validate checks the field values on WildcardStrategy with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WildcardStrategy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WildcardStrategy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WildcardStrategyMultiError, or nil if none found.
func (m *WildcardStrategy) ValidateAll() error {
	return m.validate(true)
}

func (m *WildcardStrategy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetField()) < 1 {
		err := WildcardStrategyValidationError{
			field:  "Field",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetValue()) < 1 {
		err := WildcardStrategyValidationError{
			field:  "Value",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return WildcardStrategyMultiError(errors)
	}

	return nil
}

// WildcardStrategyMultiError is an error wrapping multiple validation errors
// returned by WildcardStrategy.ValidateAll() if the designated constraints
// aren't met.
type WildcardStrategyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WildcardStrategyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WildcardStrategyMultiError) AllErrors() []error { return m }

// WildcardStrategyValidationError is the validation error returned by
// WildcardStrategy.Validate if the designated constraints aren't met.
type WildcardStrategyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WildcardStrategyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WildcardStrategyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WildcardStrategyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WildcardStrategyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WildcardStrategyValidationError) ErrorName() string { return "WildcardStrategyValidationError" }

// Error satisfies the builtin error interface
func (e WildcardStrategyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWildcardStrategy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WildcardStrategyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WildcardStrategyValidationError{}

// Validate checks the field values on TermsStrategy with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TermsStrategy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TermsStrategy with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TermsStrategyMultiError, or
// nil if none found.
func (m *TermsStrategy) ValidateAll() error {
	return m.validate(true)
}

func (m *TermsStrategy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetField()) < 1 {
		err := TermsStrategyValidationError{
			field:  "Field",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetValues()) < 1 {
		err := TermsStrategyValidationError{
			field:  "Values",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetValues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TermsStrategyValidationError{
						field:  fmt.Sprintf("Values[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TermsStrategyValidationError{
						field:  fmt.Sprintf("Values[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TermsStrategyValidationError{
					field:  fmt.Sprintf("Values[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TermsStrategyMultiError(errors)
	}

	return nil
}

// TermsStrategyMultiError is an error wrapping multiple validation errors
// returned by TermsStrategy.ValidateAll() if the designated constraints
// aren't met.
type TermsStrategyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TermsStrategyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TermsStrategyMultiError) AllErrors() []error { return m }

// TermsStrategyValidationError is the validation error returned by
// TermsStrategy.Validate if the designated constraints aren't met.
type TermsStrategyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TermsStrategyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TermsStrategyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TermsStrategyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TermsStrategyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TermsStrategyValidationError) ErrorName() string { return "TermsStrategyValidationError" }

// Error satisfies the builtin error interface
func (e TermsStrategyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTermsStrategy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TermsStrategyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TermsStrategyValidationError{}

// Validate checks the field values on BoolStrategy with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BoolStrategy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BoolStrategy with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BoolStrategyMultiError, or
// nil if none found.
func (m *BoolStrategy) ValidateAll() error {
	return m.validate(true)
}

func (m *BoolStrategy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetMust() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BoolStrategyValidationError{
						field:  fmt.Sprintf("Must[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BoolStrategyValidationError{
						field:  fmt.Sprintf("Must[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BoolStrategyValidationError{
					field:  fmt.Sprintf("Must[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMustNot() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BoolStrategyValidationError{
						field:  fmt.Sprintf("MustNot[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BoolStrategyValidationError{
						field:  fmt.Sprintf("MustNot[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BoolStrategyValidationError{
					field:  fmt.Sprintf("MustNot[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFilter() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BoolStrategyValidationError{
						field:  fmt.Sprintf("Filter[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BoolStrategyValidationError{
						field:  fmt.Sprintf("Filter[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BoolStrategyValidationError{
					field:  fmt.Sprintf("Filter[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetShould()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BoolStrategyValidationError{
					field:  "Should",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BoolStrategyValidationError{
					field:  "Should",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShould()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BoolStrategyValidationError{
				field:  "Should",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BoolStrategyMultiError(errors)
	}

	return nil
}

// BoolStrategyMultiError is an error wrapping multiple validation errors
// returned by BoolStrategy.ValidateAll() if the designated constraints aren't met.
type BoolStrategyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BoolStrategyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BoolStrategyMultiError) AllErrors() []error { return m }

// BoolStrategyValidationError is the validation error returned by
// BoolStrategy.Validate if the designated constraints aren't met.
type BoolStrategyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BoolStrategyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BoolStrategyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BoolStrategyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BoolStrategyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BoolStrategyValidationError) ErrorName() string { return "BoolStrategyValidationError" }

// Error satisfies the builtin error interface
func (e BoolStrategyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBoolStrategy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BoolStrategyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BoolStrategyValidationError{}

// Validate checks the field values on MultiMatchStrategy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MultiMatchStrategy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MultiMatchStrategy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MultiMatchStrategyMultiError, or nil if none found.
func (m *MultiMatchStrategy) ValidateAll() error {
	return m.validate(true)
}

func (m *MultiMatchStrategy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetQuery()) < 1 {
		err := MultiMatchStrategyValidationError{
			field:  "Query",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Type

	if len(m.GetFields()) < 1 {
		err := MultiMatchStrategyValidationError{
			field:  "Fields",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Fuzziness

	// no validation rules for PrefixLength

	// no validation rules for Boost

	if len(errors) > 0 {
		return MultiMatchStrategyMultiError(errors)
	}

	return nil
}

// MultiMatchStrategyMultiError is an error wrapping multiple validation errors
// returned by MultiMatchStrategy.ValidateAll() if the designated constraints
// aren't met.
type MultiMatchStrategyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MultiMatchStrategyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MultiMatchStrategyMultiError) AllErrors() []error { return m }

// MultiMatchStrategyValidationError is the validation error returned by
// MultiMatchStrategy.Validate if the designated constraints aren't met.
type MultiMatchStrategyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MultiMatchStrategyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MultiMatchStrategyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MultiMatchStrategyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MultiMatchStrategyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MultiMatchStrategyValidationError) ErrorName() string {
	return "MultiMatchStrategyValidationError"
}

// Error satisfies the builtin error interface
func (e MultiMatchStrategyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMultiMatchStrategy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MultiMatchStrategyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MultiMatchStrategyValidationError{}

// Validate checks the field values on QueryStringStrategy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryStringStrategy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryStringStrategy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryStringStrategyMultiError, or nil if none found.
func (m *QueryStringStrategy) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryStringStrategy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetQuery()) < 1 {
		err := QueryStringStrategyValidationError{
			field:  "Query",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AnalyzeWildcard

	// no validation rules for DefaultOperator

	// no validation rules for Boost

	if len(errors) > 0 {
		return QueryStringStrategyMultiError(errors)
	}

	return nil
}

// QueryStringStrategyMultiError is an error wrapping multiple validation
// errors returned by QueryStringStrategy.ValidateAll() if the designated
// constraints aren't met.
type QueryStringStrategyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryStringStrategyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryStringStrategyMultiError) AllErrors() []error { return m }

// QueryStringStrategyValidationError is the validation error returned by
// QueryStringStrategy.Validate if the designated constraints aren't met.
type QueryStringStrategyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryStringStrategyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryStringStrategyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryStringStrategyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryStringStrategyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryStringStrategyValidationError) ErrorName() string {
	return "QueryStringStrategyValidationError"
}

// Error satisfies the builtin error interface
func (e QueryStringStrategyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryStringStrategy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryStringStrategyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryStringStrategyValidationError{}

// Validate checks the field values on MatchPhraseStrategy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MatchPhraseStrategy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MatchPhraseStrategy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MatchPhraseStrategyMultiError, or nil if none found.
func (m *MatchPhraseStrategy) ValidateAll() error {
	return m.validate(true)
}

func (m *MatchPhraseStrategy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetField()) < 1 {
		err := MatchPhraseStrategyValidationError{
			field:  "Field",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetQuery()) < 1 {
		err := MatchPhraseStrategyValidationError{
			field:  "Query",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Slop

	// no validation rules for Boost

	if len(errors) > 0 {
		return MatchPhraseStrategyMultiError(errors)
	}

	return nil
}

// MatchPhraseStrategyMultiError is an error wrapping multiple validation
// errors returned by MatchPhraseStrategy.ValidateAll() if the designated
// constraints aren't met.
type MatchPhraseStrategyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MatchPhraseStrategyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MatchPhraseStrategyMultiError) AllErrors() []error { return m }

// MatchPhraseStrategyValidationError is the validation error returned by
// MatchPhraseStrategy.Validate if the designated constraints aren't met.
type MatchPhraseStrategyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MatchPhraseStrategyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MatchPhraseStrategyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MatchPhraseStrategyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MatchPhraseStrategyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MatchPhraseStrategyValidationError) ErrorName() string {
	return "MatchPhraseStrategyValidationError"
}

// Error satisfies the builtin error interface
func (e MatchPhraseStrategyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMatchPhraseStrategy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MatchPhraseStrategyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MatchPhraseStrategyValidationError{}

// Validate checks the field values on Strategy with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Strategy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Strategy with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StrategyMultiError, or nil
// if none found.
func (m *Strategy) ValidateAll() error {
	return m.validate(true)
}

func (m *Strategy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Strategy.(type) {
	case *Strategy_Term:
		if v == nil {
			err := StrategyValidationError{
				field:  "Strategy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTerm()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "Term",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "Term",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTerm()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StrategyValidationError{
					field:  "Term",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Strategy_Match:
		if v == nil {
			err := StrategyValidationError{
				field:  "Strategy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMatch()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "Match",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "Match",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMatch()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StrategyValidationError{
					field:  "Match",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Strategy_Range:
		if v == nil {
			err := StrategyValidationError{
				field:  "Strategy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRange()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "Range",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "Range",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRange()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StrategyValidationError{
					field:  "Range",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Strategy_Wildcard:
		if v == nil {
			err := StrategyValidationError{
				field:  "Strategy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWildcard()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "Wildcard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "Wildcard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWildcard()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StrategyValidationError{
					field:  "Wildcard",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Strategy_Terms:
		if v == nil {
			err := StrategyValidationError{
				field:  "Strategy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTerms()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "Terms",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "Terms",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTerms()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StrategyValidationError{
					field:  "Terms",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Strategy_Bool:
		if v == nil {
			err := StrategyValidationError{
				field:  "Strategy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBool()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "Bool",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "Bool",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBool()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StrategyValidationError{
					field:  "Bool",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Strategy_MultiMatch:
		if v == nil {
			err := StrategyValidationError{
				field:  "Strategy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMultiMatch()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "MultiMatch",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "MultiMatch",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMultiMatch()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StrategyValidationError{
					field:  "MultiMatch",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Strategy_MatchPhrase:
		if v == nil {
			err := StrategyValidationError{
				field:  "Strategy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMatchPhrase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "MatchPhrase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "MatchPhrase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMatchPhrase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StrategyValidationError{
					field:  "MatchPhrase",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Strategy_QueryString:
		if v == nil {
			err := StrategyValidationError{
				field:  "Strategy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetQueryString()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "QueryString",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StrategyValidationError{
						field:  "QueryString",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetQueryString()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StrategyValidationError{
					field:  "QueryString",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return StrategyMultiError(errors)
	}

	return nil
}

// StrategyMultiError is an error wrapping multiple validation errors returned
// by Strategy.ValidateAll() if the designated constraints aren't met.
type StrategyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StrategyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StrategyMultiError) AllErrors() []error { return m }

// StrategyValidationError is the validation error returned by
// Strategy.Validate if the designated constraints aren't met.
type StrategyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StrategyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StrategyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StrategyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StrategyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StrategyValidationError) ErrorName() string { return "StrategyValidationError" }

// Error satisfies the builtin error interface
func (e StrategyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStrategy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StrategyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StrategyValidationError{}

// Validate checks the field values on BoolStrategy_BoolShouldStrategy with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BoolStrategy_BoolShouldStrategy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BoolStrategy_BoolShouldStrategy with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BoolStrategy_BoolShouldStrategyMultiError, or nil if none found.
func (m *BoolStrategy_BoolShouldStrategy) ValidateAll() error {
	return m.validate(true)
}

func (m *BoolStrategy_BoolShouldStrategy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetStrategies() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BoolStrategy_BoolShouldStrategyValidationError{
						field:  fmt.Sprintf("Strategies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BoolStrategy_BoolShouldStrategyValidationError{
						field:  fmt.Sprintf("Strategies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BoolStrategy_BoolShouldStrategyValidationError{
					field:  fmt.Sprintf("Strategies[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for MinimumMatch

	if len(errors) > 0 {
		return BoolStrategy_BoolShouldStrategyMultiError(errors)
	}

	return nil
}

// BoolStrategy_BoolShouldStrategyMultiError is an error wrapping multiple
// validation errors returned by BoolStrategy_BoolShouldStrategy.ValidateAll()
// if the designated constraints aren't met.
type BoolStrategy_BoolShouldStrategyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BoolStrategy_BoolShouldStrategyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BoolStrategy_BoolShouldStrategyMultiError) AllErrors() []error { return m }

// BoolStrategy_BoolShouldStrategyValidationError is the validation error
// returned by BoolStrategy_BoolShouldStrategy.Validate if the designated
// constraints aren't met.
type BoolStrategy_BoolShouldStrategyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BoolStrategy_BoolShouldStrategyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BoolStrategy_BoolShouldStrategyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BoolStrategy_BoolShouldStrategyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BoolStrategy_BoolShouldStrategyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BoolStrategy_BoolShouldStrategyValidationError) ErrorName() string {
	return "BoolStrategy_BoolShouldStrategyValidationError"
}

// Error satisfies the builtin error interface
func (e BoolStrategy_BoolShouldStrategyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBoolStrategy_BoolShouldStrategy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BoolStrategy_BoolShouldStrategyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BoolStrategy_BoolShouldStrategyValidationError{}
