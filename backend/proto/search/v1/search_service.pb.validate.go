// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/search/v1/search_service.proto

package searchpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SearchDocumentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchDocumentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchDocumentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchDocumentRequestMultiError, or nil if none found.
func (m *SearchDocumentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchDocumentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetStrategy() == nil {
		err := SearchDocumentRequestValidationError{
			field:  "Strategy",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetStrategy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchDocumentRequestValidationError{
					field:  "Strategy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchDocumentRequestValidationError{
					field:  "Strategy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStrategy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchDocumentRequestValidationError{
				field:  "Strategy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetIndex()) < 1 {
		err := SearchDocumentRequestValidationError{
			field:  "Index",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPageSize() <= 0 {
		err := SearchDocumentRequestValidationError{
			field:  "PageSize",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetSearchAfter() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchDocumentRequestValidationError{
						field:  fmt.Sprintf("SearchAfter[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchDocumentRequestValidationError{
						field:  fmt.Sprintf("SearchAfter[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchDocumentRequestValidationError{
					field:  fmt.Sprintf("SearchAfter[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(m.GetSort()) < 1 {
		err := SearchDocumentRequestValidationError{
			field:  "Sort",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetSort() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchDocumentRequestValidationError{
						field:  fmt.Sprintf("Sort[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchDocumentRequestValidationError{
						field:  fmt.Sprintf("Sort[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchDocumentRequestValidationError{
					field:  fmt.Sprintf("Sort[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchDocumentRequestMultiError(errors)
	}

	return nil
}

// SearchDocumentRequestMultiError is an error wrapping multiple validation
// errors returned by SearchDocumentRequest.ValidateAll() if the designated
// constraints aren't met.
type SearchDocumentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchDocumentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchDocumentRequestMultiError) AllErrors() []error { return m }

// SearchDocumentRequestValidationError is the validation error returned by
// SearchDocumentRequest.Validate if the designated constraints aren't met.
type SearchDocumentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchDocumentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchDocumentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchDocumentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchDocumentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchDocumentRequestValidationError) ErrorName() string {
	return "SearchDocumentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SearchDocumentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchDocumentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchDocumentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchDocumentRequestValidationError{}

// Validate checks the field values on SearchDocumentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchDocumentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchDocumentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchDocumentResponseMultiError, or nil if none found.
func (m *SearchDocumentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchDocumentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetHits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchDocumentResponseValidationError{
						field:  fmt.Sprintf("Hits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchDocumentResponseValidationError{
						field:  fmt.Sprintf("Hits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchDocumentResponseValidationError{
					field:  fmt.Sprintf("Hits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for Took

	// no validation rules for TimedOut

	if all {
		switch v := interface{}(m.GetTotal()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchDocumentResponseValidationError{
					field:  "Total",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchDocumentResponseValidationError{
					field:  "Total",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotal()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchDocumentResponseValidationError{
				field:  "Total",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaxScore

	if len(errors) > 0 {
		return SearchDocumentResponseMultiError(errors)
	}

	return nil
}

// SearchDocumentResponseMultiError is an error wrapping multiple validation
// errors returned by SearchDocumentResponse.ValidateAll() if the designated
// constraints aren't met.
type SearchDocumentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchDocumentResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchDocumentResponseMultiError) AllErrors() []error { return m }

// SearchDocumentResponseValidationError is the validation error returned by
// SearchDocumentResponse.Validate if the designated constraints aren't met.
type SearchDocumentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchDocumentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchDocumentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchDocumentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchDocumentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchDocumentResponseValidationError) ErrorName() string {
	return "SearchDocumentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SearchDocumentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchDocumentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchDocumentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchDocumentResponseValidationError{}

// Validate checks the field values on SearchDocumentRequest_Sort with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchDocumentRequest_Sort) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchDocumentRequest_Sort with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchDocumentRequest_SortMultiError, or nil if none found.
func (m *SearchDocumentRequest_Sort) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchDocumentRequest_Sort) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetField()) < 1 {
		err := SearchDocumentRequest_SortValidationError{
			field:  "Field",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _SearchDocumentRequest_Sort_Order_InLookup[m.GetOrder()]; !ok {
		err := SearchDocumentRequest_SortValidationError{
			field:  "Order",
			reason: "value must be in list [ASC DESC]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SearchDocumentRequest_SortMultiError(errors)
	}

	return nil
}

// SearchDocumentRequest_SortMultiError is an error wrapping multiple
// validation errors returned by SearchDocumentRequest_Sort.ValidateAll() if
// the designated constraints aren't met.
type SearchDocumentRequest_SortMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchDocumentRequest_SortMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchDocumentRequest_SortMultiError) AllErrors() []error { return m }

// SearchDocumentRequest_SortValidationError is the validation error returned
// by SearchDocumentRequest_Sort.Validate if the designated constraints aren't met.
type SearchDocumentRequest_SortValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchDocumentRequest_SortValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchDocumentRequest_SortValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchDocumentRequest_SortValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchDocumentRequest_SortValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchDocumentRequest_SortValidationError) ErrorName() string {
	return "SearchDocumentRequest_SortValidationError"
}

// Error satisfies the builtin error interface
func (e SearchDocumentRequest_SortValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchDocumentRequest_Sort.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchDocumentRequest_SortValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchDocumentRequest_SortValidationError{}

var _SearchDocumentRequest_Sort_Order_InLookup = map[SearchDocumentRequest_Sort_Order]struct{}{
	1: {},
	2: {},
}

// Validate checks the field values on SearchDocumentResponse_Hit with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchDocumentResponse_Hit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchDocumentResponse_Hit with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchDocumentResponse_HitMultiError, or nil if none found.
func (m *SearchDocumentResponse_Hit) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchDocumentResponse_Hit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Index

	// no validation rules for Id

	// no validation rules for Score

	if all {
		switch v := interface{}(m.GetSource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchDocumentResponse_HitValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchDocumentResponse_HitValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchDocumentResponse_HitValidationError{
				field:  "Source",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSort() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchDocumentResponse_HitValidationError{
						field:  fmt.Sprintf("Sort[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchDocumentResponse_HitValidationError{
						field:  fmt.Sprintf("Sort[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchDocumentResponse_HitValidationError{
					field:  fmt.Sprintf("Sort[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchDocumentResponse_HitMultiError(errors)
	}

	return nil
}

// SearchDocumentResponse_HitMultiError is an error wrapping multiple
// validation errors returned by SearchDocumentResponse_Hit.ValidateAll() if
// the designated constraints aren't met.
type SearchDocumentResponse_HitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchDocumentResponse_HitMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchDocumentResponse_HitMultiError) AllErrors() []error { return m }

// SearchDocumentResponse_HitValidationError is the validation error returned
// by SearchDocumentResponse_Hit.Validate if the designated constraints aren't met.
type SearchDocumentResponse_HitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchDocumentResponse_HitValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchDocumentResponse_HitValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchDocumentResponse_HitValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchDocumentResponse_HitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchDocumentResponse_HitValidationError) ErrorName() string {
	return "SearchDocumentResponse_HitValidationError"
}

// Error satisfies the builtin error interface
func (e SearchDocumentResponse_HitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchDocumentResponse_Hit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchDocumentResponse_HitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchDocumentResponse_HitValidationError{}

// Validate checks the field values on SearchDocumentResponse_Total with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchDocumentResponse_Total) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchDocumentResponse_Total with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchDocumentResponse_TotalMultiError, or nil if none found.
func (m *SearchDocumentResponse_Total) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchDocumentResponse_Total) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Value

	// no validation rules for Relation

	if len(errors) > 0 {
		return SearchDocumentResponse_TotalMultiError(errors)
	}

	return nil
}

// SearchDocumentResponse_TotalMultiError is an error wrapping multiple
// validation errors returned by SearchDocumentResponse_Total.ValidateAll() if
// the designated constraints aren't met.
type SearchDocumentResponse_TotalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchDocumentResponse_TotalMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchDocumentResponse_TotalMultiError) AllErrors() []error { return m }

// SearchDocumentResponse_TotalValidationError is the validation error returned
// by SearchDocumentResponse_Total.Validate if the designated constraints
// aren't met.
type SearchDocumentResponse_TotalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchDocumentResponse_TotalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchDocumentResponse_TotalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchDocumentResponse_TotalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchDocumentResponse_TotalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchDocumentResponse_TotalValidationError) ErrorName() string {
	return "SearchDocumentResponse_TotalValidationError"
}

// Error satisfies the builtin error interface
func (e SearchDocumentResponse_TotalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchDocumentResponse_Total.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchDocumentResponse_TotalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchDocumentResponse_TotalValidationError{}
