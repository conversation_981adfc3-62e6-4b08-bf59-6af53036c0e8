// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/aistudio/v1/aistudio.proto

package aistudio

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RequestAiStudioMcpClientRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RequestAiStudioMcpClientRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RequestAiStudioMcpClientRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RequestAiStudioMcpClientRequestMultiError, or nil if none found.
func (m *RequestAiStudioMcpClientRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RequestAiStudioMcpClientRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GeminiKey

	// no validation rules for Prompt

	// no validation rules for Mcps

	// no validation rules for Envs

	// no validation rules for Model

	if len(errors) > 0 {
		return RequestAiStudioMcpClientRequestMultiError(errors)
	}

	return nil
}

// RequestAiStudioMcpClientRequestMultiError is an error wrapping multiple
// validation errors returned by RequestAiStudioMcpClientRequest.ValidateAll()
// if the designated constraints aren't met.
type RequestAiStudioMcpClientRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RequestAiStudioMcpClientRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RequestAiStudioMcpClientRequestMultiError) AllErrors() []error { return m }

// RequestAiStudioMcpClientRequestValidationError is the validation error
// returned by RequestAiStudioMcpClientRequest.Validate if the designated
// constraints aren't met.
type RequestAiStudioMcpClientRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RequestAiStudioMcpClientRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RequestAiStudioMcpClientRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RequestAiStudioMcpClientRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RequestAiStudioMcpClientRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RequestAiStudioMcpClientRequestValidationError) ErrorName() string {
	return "RequestAiStudioMcpClientRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RequestAiStudioMcpClientRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRequestAiStudioMcpClientRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RequestAiStudioMcpClientRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RequestAiStudioMcpClientRequestValidationError{}

// Validate checks the field values on RequestAiStudioMcpClientResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RequestAiStudioMcpClientResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RequestAiStudioMcpClientResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RequestAiStudioMcpClientResponseMultiError, or nil if none found.
func (m *RequestAiStudioMcpClientResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RequestAiStudioMcpClientResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RequestAiStudioMcpClientResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RequestAiStudioMcpClientResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RequestAiStudioMcpClientResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RequestAiStudioMcpClientResponseMultiError(errors)
	}

	return nil
}

// RequestAiStudioMcpClientResponseMultiError is an error wrapping multiple
// validation errors returned by
// RequestAiStudioMcpClientResponse.ValidateAll() if the designated
// constraints aren't met.
type RequestAiStudioMcpClientResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RequestAiStudioMcpClientResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RequestAiStudioMcpClientResponseMultiError) AllErrors() []error { return m }

// RequestAiStudioMcpClientResponseValidationError is the validation error
// returned by RequestAiStudioMcpClientResponse.Validate if the designated
// constraints aren't met.
type RequestAiStudioMcpClientResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RequestAiStudioMcpClientResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RequestAiStudioMcpClientResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RequestAiStudioMcpClientResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RequestAiStudioMcpClientResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RequestAiStudioMcpClientResponseValidationError) ErrorName() string {
	return "RequestAiStudioMcpClientResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RequestAiStudioMcpClientResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRequestAiStudioMcpClientResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RequestAiStudioMcpClientResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RequestAiStudioMcpClientResponseValidationError{}

// Validate checks the field values on ResponseData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ResponseData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResponseData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ResponseDataMultiError, or
// nil if none found.
func (m *ResponseData) ValidateAll() error {
	return m.validate(true)
}

func (m *ResponseData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ResponseDataMultiError(errors)
	}

	return nil
}

// ResponseDataMultiError is an error wrapping multiple validation errors
// returned by ResponseData.ValidateAll() if the designated constraints aren't met.
type ResponseDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResponseDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResponseDataMultiError) AllErrors() []error { return m }

// ResponseDataValidationError is the validation error returned by
// ResponseData.Validate if the designated constraints aren't met.
type ResponseDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResponseDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResponseDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResponseDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResponseDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResponseDataValidationError) ErrorName() string { return "ResponseDataValidationError" }

// Error satisfies the builtin error interface
func (e ResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResponseData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResponseDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResponseDataValidationError{}

// Validate checks the field values on CreateAiStudioTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAiStudioTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAiStudioTemplateRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateAiStudioTemplateRequestMultiError, or nil if none found.
func (m *CreateAiStudioTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAiStudioTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Model

	// no validation rules for Prompt

	// no validation rules for Mcps

	// no validation rules for Envs

	// no validation rules for Name

	if len(errors) > 0 {
		return CreateAiStudioTemplateRequestMultiError(errors)
	}

	return nil
}

// CreateAiStudioTemplateRequestMultiError is an error wrapping multiple
// validation errors returned by CreateAiStudioTemplateRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateAiStudioTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAiStudioTemplateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAiStudioTemplateRequestMultiError) AllErrors() []error { return m }

// CreateAiStudioTemplateRequestValidationError is the validation error
// returned by CreateAiStudioTemplateRequest.Validate if the designated
// constraints aren't met.
type CreateAiStudioTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAiStudioTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAiStudioTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAiStudioTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAiStudioTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAiStudioTemplateRequestValidationError) ErrorName() string {
	return "CreateAiStudioTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAiStudioTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAiStudioTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAiStudioTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAiStudioTemplateRequestValidationError{}

// Validate checks the field values on GetAiStudioTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAiStudioTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAiStudioTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAiStudioTemplateRequestMultiError, or nil if none found.
func (m *GetAiStudioTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAiStudioTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetAiStudioTemplateRequestMultiError(errors)
	}

	return nil
}

// GetAiStudioTemplateRequestMultiError is an error wrapping multiple
// validation errors returned by GetAiStudioTemplateRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAiStudioTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAiStudioTemplateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAiStudioTemplateRequestMultiError) AllErrors() []error { return m }

// GetAiStudioTemplateRequestValidationError is the validation error returned
// by GetAiStudioTemplateRequest.Validate if the designated constraints aren't met.
type GetAiStudioTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAiStudioTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAiStudioTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAiStudioTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAiStudioTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAiStudioTemplateRequestValidationError) ErrorName() string {
	return "GetAiStudioTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAiStudioTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAiStudioTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAiStudioTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAiStudioTemplateRequestValidationError{}

// Validate checks the field values on AiStudioTemplate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AiStudioTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AiStudioTemplate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AiStudioTemplateMultiError, or nil if none found.
func (m *AiStudioTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *AiStudioTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AiStudioTemplateValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AiStudioTemplateValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AiStudioTemplateValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AiStudioTemplateMultiError(errors)
	}

	return nil
}

// AiStudioTemplateMultiError is an error wrapping multiple validation errors
// returned by AiStudioTemplate.ValidateAll() if the designated constraints
// aren't met.
type AiStudioTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AiStudioTemplateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AiStudioTemplateMultiError) AllErrors() []error { return m }

// AiStudioTemplateValidationError is the validation error returned by
// AiStudioTemplate.Validate if the designated constraints aren't met.
type AiStudioTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AiStudioTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AiStudioTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AiStudioTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AiStudioTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AiStudioTemplateValidationError) ErrorName() string { return "AiStudioTemplateValidationError" }

// Error satisfies the builtin error interface
func (e AiStudioTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAiStudioTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AiStudioTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AiStudioTemplateValidationError{}

// Validate checks the field values on AiStudioTemplateData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AiStudioTemplateData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AiStudioTemplateData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AiStudioTemplateDataMultiError, or nil if none found.
func (m *AiStudioTemplateData) ValidateAll() error {
	return m.validate(true)
}

func (m *AiStudioTemplateData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Model

	// no validation rules for Prompt

	// no validation rules for Mcps

	// no validation rules for EnvKeys

	// no validation rules for Name

	if len(errors) > 0 {
		return AiStudioTemplateDataMultiError(errors)
	}

	return nil
}

// AiStudioTemplateDataMultiError is an error wrapping multiple validation
// errors returned by AiStudioTemplateData.ValidateAll() if the designated
// constraints aren't met.
type AiStudioTemplateDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AiStudioTemplateDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AiStudioTemplateDataMultiError) AllErrors() []error { return m }

// AiStudioTemplateDataValidationError is the validation error returned by
// AiStudioTemplateData.Validate if the designated constraints aren't met.
type AiStudioTemplateDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AiStudioTemplateDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AiStudioTemplateDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AiStudioTemplateDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AiStudioTemplateDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AiStudioTemplateDataValidationError) ErrorName() string {
	return "AiStudioTemplateDataValidationError"
}

// Error satisfies the builtin error interface
func (e AiStudioTemplateDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAiStudioTemplateData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AiStudioTemplateDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AiStudioTemplateDataValidationError{}

// Validate checks the field values on UpdateAiStudioTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAiStudioTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAiStudioTemplateRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateAiStudioTemplateRequestMultiError, or nil if none found.
func (m *UpdateAiStudioTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAiStudioTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Model

	// no validation rules for Prompt

	// no validation rules for Mcps

	// no validation rules for EnvKeys

	// no validation rules for Name

	if len(errors) > 0 {
		return UpdateAiStudioTemplateRequestMultiError(errors)
	}

	return nil
}

// UpdateAiStudioTemplateRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateAiStudioTemplateRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateAiStudioTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAiStudioTemplateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAiStudioTemplateRequestMultiError) AllErrors() []error { return m }

// UpdateAiStudioTemplateRequestValidationError is the validation error
// returned by UpdateAiStudioTemplateRequest.Validate if the designated
// constraints aren't met.
type UpdateAiStudioTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAiStudioTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAiStudioTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAiStudioTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAiStudioTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAiStudioTemplateRequestValidationError) ErrorName() string {
	return "UpdateAiStudioTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAiStudioTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAiStudioTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAiStudioTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAiStudioTemplateRequestValidationError{}

// Validate checks the field values on DeleteAiStudioTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteAiStudioTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAiStudioTemplateRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeleteAiStudioTemplateRequestMultiError, or nil if none found.
func (m *DeleteAiStudioTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAiStudioTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteAiStudioTemplateRequestMultiError(errors)
	}

	return nil
}

// DeleteAiStudioTemplateRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteAiStudioTemplateRequest.ValidateAll()
// if the designated constraints aren't met.
type DeleteAiStudioTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAiStudioTemplateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAiStudioTemplateRequestMultiError) AllErrors() []error { return m }

// DeleteAiStudioTemplateRequestValidationError is the validation error
// returned by DeleteAiStudioTemplateRequest.Validate if the designated
// constraints aren't met.
type DeleteAiStudioTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAiStudioTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAiStudioTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAiStudioTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAiStudioTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAiStudioTemplateRequestValidationError) ErrorName() string {
	return "DeleteAiStudioTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAiStudioTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAiStudioTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAiStudioTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAiStudioTemplateRequestValidationError{}

// Validate checks the field values on ListAiStudioTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAiStudioTemplateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAiStudioTemplateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAiStudioTemplateRequestMultiError, or nil if none found.
func (m *ListAiStudioTemplateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAiStudioTemplateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PerPage

	// no validation rules for Page

	if len(errors) > 0 {
		return ListAiStudioTemplateRequestMultiError(errors)
	}

	return nil
}

// ListAiStudioTemplateRequestMultiError is an error wrapping multiple
// validation errors returned by ListAiStudioTemplateRequest.ValidateAll() if
// the designated constraints aren't met.
type ListAiStudioTemplateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAiStudioTemplateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAiStudioTemplateRequestMultiError) AllErrors() []error { return m }

// ListAiStudioTemplateRequestValidationError is the validation error returned
// by ListAiStudioTemplateRequest.Validate if the designated constraints
// aren't met.
type ListAiStudioTemplateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAiStudioTemplateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAiStudioTemplateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAiStudioTemplateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAiStudioTemplateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAiStudioTemplateRequestValidationError) ErrorName() string {
	return "ListAiStudioTemplateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAiStudioTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAiStudioTemplateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAiStudioTemplateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAiStudioTemplateRequestValidationError{}

// Validate checks the field values on ListAiStudioTemplateData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAiStudioTemplateData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAiStudioTemplateData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAiStudioTemplateDataMultiError, or nil if none found.
func (m *ListAiStudioTemplateData) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAiStudioTemplateData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAiStudioTemplateDataValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAiStudioTemplateDataValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAiStudioTemplateDataValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListAiStudioTemplateDataMultiError(errors)
	}

	return nil
}

// ListAiStudioTemplateDataMultiError is an error wrapping multiple validation
// errors returned by ListAiStudioTemplateData.ValidateAll() if the designated
// constraints aren't met.
type ListAiStudioTemplateDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAiStudioTemplateDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAiStudioTemplateDataMultiError) AllErrors() []error { return m }

// ListAiStudioTemplateDataValidationError is the validation error returned by
// ListAiStudioTemplateData.Validate if the designated constraints aren't met.
type ListAiStudioTemplateDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAiStudioTemplateDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAiStudioTemplateDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAiStudioTemplateDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAiStudioTemplateDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAiStudioTemplateDataValidationError) ErrorName() string {
	return "ListAiStudioTemplateDataValidationError"
}

// Error satisfies the builtin error interface
func (e ListAiStudioTemplateDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAiStudioTemplateData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAiStudioTemplateDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAiStudioTemplateDataValidationError{}

// Validate checks the field values on ListAiStudioTemplateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAiStudioTemplateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAiStudioTemplateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAiStudioTemplateResponseMultiError, or nil if none found.
func (m *ListAiStudioTemplateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAiStudioTemplateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAiStudioTemplateResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAiStudioTemplateResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAiStudioTemplateResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAiStudioTemplateResponseMultiError(errors)
	}

	return nil
}

// ListAiStudioTemplateResponseMultiError is an error wrapping multiple
// validation errors returned by ListAiStudioTemplateResponse.ValidateAll() if
// the designated constraints aren't met.
type ListAiStudioTemplateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAiStudioTemplateResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAiStudioTemplateResponseMultiError) AllErrors() []error { return m }

// ListAiStudioTemplateResponseValidationError is the validation error returned
// by ListAiStudioTemplateResponse.Validate if the designated constraints
// aren't met.
type ListAiStudioTemplateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAiStudioTemplateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAiStudioTemplateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAiStudioTemplateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAiStudioTemplateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAiStudioTemplateResponseValidationError) ErrorName() string {
	return "ListAiStudioTemplateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAiStudioTemplateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAiStudioTemplateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAiStudioTemplateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAiStudioTemplateResponseValidationError{}

// Validate checks the field values on ListAiStudioTemplateIDRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAiStudioTemplateIDRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAiStudioTemplateIDRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListAiStudioTemplateIDRequestMultiError, or nil if none found.
func (m *ListAiStudioTemplateIDRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAiStudioTemplateIDRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListAiStudioTemplateIDRequestMultiError(errors)
	}

	return nil
}

// ListAiStudioTemplateIDRequestMultiError is an error wrapping multiple
// validation errors returned by ListAiStudioTemplateIDRequest.ValidateAll()
// if the designated constraints aren't met.
type ListAiStudioTemplateIDRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAiStudioTemplateIDRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAiStudioTemplateIDRequestMultiError) AllErrors() []error { return m }

// ListAiStudioTemplateIDRequestValidationError is the validation error
// returned by ListAiStudioTemplateIDRequest.Validate if the designated
// constraints aren't met.
type ListAiStudioTemplateIDRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAiStudioTemplateIDRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAiStudioTemplateIDRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAiStudioTemplateIDRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAiStudioTemplateIDRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAiStudioTemplateIDRequestValidationError) ErrorName() string {
	return "ListAiStudioTemplateIDRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAiStudioTemplateIDRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAiStudioTemplateIDRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAiStudioTemplateIDRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAiStudioTemplateIDRequestValidationError{}

// Validate checks the field values on AiStudioTemplateID with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AiStudioTemplateID) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AiStudioTemplateID with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AiStudioTemplateIDMultiError, or nil if none found.
func (m *AiStudioTemplateID) ValidateAll() error {
	return m.validate(true)
}

func (m *AiStudioTemplateID) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Label

	// no validation rules for Value

	if len(errors) > 0 {
		return AiStudioTemplateIDMultiError(errors)
	}

	return nil
}

// AiStudioTemplateIDMultiError is an error wrapping multiple validation errors
// returned by AiStudioTemplateID.ValidateAll() if the designated constraints
// aren't met.
type AiStudioTemplateIDMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AiStudioTemplateIDMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AiStudioTemplateIDMultiError) AllErrors() []error { return m }

// AiStudioTemplateIDValidationError is the validation error returned by
// AiStudioTemplateID.Validate if the designated constraints aren't met.
type AiStudioTemplateIDValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AiStudioTemplateIDValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AiStudioTemplateIDValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AiStudioTemplateIDValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AiStudioTemplateIDValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AiStudioTemplateIDValidationError) ErrorName() string {
	return "AiStudioTemplateIDValidationError"
}

// Error satisfies the builtin error interface
func (e AiStudioTemplateIDValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAiStudioTemplateID.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AiStudioTemplateIDValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AiStudioTemplateIDValidationError{}

// Validate checks the field values on ListAiStudioTemplateIDData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAiStudioTemplateIDData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAiStudioTemplateIDData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAiStudioTemplateIDDataMultiError, or nil if none found.
func (m *ListAiStudioTemplateIDData) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAiStudioTemplateIDData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAiStudioTemplateIDDataValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAiStudioTemplateIDDataValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAiStudioTemplateIDDataValidationError{
					field:  fmt.Sprintf("Options[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListAiStudioTemplateIDDataMultiError(errors)
	}

	return nil
}

// ListAiStudioTemplateIDDataMultiError is an error wrapping multiple
// validation errors returned by ListAiStudioTemplateIDData.ValidateAll() if
// the designated constraints aren't met.
type ListAiStudioTemplateIDDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAiStudioTemplateIDDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAiStudioTemplateIDDataMultiError) AllErrors() []error { return m }

// ListAiStudioTemplateIDDataValidationError is the validation error returned
// by ListAiStudioTemplateIDData.Validate if the designated constraints aren't met.
type ListAiStudioTemplateIDDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAiStudioTemplateIDDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAiStudioTemplateIDDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAiStudioTemplateIDDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAiStudioTemplateIDDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAiStudioTemplateIDDataValidationError) ErrorName() string {
	return "ListAiStudioTemplateIDDataValidationError"
}

// Error satisfies the builtin error interface
func (e ListAiStudioTemplateIDDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAiStudioTemplateIDData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAiStudioTemplateIDDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAiStudioTemplateIDDataValidationError{}

// Validate checks the field values on ListAiStudioTemplateIDResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAiStudioTemplateIDResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAiStudioTemplateIDResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListAiStudioTemplateIDResponseMultiError, or nil if none found.
func (m *ListAiStudioTemplateIDResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAiStudioTemplateIDResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAiStudioTemplateIDResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAiStudioTemplateIDResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAiStudioTemplateIDResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAiStudioTemplateIDResponseMultiError(errors)
	}

	return nil
}

// ListAiStudioTemplateIDResponseMultiError is an error wrapping multiple
// validation errors returned by ListAiStudioTemplateIDResponse.ValidateAll()
// if the designated constraints aren't met.
type ListAiStudioTemplateIDResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAiStudioTemplateIDResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAiStudioTemplateIDResponseMultiError) AllErrors() []error { return m }

// ListAiStudioTemplateIDResponseValidationError is the validation error
// returned by ListAiStudioTemplateIDResponse.Validate if the designated
// constraints aren't met.
type ListAiStudioTemplateIDResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAiStudioTemplateIDResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAiStudioTemplateIDResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAiStudioTemplateIDResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAiStudioTemplateIDResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAiStudioTemplateIDResponseValidationError) ErrorName() string {
	return "ListAiStudioTemplateIDResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAiStudioTemplateIDResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAiStudioTemplateIDResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAiStudioTemplateIDResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAiStudioTemplateIDResponseValidationError{}

// Validate checks the field values on CreateAiStudioTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAiStudioTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAiStudioTaskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAiStudioTaskRequestMultiError, or nil if none found.
func (m *CreateAiStudioTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAiStudioTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Task

	// no validation rules for TemplateId

	// no validation rules for Envs

	// no validation rules for AiKey

	// no validation rules for ImChannel

	// no validation rules for Spec

	if len(errors) > 0 {
		return CreateAiStudioTaskRequestMultiError(errors)
	}

	return nil
}

// CreateAiStudioTaskRequestMultiError is an error wrapping multiple validation
// errors returned by CreateAiStudioTaskRequest.ValidateAll() if the
// designated constraints aren't met.
type CreateAiStudioTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAiStudioTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAiStudioTaskRequestMultiError) AllErrors() []error { return m }

// CreateAiStudioTaskRequestValidationError is the validation error returned by
// CreateAiStudioTaskRequest.Validate if the designated constraints aren't met.
type CreateAiStudioTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAiStudioTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAiStudioTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAiStudioTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAiStudioTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAiStudioTaskRequestValidationError) ErrorName() string {
	return "CreateAiStudioTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAiStudioTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAiStudioTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAiStudioTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAiStudioTaskRequestValidationError{}

// Validate checks the field values on AiStudioTask with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AiStudioTask) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AiStudioTask with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AiStudioTaskMultiError, or
// nil if none found.
func (m *AiStudioTask) ValidateAll() error {
	return m.validate(true)
}

func (m *AiStudioTask) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AiStudioTaskValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AiStudioTaskValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AiStudioTaskValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AiStudioTaskMultiError(errors)
	}

	return nil
}

// AiStudioTaskMultiError is an error wrapping multiple validation errors
// returned by AiStudioTask.ValidateAll() if the designated constraints aren't met.
type AiStudioTaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AiStudioTaskMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AiStudioTaskMultiError) AllErrors() []error { return m }

// AiStudioTaskValidationError is the validation error returned by
// AiStudioTask.Validate if the designated constraints aren't met.
type AiStudioTaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AiStudioTaskValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AiStudioTaskValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AiStudioTaskValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AiStudioTaskValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AiStudioTaskValidationError) ErrorName() string { return "AiStudioTaskValidationError" }

// Error satisfies the builtin error interface
func (e AiStudioTaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAiStudioTask.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AiStudioTaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AiStudioTaskValidationError{}

// Validate checks the field values on AiStudioTaskData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AiStudioTaskData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AiStudioTaskData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AiStudioTaskDataMultiError, or nil if none found.
func (m *AiStudioTaskData) ValidateAll() error {
	return m.validate(true)
}

func (m *AiStudioTaskData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Task

	// no validation rules for TemplateId

	// no validation rules for Envs

	// no validation rules for AiKey

	// no validation rules for ImChannel

	// no validation rules for Spec

	if len(errors) > 0 {
		return AiStudioTaskDataMultiError(errors)
	}

	return nil
}

// AiStudioTaskDataMultiError is an error wrapping multiple validation errors
// returned by AiStudioTaskData.ValidateAll() if the designated constraints
// aren't met.
type AiStudioTaskDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AiStudioTaskDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AiStudioTaskDataMultiError) AllErrors() []error { return m }

// AiStudioTaskDataValidationError is the validation error returned by
// AiStudioTaskData.Validate if the designated constraints aren't met.
type AiStudioTaskDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AiStudioTaskDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AiStudioTaskDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AiStudioTaskDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AiStudioTaskDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AiStudioTaskDataValidationError) ErrorName() string { return "AiStudioTaskDataValidationError" }

// Error satisfies the builtin error interface
func (e AiStudioTaskDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAiStudioTaskData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AiStudioTaskDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AiStudioTaskDataValidationError{}

// Validate checks the field values on GetAiStudioTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAiStudioTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAiStudioTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAiStudioTaskRequestMultiError, or nil if none found.
func (m *GetAiStudioTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAiStudioTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetAiStudioTaskRequestMultiError(errors)
	}

	return nil
}

// GetAiStudioTaskRequestMultiError is an error wrapping multiple validation
// errors returned by GetAiStudioTaskRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAiStudioTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAiStudioTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAiStudioTaskRequestMultiError) AllErrors() []error { return m }

// GetAiStudioTaskRequestValidationError is the validation error returned by
// GetAiStudioTaskRequest.Validate if the designated constraints aren't met.
type GetAiStudioTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAiStudioTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAiStudioTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAiStudioTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAiStudioTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAiStudioTaskRequestValidationError) ErrorName() string {
	return "GetAiStudioTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAiStudioTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAiStudioTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAiStudioTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAiStudioTaskRequestValidationError{}

// Validate checks the field values on RunAiStudioTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RunAiStudioTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RunAiStudioTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RunAiStudioTaskRequestMultiError, or nil if none found.
func (m *RunAiStudioTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RunAiStudioTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Envs

	if len(errors) > 0 {
		return RunAiStudioTaskRequestMultiError(errors)
	}

	return nil
}

// RunAiStudioTaskRequestMultiError is an error wrapping multiple validation
// errors returned by RunAiStudioTaskRequest.ValidateAll() if the designated
// constraints aren't met.
type RunAiStudioTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RunAiStudioTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RunAiStudioTaskRequestMultiError) AllErrors() []error { return m }

// RunAiStudioTaskRequestValidationError is the validation error returned by
// RunAiStudioTaskRequest.Validate if the designated constraints aren't met.
type RunAiStudioTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RunAiStudioTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RunAiStudioTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RunAiStudioTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RunAiStudioTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RunAiStudioTaskRequestValidationError) ErrorName() string {
	return "RunAiStudioTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RunAiStudioTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRunAiStudioTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RunAiStudioTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RunAiStudioTaskRequestValidationError{}

// Validate checks the field values on RunAiStudioTaskResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RunAiStudioTaskResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RunAiStudioTaskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RunAiStudioTaskResponseMultiError, or nil if none found.
func (m *RunAiStudioTaskResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RunAiStudioTaskResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Msg

	if len(errors) > 0 {
		return RunAiStudioTaskResponseMultiError(errors)
	}

	return nil
}

// RunAiStudioTaskResponseMultiError is an error wrapping multiple validation
// errors returned by RunAiStudioTaskResponse.ValidateAll() if the designated
// constraints aren't met.
type RunAiStudioTaskResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RunAiStudioTaskResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RunAiStudioTaskResponseMultiError) AllErrors() []error { return m }

// RunAiStudioTaskResponseValidationError is the validation error returned by
// RunAiStudioTaskResponse.Validate if the designated constraints aren't met.
type RunAiStudioTaskResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RunAiStudioTaskResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RunAiStudioTaskResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RunAiStudioTaskResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RunAiStudioTaskResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RunAiStudioTaskResponseValidationError) ErrorName() string {
	return "RunAiStudioTaskResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RunAiStudioTaskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRunAiStudioTaskResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RunAiStudioTaskResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RunAiStudioTaskResponseValidationError{}

// Validate checks the field values on UpdateAiStudioTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAiStudioTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAiStudioTaskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAiStudioTaskRequestMultiError, or nil if none found.
func (m *UpdateAiStudioTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAiStudioTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Task

	// no validation rules for TemplateId

	// no validation rules for Envs

	// no validation rules for AiKey

	// no validation rules for ImChannel

	// no validation rules for Spec

	if len(errors) > 0 {
		return UpdateAiStudioTaskRequestMultiError(errors)
	}

	return nil
}

// UpdateAiStudioTaskRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateAiStudioTaskRequest.ValidateAll() if the
// designated constraints aren't met.
type UpdateAiStudioTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAiStudioTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAiStudioTaskRequestMultiError) AllErrors() []error { return m }

// UpdateAiStudioTaskRequestValidationError is the validation error returned by
// UpdateAiStudioTaskRequest.Validate if the designated constraints aren't met.
type UpdateAiStudioTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAiStudioTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAiStudioTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAiStudioTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAiStudioTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAiStudioTaskRequestValidationError) ErrorName() string {
	return "UpdateAiStudioTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAiStudioTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAiStudioTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAiStudioTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAiStudioTaskRequestValidationError{}

// Validate checks the field values on DeleteAiStudioTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteAiStudioTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAiStudioTaskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteAiStudioTaskRequestMultiError, or nil if none found.
func (m *DeleteAiStudioTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAiStudioTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteAiStudioTaskRequestMultiError(errors)
	}

	return nil
}

// DeleteAiStudioTaskRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteAiStudioTaskRequest.ValidateAll() if the
// designated constraints aren't met.
type DeleteAiStudioTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAiStudioTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAiStudioTaskRequestMultiError) AllErrors() []error { return m }

// DeleteAiStudioTaskRequestValidationError is the validation error returned by
// DeleteAiStudioTaskRequest.Validate if the designated constraints aren't met.
type DeleteAiStudioTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAiStudioTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAiStudioTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAiStudioTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAiStudioTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAiStudioTaskRequestValidationError) ErrorName() string {
	return "DeleteAiStudioTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAiStudioTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAiStudioTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAiStudioTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAiStudioTaskRequestValidationError{}

// Validate checks the field values on ListAiStudioTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAiStudioTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAiStudioTaskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAiStudioTaskRequestMultiError, or nil if none found.
func (m *ListAiStudioTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAiStudioTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PerPage

	// no validation rules for Page

	if len(errors) > 0 {
		return ListAiStudioTaskRequestMultiError(errors)
	}

	return nil
}

// ListAiStudioTaskRequestMultiError is an error wrapping multiple validation
// errors returned by ListAiStudioTaskRequest.ValidateAll() if the designated
// constraints aren't met.
type ListAiStudioTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAiStudioTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAiStudioTaskRequestMultiError) AllErrors() []error { return m }

// ListAiStudioTaskRequestValidationError is the validation error returned by
// ListAiStudioTaskRequest.Validate if the designated constraints aren't met.
type ListAiStudioTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAiStudioTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAiStudioTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAiStudioTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAiStudioTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAiStudioTaskRequestValidationError) ErrorName() string {
	return "ListAiStudioTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAiStudioTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAiStudioTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAiStudioTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAiStudioTaskRequestValidationError{}

// Validate checks the field values on ListAiStudioTaskData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAiStudioTaskData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAiStudioTaskData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAiStudioTaskDataMultiError, or nil if none found.
func (m *ListAiStudioTaskData) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAiStudioTaskData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAiStudioTaskDataValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAiStudioTaskDataValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAiStudioTaskDataValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListAiStudioTaskDataMultiError(errors)
	}

	return nil
}

// ListAiStudioTaskDataMultiError is an error wrapping multiple validation
// errors returned by ListAiStudioTaskData.ValidateAll() if the designated
// constraints aren't met.
type ListAiStudioTaskDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAiStudioTaskDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAiStudioTaskDataMultiError) AllErrors() []error { return m }

// ListAiStudioTaskDataValidationError is the validation error returned by
// ListAiStudioTaskData.Validate if the designated constraints aren't met.
type ListAiStudioTaskDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAiStudioTaskDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAiStudioTaskDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAiStudioTaskDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAiStudioTaskDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAiStudioTaskDataValidationError) ErrorName() string {
	return "ListAiStudioTaskDataValidationError"
}

// Error satisfies the builtin error interface
func (e ListAiStudioTaskDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAiStudioTaskData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAiStudioTaskDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAiStudioTaskDataValidationError{}

// Validate checks the field values on ListAiStudioTaskResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAiStudioTaskResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAiStudioTaskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAiStudioTaskResponseMultiError, or nil if none found.
func (m *ListAiStudioTaskResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAiStudioTaskResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAiStudioTaskResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAiStudioTaskResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAiStudioTaskResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAiStudioTaskResponseMultiError(errors)
	}

	return nil
}

// ListAiStudioTaskResponseMultiError is an error wrapping multiple validation
// errors returned by ListAiStudioTaskResponse.ValidateAll() if the designated
// constraints aren't met.
type ListAiStudioTaskResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAiStudioTaskResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAiStudioTaskResponseMultiError) AllErrors() []error { return m }

// ListAiStudioTaskResponseValidationError is the validation error returned by
// ListAiStudioTaskResponse.Validate if the designated constraints aren't met.
type ListAiStudioTaskResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAiStudioTaskResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAiStudioTaskResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAiStudioTaskResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAiStudioTaskResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAiStudioTaskResponseValidationError) ErrorName() string {
	return "ListAiStudioTaskResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAiStudioTaskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAiStudioTaskResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAiStudioTaskResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAiStudioTaskResponseValidationError{}

// Validate checks the field values on AiStudioTaskLog with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AiStudioTaskLog) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AiStudioTaskLog with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AiStudioTaskLogMultiError, or nil if none found.
func (m *AiStudioTaskLog) ValidateAll() error {
	return m.validate(true)
}

func (m *AiStudioTaskLog) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for TaskId

	// no validation rules for Prompt

	// no validation rules for Envs

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AiStudioTaskLogValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AiStudioTaskLogValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AiStudioTaskLogValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AiStudioTaskLogMultiError(errors)
	}

	return nil
}

// AiStudioTaskLogMultiError is an error wrapping multiple validation errors
// returned by AiStudioTaskLog.ValidateAll() if the designated constraints
// aren't met.
type AiStudioTaskLogMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AiStudioTaskLogMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AiStudioTaskLogMultiError) AllErrors() []error { return m }

// AiStudioTaskLogValidationError is the validation error returned by
// AiStudioTaskLog.Validate if the designated constraints aren't met.
type AiStudioTaskLogValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AiStudioTaskLogValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AiStudioTaskLogValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AiStudioTaskLogValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AiStudioTaskLogValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AiStudioTaskLogValidationError) ErrorName() string { return "AiStudioTaskLogValidationError" }

// Error satisfies the builtin error interface
func (e AiStudioTaskLogValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAiStudioTaskLog.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AiStudioTaskLogValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AiStudioTaskLogValidationError{}

// Validate checks the field values on CreateAiStudioTaskLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAiStudioTaskLogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAiStudioTaskLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAiStudioTaskLogRequestMultiError, or nil if none found.
func (m *CreateAiStudioTaskLogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAiStudioTaskLogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for Prompt

	// no validation rules for Envs

	if len(errors) > 0 {
		return CreateAiStudioTaskLogRequestMultiError(errors)
	}

	return nil
}

// CreateAiStudioTaskLogRequestMultiError is an error wrapping multiple
// validation errors returned by CreateAiStudioTaskLogRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateAiStudioTaskLogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAiStudioTaskLogRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAiStudioTaskLogRequestMultiError) AllErrors() []error { return m }

// CreateAiStudioTaskLogRequestValidationError is the validation error returned
// by CreateAiStudioTaskLogRequest.Validate if the designated constraints
// aren't met.
type CreateAiStudioTaskLogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAiStudioTaskLogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAiStudioTaskLogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAiStudioTaskLogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAiStudioTaskLogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAiStudioTaskLogRequestValidationError) ErrorName() string {
	return "CreateAiStudioTaskLogRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAiStudioTaskLogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAiStudioTaskLogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAiStudioTaskLogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAiStudioTaskLogRequestValidationError{}

// Validate checks the field values on GetAiStudioTaskLogRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAiStudioTaskLogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAiStudioTaskLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAiStudioTaskLogRequestMultiError, or nil if none found.
func (m *GetAiStudioTaskLogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAiStudioTaskLogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetAiStudioTaskLogRequestMultiError(errors)
	}

	return nil
}

// GetAiStudioTaskLogRequestMultiError is an error wrapping multiple validation
// errors returned by GetAiStudioTaskLogRequest.ValidateAll() if the
// designated constraints aren't met.
type GetAiStudioTaskLogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAiStudioTaskLogRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAiStudioTaskLogRequestMultiError) AllErrors() []error { return m }

// GetAiStudioTaskLogRequestValidationError is the validation error returned by
// GetAiStudioTaskLogRequest.Validate if the designated constraints aren't met.
type GetAiStudioTaskLogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAiStudioTaskLogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAiStudioTaskLogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAiStudioTaskLogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAiStudioTaskLogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAiStudioTaskLogRequestValidationError) ErrorName() string {
	return "GetAiStudioTaskLogRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAiStudioTaskLogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAiStudioTaskLogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAiStudioTaskLogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAiStudioTaskLogRequestValidationError{}

// Validate checks the field values on UpdateAiStudioTaskLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAiStudioTaskLogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAiStudioTaskLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAiStudioTaskLogRequestMultiError, or nil if none found.
func (m *UpdateAiStudioTaskLogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAiStudioTaskLogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for TaskId

	// no validation rules for Prompt

	// no validation rules for Envs

	if len(errors) > 0 {
		return UpdateAiStudioTaskLogRequestMultiError(errors)
	}

	return nil
}

// UpdateAiStudioTaskLogRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateAiStudioTaskLogRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateAiStudioTaskLogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAiStudioTaskLogRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAiStudioTaskLogRequestMultiError) AllErrors() []error { return m }

// UpdateAiStudioTaskLogRequestValidationError is the validation error returned
// by UpdateAiStudioTaskLogRequest.Validate if the designated constraints
// aren't met.
type UpdateAiStudioTaskLogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAiStudioTaskLogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAiStudioTaskLogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAiStudioTaskLogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAiStudioTaskLogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAiStudioTaskLogRequestValidationError) ErrorName() string {
	return "UpdateAiStudioTaskLogRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAiStudioTaskLogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAiStudioTaskLogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAiStudioTaskLogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAiStudioTaskLogRequestValidationError{}

// Validate checks the field values on ListAiStudioTaskLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAiStudioTaskLogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAiStudioTaskLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAiStudioTaskLogRequestMultiError, or nil if none found.
func (m *ListAiStudioTaskLogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAiStudioTaskLogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PerPage

	// no validation rules for Page

	if len(errors) > 0 {
		return ListAiStudioTaskLogRequestMultiError(errors)
	}

	return nil
}

// ListAiStudioTaskLogRequestMultiError is an error wrapping multiple
// validation errors returned by ListAiStudioTaskLogRequest.ValidateAll() if
// the designated constraints aren't met.
type ListAiStudioTaskLogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAiStudioTaskLogRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAiStudioTaskLogRequestMultiError) AllErrors() []error { return m }

// ListAiStudioTaskLogRequestValidationError is the validation error returned
// by ListAiStudioTaskLogRequest.Validate if the designated constraints aren't met.
type ListAiStudioTaskLogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAiStudioTaskLogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAiStudioTaskLogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAiStudioTaskLogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAiStudioTaskLogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAiStudioTaskLogRequestValidationError) ErrorName() string {
	return "ListAiStudioTaskLogRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAiStudioTaskLogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAiStudioTaskLogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAiStudioTaskLogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAiStudioTaskLogRequestValidationError{}

// Validate checks the field values on ListAiStudioTaskLogData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAiStudioTaskLogData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAiStudioTaskLogData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAiStudioTaskLogDataMultiError, or nil if none found.
func (m *ListAiStudioTaskLogData) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAiStudioTaskLogData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAiStudioTaskLogDataValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAiStudioTaskLogDataValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAiStudioTaskLogDataValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListAiStudioTaskLogDataMultiError(errors)
	}

	return nil
}

// ListAiStudioTaskLogDataMultiError is an error wrapping multiple validation
// errors returned by ListAiStudioTaskLogData.ValidateAll() if the designated
// constraints aren't met.
type ListAiStudioTaskLogDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAiStudioTaskLogDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAiStudioTaskLogDataMultiError) AllErrors() []error { return m }

// ListAiStudioTaskLogDataValidationError is the validation error returned by
// ListAiStudioTaskLogData.Validate if the designated constraints aren't met.
type ListAiStudioTaskLogDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAiStudioTaskLogDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAiStudioTaskLogDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAiStudioTaskLogDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAiStudioTaskLogDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAiStudioTaskLogDataValidationError) ErrorName() string {
	return "ListAiStudioTaskLogDataValidationError"
}

// Error satisfies the builtin error interface
func (e ListAiStudioTaskLogDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAiStudioTaskLogData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAiStudioTaskLogDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAiStudioTaskLogDataValidationError{}

// Validate checks the field values on ListAiStudioTaskLogResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAiStudioTaskLogResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAiStudioTaskLogResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAiStudioTaskLogResponseMultiError, or nil if none found.
func (m *ListAiStudioTaskLogResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAiStudioTaskLogResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAiStudioTaskLogResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAiStudioTaskLogResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAiStudioTaskLogResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAiStudioTaskLogResponseMultiError(errors)
	}

	return nil
}

// ListAiStudioTaskLogResponseMultiError is an error wrapping multiple
// validation errors returned by ListAiStudioTaskLogResponse.ValidateAll() if
// the designated constraints aren't met.
type ListAiStudioTaskLogResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAiStudioTaskLogResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAiStudioTaskLogResponseMultiError) AllErrors() []error { return m }

// ListAiStudioTaskLogResponseValidationError is the validation error returned
// by ListAiStudioTaskLogResponse.Validate if the designated constraints
// aren't met.
type ListAiStudioTaskLogResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAiStudioTaskLogResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAiStudioTaskLogResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAiStudioTaskLogResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAiStudioTaskLogResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAiStudioTaskLogResponseValidationError) ErrorName() string {
	return "ListAiStudioTaskLogResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAiStudioTaskLogResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAiStudioTaskLogResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAiStudioTaskLogResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAiStudioTaskLogResponseValidationError{}

// Validate checks the field values on HandleURLVerificationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleURLVerificationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleURLVerificationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HandleURLVerificationRequestMultiError, or nil if none found.
func (m *HandleURLVerificationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleURLVerificationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Token

	// no validation rules for Challenge

	if len(errors) > 0 {
		return HandleURLVerificationRequestMultiError(errors)
	}

	return nil
}

// HandleURLVerificationRequestMultiError is an error wrapping multiple
// validation errors returned by HandleURLVerificationRequest.ValidateAll() if
// the designated constraints aren't met.
type HandleURLVerificationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleURLVerificationRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleURLVerificationRequestMultiError) AllErrors() []error { return m }

// HandleURLVerificationRequestValidationError is the validation error returned
// by HandleURLVerificationRequest.Validate if the designated constraints
// aren't met.
type HandleURLVerificationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleURLVerificationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleURLVerificationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleURLVerificationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleURLVerificationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleURLVerificationRequestValidationError) ErrorName() string {
	return "HandleURLVerificationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e HandleURLVerificationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleURLVerificationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleURLVerificationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleURLVerificationRequestValidationError{}

// Validate checks the field values on HandleURLVerificationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleURLVerificationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleURLVerificationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// HandleURLVerificationResponseMultiError, or nil if none found.
func (m *HandleURLVerificationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleURLVerificationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Challenge

	if len(errors) > 0 {
		return HandleURLVerificationResponseMultiError(errors)
	}

	return nil
}

// HandleURLVerificationResponseMultiError is an error wrapping multiple
// validation errors returned by HandleURLVerificationResponse.ValidateAll()
// if the designated constraints aren't met.
type HandleURLVerificationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleURLVerificationResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleURLVerificationResponseMultiError) AllErrors() []error { return m }

// HandleURLVerificationResponseValidationError is the validation error
// returned by HandleURLVerificationResponse.Validate if the designated
// constraints aren't met.
type HandleURLVerificationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleURLVerificationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleURLVerificationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleURLVerificationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleURLVerificationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleURLVerificationResponseValidationError) ErrorName() string {
	return "HandleURLVerificationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e HandleURLVerificationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleURLVerificationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleURLVerificationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleURLVerificationResponseValidationError{}

// Validate checks the field values on HandleEventCallbackRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleEventCallbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleEventCallbackRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HandleEventCallbackRequestMultiError, or nil if none found.
func (m *HandleEventCallbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleEventCallbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	// no validation rules for TeamId

	// no validation rules for ApiAppId

	if all {
		switch v := interface{}(m.GetEvent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HandleEventCallbackRequestValidationError{
					field:  "Event",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HandleEventCallbackRequestValidationError{
					field:  "Event",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEvent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HandleEventCallbackRequestValidationError{
				field:  "Event",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	// no validation rules for EventId

	// no validation rules for EventTime

	for idx, item := range m.GetAuthorizations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HandleEventCallbackRequestValidationError{
						field:  fmt.Sprintf("Authorizations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HandleEventCallbackRequestValidationError{
						field:  fmt.Sprintf("Authorizations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HandleEventCallbackRequestValidationError{
					field:  fmt.Sprintf("Authorizations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsExtSharedChannel

	if m.ContextTeamId != nil {
		// no validation rules for ContextTeamId
	}

	if m.ContextEnterpriseId != nil {
		// no validation rules for ContextEnterpriseId
	}

	if m.Challenge != nil {
		// no validation rules for Challenge
	}

	if len(errors) > 0 {
		return HandleEventCallbackRequestMultiError(errors)
	}

	return nil
}

// HandleEventCallbackRequestMultiError is an error wrapping multiple
// validation errors returned by HandleEventCallbackRequest.ValidateAll() if
// the designated constraints aren't met.
type HandleEventCallbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleEventCallbackRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleEventCallbackRequestMultiError) AllErrors() []error { return m }

// HandleEventCallbackRequestValidationError is the validation error returned
// by HandleEventCallbackRequest.Validate if the designated constraints aren't met.
type HandleEventCallbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleEventCallbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleEventCallbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleEventCallbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleEventCallbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleEventCallbackRequestValidationError) ErrorName() string {
	return "HandleEventCallbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e HandleEventCallbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleEventCallbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleEventCallbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleEventCallbackRequestValidationError{}

// Validate checks the field values on Authorization with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Authorization) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Authorization with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AuthorizationMultiError, or
// nil if none found.
func (m *Authorization) ValidateAll() error {
	return m.validate(true)
}

func (m *Authorization) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TeamId

	// no validation rules for UserId

	// no validation rules for IsBot

	// no validation rules for IsEnterpriseInstall

	if m.EnterpriseId != nil {
		// no validation rules for EnterpriseId
	}

	if len(errors) > 0 {
		return AuthorizationMultiError(errors)
	}

	return nil
}

// AuthorizationMultiError is an error wrapping multiple validation errors
// returned by Authorization.ValidateAll() if the designated constraints
// aren't met.
type AuthorizationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthorizationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthorizationMultiError) AllErrors() []error { return m }

// AuthorizationValidationError is the validation error returned by
// Authorization.Validate if the designated constraints aren't met.
type AuthorizationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthorizationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthorizationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthorizationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthorizationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthorizationValidationError) ErrorName() string { return "AuthorizationValidationError" }

// Error satisfies the builtin error interface
func (e AuthorizationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthorization.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthorizationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthorizationValidationError{}

// Validate checks the field values on AppMentionEvent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AppMentionEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppMentionEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AppMentionEventMultiError, or nil if none found.
func (m *AppMentionEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *AppMentionEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Text

	// no validation rules for User

	// no validation rules for Ts

	// no validation rules for Channel

	// no validation rules for EventTs

	if m.ClientMsgId != nil {
		// no validation rules for ClientMsgId
	}

	if m.Team != nil {
		// no validation rules for Team
	}

	if m.ThreadTs != nil {
		// no validation rules for ThreadTs
	}

	if len(errors) > 0 {
		return AppMentionEventMultiError(errors)
	}

	return nil
}

// AppMentionEventMultiError is an error wrapping multiple validation errors
// returned by AppMentionEvent.ValidateAll() if the designated constraints
// aren't met.
type AppMentionEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppMentionEventMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppMentionEventMultiError) AllErrors() []error { return m }

// AppMentionEventValidationError is the validation error returned by
// AppMentionEvent.Validate if the designated constraints aren't met.
type AppMentionEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppMentionEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppMentionEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppMentionEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppMentionEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppMentionEventValidationError) ErrorName() string { return "AppMentionEventValidationError" }

// Error satisfies the builtin error interface
func (e AppMentionEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppMentionEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppMentionEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppMentionEventValidationError{}

// Validate checks the field values on HandleEventCallbackResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleEventCallbackResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleEventCallbackResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HandleEventCallbackResponseMultiError, or nil if none found.
func (m *HandleEventCallbackResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleEventCallbackResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Challenge

	if len(errors) > 0 {
		return HandleEventCallbackResponseMultiError(errors)
	}

	return nil
}

// HandleEventCallbackResponseMultiError is an error wrapping multiple
// validation errors returned by HandleEventCallbackResponse.ValidateAll() if
// the designated constraints aren't met.
type HandleEventCallbackResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleEventCallbackResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleEventCallbackResponseMultiError) AllErrors() []error { return m }

// HandleEventCallbackResponseValidationError is the validation error returned
// by HandleEventCallbackResponse.Validate if the designated constraints
// aren't met.
type HandleEventCallbackResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleEventCallbackResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleEventCallbackResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleEventCallbackResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleEventCallbackResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleEventCallbackResponseValidationError) ErrorName() string {
	return "HandleEventCallbackResponseValidationError"
}

// Error satisfies the builtin error interface
func (e HandleEventCallbackResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleEventCallbackResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleEventCallbackResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleEventCallbackResponseValidationError{}
