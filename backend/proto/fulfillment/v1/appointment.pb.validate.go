// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/fulfillment/v1/appointment.proto

package fulfillmentpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AppointmentFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AppointmentFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppointmentFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AppointmentFilterMultiError, or nil if none found.
func (m *AppointmentFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *AppointmentFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AppointmentFilterMultiError(errors)
	}

	return nil
}

// AppointmentFilterMultiError is an error wrapping multiple validation errors
// returned by AppointmentFilter.ValidateAll() if the designated constraints
// aren't met.
type AppointmentFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppointmentFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppointmentFilterMultiError) AllErrors() []error { return m }

// AppointmentFilterValidationError is the validation error returned by
// AppointmentFilter.Validate if the designated constraints aren't met.
type AppointmentFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppointmentFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppointmentFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppointmentFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppointmentFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppointmentFilterValidationError) ErrorName() string {
	return "AppointmentFilterValidationError"
}

// Error satisfies the builtin error interface
func (e AppointmentFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppointmentFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppointmentFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppointmentFilterValidationError{}

// Validate checks the field values on Appointment with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Appointment) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Appointment with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AppointmentMultiError, or
// nil if none found.
func (m *Appointment) ValidateAll() error {
	return m.validate(true)
}

func (m *Appointment) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if m.GetBusinessId() <= 0 {
		err := AppointmentValidationError{
			field:  "BusinessId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetCompanyId() <= 0 {
		err := AppointmentValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CustomerId

	// no validation rules for Status

	// no validation rules for ServiceItemType

	// no validation rules for ColorCode

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AppointmentValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AppointmentValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AppointmentValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AppointmentValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AppointmentValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AppointmentValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AppointmentValidationError{
						field:  fmt.Sprintf("Pets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AppointmentValidationError{
						field:  fmt.Sprintf("Pets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AppointmentValidationError{
					field:  fmt.Sprintf("Pets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AppointmentMultiError(errors)
	}

	return nil
}

// AppointmentMultiError is an error wrapping multiple validation errors
// returned by Appointment.ValidateAll() if the designated constraints aren't met.
type AppointmentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppointmentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppointmentMultiError) AllErrors() []error { return m }

// AppointmentValidationError is the validation error returned by
// Appointment.Validate if the designated constraints aren't met.
type AppointmentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppointmentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppointmentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppointmentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppointmentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppointmentValidationError) ErrorName() string { return "AppointmentValidationError" }

// Error satisfies the builtin error interface
func (e AppointmentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppointment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppointmentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppointmentValidationError{}

// Validate checks the field values on PetDetail with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PetDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PetDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PetDetailMultiError, or nil
// if none found.
func (m *PetDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *PetDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PetId

	for idx, item := range m.GetServices() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PetDetailValidationError{
						field:  fmt.Sprintf("Services[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PetDetailValidationError{
						field:  fmt.Sprintf("Services[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PetDetailValidationError{
					field:  fmt.Sprintf("Services[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PetDetailMultiError(errors)
	}

	return nil
}

// PetDetailMultiError is an error wrapping multiple validation errors returned
// by PetDetail.ValidateAll() if the designated constraints aren't met.
type PetDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PetDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PetDetailMultiError) AllErrors() []error { return m }

// PetDetailValidationError is the validation error returned by
// PetDetail.Validate if the designated constraints aren't met.
type PetDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PetDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PetDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PetDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PetDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PetDetailValidationError) ErrorName() string { return "PetDetailValidationError" }

// Error satisfies the builtin error interface
func (e PetDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPetDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PetDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PetDetailValidationError{}

// Validate checks the field values on ServiceInstanceImpl with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceInstanceImpl) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceInstanceImpl with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceInstanceImplMultiError, or nil if none found.
func (m *ServiceInstanceImpl) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceInstanceImpl) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceTemplateId

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServiceInstanceImplValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServiceInstanceImplValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServiceInstanceImplValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServiceInstanceImplValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServiceInstanceImplValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServiceInstanceImplValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DateType

	for idx, item := range m.GetSubServiceInstance() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServiceInstanceImplValidationError{
						field:  fmt.Sprintf("SubServiceInstance[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServiceInstanceImplValidationError{
						field:  fmt.Sprintf("SubServiceInstance[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServiceInstanceImplValidationError{
					field:  fmt.Sprintf("SubServiceInstance[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServiceInstanceImplValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServiceInstanceImplValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServiceInstanceImplValidationError{
					field:  fmt.Sprintf("Options[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCharge() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServiceInstanceImplValidationError{
						field:  fmt.Sprintf("Charge[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServiceInstanceImplValidationError{
						field:  fmt.Sprintf("Charge[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServiceInstanceImplValidationError{
					field:  fmt.Sprintf("Charge[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetNote() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServiceInstanceImplValidationError{
						field:  fmt.Sprintf("Note[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServiceInstanceImplValidationError{
						field:  fmt.Sprintf("Note[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServiceInstanceImplValidationError{
					field:  fmt.Sprintf("Note[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFeedingMedication() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServiceInstanceImplValidationError{
						field:  fmt.Sprintf("FeedingMedication[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServiceInstanceImplValidationError{
						field:  fmt.Sprintf("FeedingMedication[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServiceInstanceImplValidationError{
					field:  fmt.Sprintf("FeedingMedication[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetServiceDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServiceInstanceImplValidationError{
						field:  fmt.Sprintf("ServiceDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServiceInstanceImplValidationError{
						field:  fmt.Sprintf("ServiceDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServiceInstanceImplValidationError{
					field:  fmt.Sprintf("ServiceDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ServiceInstanceImplMultiError(errors)
	}

	return nil
}

// ServiceInstanceImplMultiError is an error wrapping multiple validation
// errors returned by ServiceInstanceImpl.ValidateAll() if the designated
// constraints aren't met.
type ServiceInstanceImplMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceInstanceImplMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceInstanceImplMultiError) AllErrors() []error { return m }

// ServiceInstanceImplValidationError is the validation error returned by
// ServiceInstanceImpl.Validate if the designated constraints aren't met.
type ServiceInstanceImplValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceInstanceImplValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceInstanceImplValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceInstanceImplValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceInstanceImplValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceInstanceImplValidationError) ErrorName() string {
	return "ServiceInstanceImplValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceInstanceImplValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceInstanceImpl.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceInstanceImplValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceInstanceImplValidationError{}

// Validate checks the field values on ServiceDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ServiceDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ServiceDetailMultiError, or
// nil if none found.
func (m *ServiceDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetStaffDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServiceDetailValidationError{
						field:  fmt.Sprintf("StaffDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServiceDetailValidationError{
						field:  fmt.Sprintf("StaffDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServiceDetailValidationError{
					field:  fmt.Sprintf("StaffDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ServiceDetailMultiError(errors)
	}

	return nil
}

// ServiceDetailMultiError is an error wrapping multiple validation errors
// returned by ServiceDetail.ValidateAll() if the designated constraints
// aren't met.
type ServiceDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceDetailMultiError) AllErrors() []error { return m }

// ServiceDetailValidationError is the validation error returned by
// ServiceDetail.Validate if the designated constraints aren't met.
type ServiceDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceDetailValidationError) ErrorName() string { return "ServiceDetailValidationError" }

// Error satisfies the builtin error interface
func (e ServiceDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceDetailValidationError{}

// Validate checks the field values on StaffDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StaffDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StaffDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StaffDetailMultiError, or
// nil if none found.
func (m *StaffDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *StaffDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StaffId

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StaffDetailValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StaffDetailValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StaffDetailValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StaffDetailValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StaffDetailValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StaffDetailValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.AssignmentReason != nil {
		// no validation rules for AssignmentReason
	}

	if len(errors) > 0 {
		return StaffDetailMultiError(errors)
	}

	return nil
}

// StaffDetailMultiError is an error wrapping multiple validation errors
// returned by StaffDetail.ValidateAll() if the designated constraints aren't met.
type StaffDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StaffDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StaffDetailMultiError) AllErrors() []error { return m }

// StaffDetailValidationError is the validation error returned by
// StaffDetail.Validate if the designated constraints aren't met.
type StaffDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StaffDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StaffDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StaffDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StaffDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StaffDetailValidationError) ErrorName() string { return "StaffDetailValidationError" }

// Error satisfies the builtin error interface
func (e StaffDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStaffDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StaffDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StaffDetailValidationError{}

// Validate checks the field values on ServiceCharge with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ServiceCharge) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceCharge with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ServiceChargeMultiError, or
// nil if none found.
func (m *ServiceCharge) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceCharge) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Amount

	// no validation rules for Description

	// no validation rules for ChargeType

	if len(errors) > 0 {
		return ServiceChargeMultiError(errors)
	}

	return nil
}

// ServiceChargeMultiError is an error wrapping multiple validation errors
// returned by ServiceCharge.ValidateAll() if the designated constraints
// aren't met.
type ServiceChargeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceChargeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceChargeMultiError) AllErrors() []error { return m }

// ServiceChargeValidationError is the validation error returned by
// ServiceCharge.Validate if the designated constraints aren't met.
type ServiceChargeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceChargeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceChargeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceChargeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceChargeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceChargeValidationError) ErrorName() string { return "ServiceChargeValidationError" }

// Error satisfies the builtin error interface
func (e ServiceChargeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceCharge.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceChargeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceChargeValidationError{}

// Validate checks the field values on Note with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Note) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Note with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in NoteMultiError, or nil if none found.
func (m *Note) ValidateAll() error {
	return m.validate(true)
}

func (m *Note) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Content

	// no validation rules for StaffId

	if len(errors) > 0 {
		return NoteMultiError(errors)
	}

	return nil
}

// NoteMultiError is an error wrapping multiple validation errors returned by
// Note.ValidateAll() if the designated constraints aren't met.
type NoteMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoteMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoteMultiError) AllErrors() []error { return m }

// NoteValidationError is the validation error returned by Note.Validate if the
// designated constraints aren't met.
type NoteValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoteValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoteValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoteValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoteValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoteValidationError) ErrorName() string { return "NoteValidationError" }

// Error satisfies the builtin error interface
func (e NoteValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNote.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoteValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoteValidationError{}

// Validate checks the field values on FeedingMedication with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FeedingMedication) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeedingMedication with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FeedingMedicationMultiError, or nil if none found.
func (m *FeedingMedication) ValidateAll() error {
	return m.validate(true)
}

func (m *FeedingMedication) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FeedingRules

	// no validation rules for MedicationRules

	for idx, item := range m.GetMedications() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FeedingMedicationValidationError{
						field:  fmt.Sprintf("Medications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FeedingMedicationValidationError{
						field:  fmt.Sprintf("Medications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FeedingMedicationValidationError{
					field:  fmt.Sprintf("Medications[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FeedingMedicationMultiError(errors)
	}

	return nil
}

// FeedingMedicationMultiError is an error wrapping multiple validation errors
// returned by FeedingMedication.ValidateAll() if the designated constraints
// aren't met.
type FeedingMedicationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeedingMedicationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeedingMedicationMultiError) AllErrors() []error { return m }

// FeedingMedicationValidationError is the validation error returned by
// FeedingMedication.Validate if the designated constraints aren't met.
type FeedingMedicationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeedingMedicationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeedingMedicationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeedingMedicationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeedingMedicationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeedingMedicationValidationError) ErrorName() string {
	return "FeedingMedicationValidationError"
}

// Error satisfies the builtin error interface
func (e FeedingMedicationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeedingMedication.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeedingMedicationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeedingMedicationValidationError{}

// Validate checks the field values on MedicationSchedule with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MedicationSchedule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MedicationSchedule with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MedicationScheduleMultiError, or nil if none found.
func (m *MedicationSchedule) ValidateAll() error {
	return m.validate(true)
}

func (m *MedicationSchedule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Medication

	// no validation rules for Dosage

	// no validation rules for Frequency

	// no validation rules for AdministrationMethod

	// no validation rules for Notes

	if len(errors) > 0 {
		return MedicationScheduleMultiError(errors)
	}

	return nil
}

// MedicationScheduleMultiError is an error wrapping multiple validation errors
// returned by MedicationSchedule.ValidateAll() if the designated constraints
// aren't met.
type MedicationScheduleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MedicationScheduleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MedicationScheduleMultiError) AllErrors() []error { return m }

// MedicationScheduleValidationError is the validation error returned by
// MedicationSchedule.Validate if the designated constraints aren't met.
type MedicationScheduleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MedicationScheduleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MedicationScheduleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MedicationScheduleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MedicationScheduleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MedicationScheduleValidationError) ErrorName() string {
	return "MedicationScheduleValidationError"
}

// Error satisfies the builtin error interface
func (e MedicationScheduleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMedicationSchedule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MedicationScheduleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MedicationScheduleValidationError{}

// Validate checks the field values on ServiceOption with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ServiceOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceOption with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ServiceOptionMultiError, or
// nil if none found.
func (m *ServiceOption) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceOptionTemplateId

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServiceOptionValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServiceOptionValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServiceOptionValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServiceOptionValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServiceOptionValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServiceOptionValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Name

	// no validation rules for Quantity

	// no validation rules for Price

	// no validation rules for Tax

	// no validation rules for QuantityPerDay

	if len(errors) > 0 {
		return ServiceOptionMultiError(errors)
	}

	return nil
}

// ServiceOptionMultiError is an error wrapping multiple validation errors
// returned by ServiceOption.ValidateAll() if the designated constraints
// aren't met.
type ServiceOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceOptionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceOptionMultiError) AllErrors() []error { return m }

// ServiceOptionValidationError is the validation error returned by
// ServiceOption.Validate if the designated constraints aren't met.
type ServiceOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceOptionValidationError) ErrorName() string { return "ServiceOptionValidationError" }

// Error satisfies the builtin error interface
func (e ServiceOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceOptionValidationError{}

// Validate checks the field values on Metadata with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Metadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Metadata with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MetadataMultiError, or nil
// if none found.
func (m *Metadata) ValidateAll() error {
	return m.validate(true)
}

func (m *Metadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tags

	if len(errors) > 0 {
		return MetadataMultiError(errors)
	}

	return nil
}

// MetadataMultiError is an error wrapping multiple validation errors returned
// by Metadata.ValidateAll() if the designated constraints aren't met.
type MetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MetadataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MetadataMultiError) AllErrors() []error { return m }

// MetadataValidationError is the validation error returned by
// Metadata.Validate if the designated constraints aren't met.
type MetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MetadataValidationError) ErrorName() string { return "MetadataValidationError" }

// Error satisfies the builtin error interface
func (e MetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MetadataValidationError{}

// Validate checks the field values on AppointmentOperationResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AppointmentOperationResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppointmentOperationResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AppointmentOperationResultMultiError, or nil if none found.
func (m *AppointmentOperationResult) ValidateAll() error {
	return m.validate(true)
}

func (m *AppointmentOperationResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OperationType

	// no validation rules for Success

	// no validation rules for ErrorMessage

	if m.OldStartTime != nil {

		if all {
			switch v := interface{}(m.GetOldStartTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AppointmentOperationResultValidationError{
						field:  "OldStartTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AppointmentOperationResultValidationError{
						field:  "OldStartTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOldStartTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AppointmentOperationResultValidationError{
					field:  "OldStartTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.OldEndTime != nil {

		if all {
			switch v := interface{}(m.GetOldEndTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AppointmentOperationResultValidationError{
						field:  "OldEndTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AppointmentOperationResultValidationError{
						field:  "OldEndTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOldEndTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AppointmentOperationResultValidationError{
					field:  "OldEndTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.OldColorCode != nil {
		// no validation rules for OldColorCode
	}

	if m.OldStatus != nil {
		// no validation rules for OldStatus
	}

	if m.NewStartTime != nil {

		if all {
			switch v := interface{}(m.GetNewStartTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AppointmentOperationResultValidationError{
						field:  "NewStartTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AppointmentOperationResultValidationError{
						field:  "NewStartTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNewStartTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AppointmentOperationResultValidationError{
					field:  "NewStartTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.NewEndTime != nil {

		if all {
			switch v := interface{}(m.GetNewEndTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AppointmentOperationResultValidationError{
						field:  "NewEndTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AppointmentOperationResultValidationError{
						field:  "NewEndTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNewEndTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AppointmentOperationResultValidationError{
					field:  "NewEndTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.NewColorCode != nil {
		// no validation rules for NewColorCode
	}

	if m.NewStatus != nil {
		// no validation rules for NewStatus
	}

	if len(errors) > 0 {
		return AppointmentOperationResultMultiError(errors)
	}

	return nil
}

// AppointmentOperationResultMultiError is an error wrapping multiple
// validation errors returned by AppointmentOperationResult.ValidateAll() if
// the designated constraints aren't met.
type AppointmentOperationResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppointmentOperationResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppointmentOperationResultMultiError) AllErrors() []error { return m }

// AppointmentOperationResultValidationError is the validation error returned
// by AppointmentOperationResult.Validate if the designated constraints aren't met.
type AppointmentOperationResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppointmentOperationResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppointmentOperationResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppointmentOperationResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppointmentOperationResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppointmentOperationResultValidationError) ErrorName() string {
	return "AppointmentOperationResultValidationError"
}

// Error satisfies the builtin error interface
func (e AppointmentOperationResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppointmentOperationResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppointmentOperationResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppointmentOperationResultValidationError{}

// Validate checks the field values on PetOperationResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PetOperationResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PetOperationResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PetOperationResultMultiError, or nil if none found.
func (m *PetOperationResult) ValidateAll() error {
	return m.validate(true)
}

func (m *PetOperationResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PetId

	// no validation rules for OperationMode

	// no validation rules for Success

	// no validation rules for ErrorMessage

	switch v := m.OperationDetail.(type) {
	case *PetOperationResult_DeleteResult:
		if v == nil {
			err := PetOperationResultValidationError{
				field:  "OperationDetail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDeleteResult()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PetOperationResultValidationError{
						field:  "DeleteResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PetOperationResultValidationError{
						field:  "DeleteResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteResult()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PetOperationResultValidationError{
					field:  "DeleteResult",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PetOperationResult_CreateResult:
		if v == nil {
			err := PetOperationResultValidationError{
				field:  "OperationDetail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCreateResult()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PetOperationResultValidationError{
						field:  "CreateResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PetOperationResultValidationError{
						field:  "CreateResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateResult()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PetOperationResultValidationError{
					field:  "CreateResult",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PetOperationResult_UpdateResult:
		if v == nil {
			err := PetOperationResultValidationError{
				field:  "OperationDetail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUpdateResult()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PetOperationResultValidationError{
						field:  "UpdateResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PetOperationResultValidationError{
						field:  "UpdateResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateResult()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PetOperationResultValidationError{
					field:  "UpdateResult",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return PetOperationResultMultiError(errors)
	}

	return nil
}

// PetOperationResultMultiError is an error wrapping multiple validation errors
// returned by PetOperationResult.ValidateAll() if the designated constraints
// aren't met.
type PetOperationResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PetOperationResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PetOperationResultMultiError) AllErrors() []error { return m }

// PetOperationResultValidationError is the validation error returned by
// PetOperationResult.Validate if the designated constraints aren't met.
type PetOperationResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PetOperationResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PetOperationResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PetOperationResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PetOperationResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PetOperationResultValidationError) ErrorName() string {
	return "PetOperationResultValidationError"
}

// Error satisfies the builtin error interface
func (e PetOperationResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPetOperationResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PetOperationResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PetOperationResultValidationError{}

// Validate checks the field values on PetDeleteResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PetDeleteResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PetDeleteResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PetDeleteResultMultiError, or nil if none found.
func (m *PetDeleteResult) ValidateAll() error {
	return m.validate(true)
}

func (m *PetDeleteResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Deleted

	// no validation rules for DeletedServiceCount

	if len(errors) > 0 {
		return PetDeleteResultMultiError(errors)
	}

	return nil
}

// PetDeleteResultMultiError is an error wrapping multiple validation errors
// returned by PetDeleteResult.ValidateAll() if the designated constraints
// aren't met.
type PetDeleteResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PetDeleteResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PetDeleteResultMultiError) AllErrors() []error { return m }

// PetDeleteResultValidationError is the validation error returned by
// PetDeleteResult.Validate if the designated constraints aren't met.
type PetDeleteResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PetDeleteResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PetDeleteResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PetDeleteResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PetDeleteResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PetDeleteResultValidationError) ErrorName() string { return "PetDeleteResultValidationError" }

// Error satisfies the builtin error interface
func (e PetDeleteResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPetDeleteResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PetDeleteResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PetDeleteResultValidationError{}

// Validate checks the field values on PetCreateResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PetCreateResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PetCreateResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PetCreateResultMultiError, or nil if none found.
func (m *PetCreateResult) ValidateAll() error {
	return m.validate(true)
}

func (m *PetCreateResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NewPetId

	// no validation rules for CreatedServiceCount

	if len(errors) > 0 {
		return PetCreateResultMultiError(errors)
	}

	return nil
}

// PetCreateResultMultiError is an error wrapping multiple validation errors
// returned by PetCreateResult.ValidateAll() if the designated constraints
// aren't met.
type PetCreateResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PetCreateResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PetCreateResultMultiError) AllErrors() []error { return m }

// PetCreateResultValidationError is the validation error returned by
// PetCreateResult.Validate if the designated constraints aren't met.
type PetCreateResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PetCreateResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PetCreateResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PetCreateResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PetCreateResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PetCreateResultValidationError) ErrorName() string { return "PetCreateResultValidationError" }

// Error satisfies the builtin error interface
func (e PetCreateResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPetCreateResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PetCreateResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PetCreateResultValidationError{}

// Validate checks the field values on PetUpdateResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PetUpdateResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PetUpdateResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PetUpdateResultMultiError, or nil if none found.
func (m *PetUpdateResult) ValidateAll() error {
	return m.validate(true)
}

func (m *PetUpdateResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUpdatedPet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PetUpdateResultValidationError{
					field:  "UpdatedPet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PetUpdateResultValidationError{
					field:  "UpdatedPet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedPet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PetUpdateResultValidationError{
				field:  "UpdatedPet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UpdatedServiceCount

	if len(errors) > 0 {
		return PetUpdateResultMultiError(errors)
	}

	return nil
}

// PetUpdateResultMultiError is an error wrapping multiple validation errors
// returned by PetUpdateResult.ValidateAll() if the designated constraints
// aren't met.
type PetUpdateResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PetUpdateResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PetUpdateResultMultiError) AllErrors() []error { return m }

// PetUpdateResultValidationError is the validation error returned by
// PetUpdateResult.Validate if the designated constraints aren't met.
type PetUpdateResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PetUpdateResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PetUpdateResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PetUpdateResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PetUpdateResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PetUpdateResultValidationError) ErrorName() string { return "PetUpdateResultValidationError" }

// Error satisfies the builtin error interface
func (e PetUpdateResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPetUpdateResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PetUpdateResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PetUpdateResultValidationError{}

// Validate checks the field values on ServiceOperationResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceOperationResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceOperationResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceOperationResultMultiError, or nil if none found.
func (m *ServiceOperationResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceOperationResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceInstanceId

	// no validation rules for PetId

	// no validation rules for OperationMode

	// no validation rules for Success

	// no validation rules for ErrorMessage

	switch v := m.OperationDetail.(type) {
	case *ServiceOperationResult_DeleteResult:
		if v == nil {
			err := ServiceOperationResultValidationError{
				field:  "OperationDetail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDeleteResult()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServiceOperationResultValidationError{
						field:  "DeleteResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServiceOperationResultValidationError{
						field:  "DeleteResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteResult()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServiceOperationResultValidationError{
					field:  "DeleteResult",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ServiceOperationResult_CreateResult:
		if v == nil {
			err := ServiceOperationResultValidationError{
				field:  "OperationDetail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCreateResult()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServiceOperationResultValidationError{
						field:  "CreateResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServiceOperationResultValidationError{
						field:  "CreateResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateResult()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServiceOperationResultValidationError{
					field:  "CreateResult",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ServiceOperationResult_UpdateResult:
		if v == nil {
			err := ServiceOperationResultValidationError{
				field:  "OperationDetail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUpdateResult()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServiceOperationResultValidationError{
						field:  "UpdateResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServiceOperationResultValidationError{
						field:  "UpdateResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateResult()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServiceOperationResultValidationError{
					field:  "UpdateResult",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ServiceOperationResultMultiError(errors)
	}

	return nil
}

// ServiceOperationResultMultiError is an error wrapping multiple validation
// errors returned by ServiceOperationResult.ValidateAll() if the designated
// constraints aren't met.
type ServiceOperationResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceOperationResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceOperationResultMultiError) AllErrors() []error { return m }

// ServiceOperationResultValidationError is the validation error returned by
// ServiceOperationResult.Validate if the designated constraints aren't met.
type ServiceOperationResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceOperationResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceOperationResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceOperationResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceOperationResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceOperationResultValidationError) ErrorName() string {
	return "ServiceOperationResultValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceOperationResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceOperationResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceOperationResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceOperationResultValidationError{}

// Validate checks the field values on ServiceDeleteResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceDeleteResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceDeleteResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceDeleteResultMultiError, or nil if none found.
func (m *ServiceDeleteResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceDeleteResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Deleted

	// no validation rules for DeletedOptionCount

	if len(errors) > 0 {
		return ServiceDeleteResultMultiError(errors)
	}

	return nil
}

// ServiceDeleteResultMultiError is an error wrapping multiple validation
// errors returned by ServiceDeleteResult.ValidateAll() if the designated
// constraints aren't met.
type ServiceDeleteResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceDeleteResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceDeleteResultMultiError) AllErrors() []error { return m }

// ServiceDeleteResultValidationError is the validation error returned by
// ServiceDeleteResult.Validate if the designated constraints aren't met.
type ServiceDeleteResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceDeleteResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceDeleteResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceDeleteResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceDeleteResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceDeleteResultValidationError) ErrorName() string {
	return "ServiceDeleteResultValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceDeleteResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceDeleteResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceDeleteResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceDeleteResultValidationError{}

// Validate checks the field values on ServiceCreateResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceCreateResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceCreateResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceCreateResultMultiError, or nil if none found.
func (m *ServiceCreateResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceCreateResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NewServiceInstanceId

	// no validation rules for CreatedOptionCount

	if len(errors) > 0 {
		return ServiceCreateResultMultiError(errors)
	}

	return nil
}

// ServiceCreateResultMultiError is an error wrapping multiple validation
// errors returned by ServiceCreateResult.ValidateAll() if the designated
// constraints aren't met.
type ServiceCreateResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceCreateResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceCreateResultMultiError) AllErrors() []error { return m }

// ServiceCreateResultValidationError is the validation error returned by
// ServiceCreateResult.Validate if the designated constraints aren't met.
type ServiceCreateResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceCreateResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceCreateResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceCreateResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceCreateResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceCreateResultValidationError) ErrorName() string {
	return "ServiceCreateResultValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceCreateResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceCreateResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceCreateResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceCreateResultValidationError{}

// Validate checks the field values on ServiceUpdateResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceUpdateResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceUpdateResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceUpdateResultMultiError, or nil if none found.
func (m *ServiceUpdateResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceUpdateResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUpdatedService()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServiceUpdateResultValidationError{
					field:  "UpdatedService",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServiceUpdateResultValidationError{
					field:  "UpdatedService",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedService()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServiceUpdateResultValidationError{
				field:  "UpdatedService",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UpdatedOptionCount

	if len(errors) > 0 {
		return ServiceUpdateResultMultiError(errors)
	}

	return nil
}

// ServiceUpdateResultMultiError is an error wrapping multiple validation
// errors returned by ServiceUpdateResult.ValidateAll() if the designated
// constraints aren't met.
type ServiceUpdateResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceUpdateResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceUpdateResultMultiError) AllErrors() []error { return m }

// ServiceUpdateResultValidationError is the validation error returned by
// ServiceUpdateResult.Validate if the designated constraints aren't met.
type ServiceUpdateResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceUpdateResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceUpdateResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceUpdateResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceUpdateResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceUpdateResultValidationError) ErrorName() string {
	return "ServiceUpdateResultValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceUpdateResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceUpdateResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceUpdateResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceUpdateResultValidationError{}

// Validate checks the field values on OptionOperationResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OptionOperationResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OptionOperationResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OptionOperationResultMultiError, or nil if none found.
func (m *OptionOperationResult) ValidateAll() error {
	return m.validate(true)
}

func (m *OptionOperationResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceOptionId

	// no validation rules for ServiceInstanceId

	// no validation rules for OperationMode

	// no validation rules for Success

	// no validation rules for ErrorMessage

	switch v := m.OperationDetail.(type) {
	case *OptionOperationResult_DeleteResult:
		if v == nil {
			err := OptionOperationResultValidationError{
				field:  "OperationDetail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDeleteResult()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OptionOperationResultValidationError{
						field:  "DeleteResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OptionOperationResultValidationError{
						field:  "DeleteResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteResult()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OptionOperationResultValidationError{
					field:  "DeleteResult",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OptionOperationResult_CreateResult:
		if v == nil {
			err := OptionOperationResultValidationError{
				field:  "OperationDetail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCreateResult()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OptionOperationResultValidationError{
						field:  "CreateResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OptionOperationResultValidationError{
						field:  "CreateResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateResult()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OptionOperationResultValidationError{
					field:  "CreateResult",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OptionOperationResult_UpdateResult:
		if v == nil {
			err := OptionOperationResultValidationError{
				field:  "OperationDetail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUpdateResult()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OptionOperationResultValidationError{
						field:  "UpdateResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OptionOperationResultValidationError{
						field:  "UpdateResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateResult()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OptionOperationResultValidationError{
					field:  "UpdateResult",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return OptionOperationResultMultiError(errors)
	}

	return nil
}

// OptionOperationResultMultiError is an error wrapping multiple validation
// errors returned by OptionOperationResult.ValidateAll() if the designated
// constraints aren't met.
type OptionOperationResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OptionOperationResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OptionOperationResultMultiError) AllErrors() []error { return m }

// OptionOperationResultValidationError is the validation error returned by
// OptionOperationResult.Validate if the designated constraints aren't met.
type OptionOperationResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OptionOperationResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OptionOperationResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OptionOperationResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OptionOperationResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OptionOperationResultValidationError) ErrorName() string {
	return "OptionOperationResultValidationError"
}

// Error satisfies the builtin error interface
func (e OptionOperationResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOptionOperationResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OptionOperationResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OptionOperationResultValidationError{}

// Validate checks the field values on OptionDeleteResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OptionDeleteResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OptionDeleteResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OptionDeleteResultMultiError, or nil if none found.
func (m *OptionDeleteResult) ValidateAll() error {
	return m.validate(true)
}

func (m *OptionDeleteResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Deleted

	if len(errors) > 0 {
		return OptionDeleteResultMultiError(errors)
	}

	return nil
}

// OptionDeleteResultMultiError is an error wrapping multiple validation errors
// returned by OptionDeleteResult.ValidateAll() if the designated constraints
// aren't met.
type OptionDeleteResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OptionDeleteResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OptionDeleteResultMultiError) AllErrors() []error { return m }

// OptionDeleteResultValidationError is the validation error returned by
// OptionDeleteResult.Validate if the designated constraints aren't met.
type OptionDeleteResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OptionDeleteResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OptionDeleteResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OptionDeleteResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OptionDeleteResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OptionDeleteResultValidationError) ErrorName() string {
	return "OptionDeleteResultValidationError"
}

// Error satisfies the builtin error interface
func (e OptionDeleteResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOptionDeleteResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OptionDeleteResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OptionDeleteResultValidationError{}

// Validate checks the field values on OptionCreateResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OptionCreateResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OptionCreateResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OptionCreateResultMultiError, or nil if none found.
func (m *OptionCreateResult) ValidateAll() error {
	return m.validate(true)
}

func (m *OptionCreateResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NewServiceOptionId

	if len(errors) > 0 {
		return OptionCreateResultMultiError(errors)
	}

	return nil
}

// OptionCreateResultMultiError is an error wrapping multiple validation errors
// returned by OptionCreateResult.ValidateAll() if the designated constraints
// aren't met.
type OptionCreateResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OptionCreateResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OptionCreateResultMultiError) AllErrors() []error { return m }

// OptionCreateResultValidationError is the validation error returned by
// OptionCreateResult.Validate if the designated constraints aren't met.
type OptionCreateResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OptionCreateResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OptionCreateResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OptionCreateResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OptionCreateResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OptionCreateResultValidationError) ErrorName() string {
	return "OptionCreateResultValidationError"
}

// Error satisfies the builtin error interface
func (e OptionCreateResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOptionCreateResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OptionCreateResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OptionCreateResultValidationError{}

// Validate checks the field values on OptionUpdateResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OptionUpdateResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OptionUpdateResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OptionUpdateResultMultiError, or nil if none found.
func (m *OptionUpdateResult) ValidateAll() error {
	return m.validate(true)
}

func (m *OptionUpdateResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUpdatedOption()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OptionUpdateResultValidationError{
					field:  "UpdatedOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OptionUpdateResultValidationError{
					field:  "UpdatedOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedOption()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OptionUpdateResultValidationError{
				field:  "UpdatedOption",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OptionUpdateResultMultiError(errors)
	}

	return nil
}

// OptionUpdateResultMultiError is an error wrapping multiple validation errors
// returned by OptionUpdateResult.ValidateAll() if the designated constraints
// aren't met.
type OptionUpdateResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OptionUpdateResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OptionUpdateResultMultiError) AllErrors() []error { return m }

// OptionUpdateResultValidationError is the validation error returned by
// OptionUpdateResult.Validate if the designated constraints aren't met.
type OptionUpdateResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OptionUpdateResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OptionUpdateResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OptionUpdateResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OptionUpdateResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OptionUpdateResultValidationError) ErrorName() string {
	return "OptionUpdateResultValidationError"
}

// Error satisfies the builtin error interface
func (e OptionUpdateResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOptionUpdateResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OptionUpdateResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OptionUpdateResultValidationError{}
