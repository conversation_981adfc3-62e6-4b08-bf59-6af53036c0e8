// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/fulfillment/inner/inner_service.proto

package fulfillmentinnerpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = fulfillmentpb.ServiceType(0)
)

// Validate checks the field values on GetGroomingDetailByAppointmentIdRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetGroomingDetailByAppointmentIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetGroomingDetailByAppointmentIdRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetGroomingDetailByAppointmentIdRequestMultiError, or nil if none found.
func (m *GetGroomingDetailByAppointmentIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetGroomingDetailByAppointmentIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppointmentId

	if len(errors) > 0 {
		return GetGroomingDetailByAppointmentIdRequestMultiError(errors)
	}

	return nil
}

// GetGroomingDetailByAppointmentIdRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetGroomingDetailByAppointmentIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetGroomingDetailByAppointmentIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetGroomingDetailByAppointmentIdRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetGroomingDetailByAppointmentIdRequestMultiError) AllErrors() []error { return m }

// GetGroomingDetailByAppointmentIdRequestValidationError is the validation
// error returned by GetGroomingDetailByAppointmentIdRequest.Validate if the
// designated constraints aren't met.
type GetGroomingDetailByAppointmentIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetGroomingDetailByAppointmentIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetGroomingDetailByAppointmentIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetGroomingDetailByAppointmentIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetGroomingDetailByAppointmentIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetGroomingDetailByAppointmentIdRequestValidationError) ErrorName() string {
	return "GetGroomingDetailByAppointmentIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetGroomingDetailByAppointmentIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetGroomingDetailByAppointmentIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetGroomingDetailByAppointmentIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetGroomingDetailByAppointmentIdRequestValidationError{}

// Validate checks the field values on GetGroomingDetailByAppointmentIdResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetGroomingDetailByAppointmentIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetGroomingDetailByAppointmentIdResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetGroomingDetailByAppointmentIdResponseMultiError, or nil if none found.
func (m *GetGroomingDetailByAppointmentIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetGroomingDetailByAppointmentIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetGroomingDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetGroomingDetailByAppointmentIdResponseValidationError{
						field:  fmt.Sprintf("GroomingDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetGroomingDetailByAppointmentIdResponseValidationError{
						field:  fmt.Sprintf("GroomingDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetGroomingDetailByAppointmentIdResponseValidationError{
					field:  fmt.Sprintf("GroomingDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetGroomingDetailByAppointmentIdResponseMultiError(errors)
	}

	return nil
}

// GetGroomingDetailByAppointmentIdResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetGroomingDetailByAppointmentIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetGroomingDetailByAppointmentIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetGroomingDetailByAppointmentIdResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetGroomingDetailByAppointmentIdResponseMultiError) AllErrors() []error { return m }

// GetGroomingDetailByAppointmentIdResponseValidationError is the validation
// error returned by GetGroomingDetailByAppointmentIdResponse.Validate if the
// designated constraints aren't met.
type GetGroomingDetailByAppointmentIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetGroomingDetailByAppointmentIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetGroomingDetailByAppointmentIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetGroomingDetailByAppointmentIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetGroomingDetailByAppointmentIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetGroomingDetailByAppointmentIdResponseValidationError) ErrorName() string {
	return "GetGroomingDetailByAppointmentIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetGroomingDetailByAppointmentIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetGroomingDetailByAppointmentIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetGroomingDetailByAppointmentIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetGroomingDetailByAppointmentIdResponseValidationError{}
