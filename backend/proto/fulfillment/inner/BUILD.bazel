load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "innerpb_proto",
    srcs = [
        "inner.proto",
        "inner_service.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//backend/proto/fulfillment/v1:fulfillmentpb_proto",
        "@com_envoyproxy_protoc_gen_validate//validate:validate_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

go_proto_library(
    name = "innerpb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_proto",
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "//:pgv_plugin_go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/fulfillment/inner",
    proto = ":innerpb_proto",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/proto/fulfillment/v1:fulfillment",
        "@com_envoyproxy_protoc_gen_validate//validate:go_default_library",
    ],
)

go_library(
    name = "inner",
    embed = [":innerpb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/fulfillment/inner",
    visibility = ["//visibility:public"],
)
