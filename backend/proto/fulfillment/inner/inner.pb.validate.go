// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/fulfillment/inner/inner.proto

package fulfillmentinnerpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = fulfillmentpb.OverrideType(0)
)

// Validate checks the field values on GroomingPetDetailDTO with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GroomingPetDetailDTO) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GroomingPetDetailDTO with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GroomingPetDetailDTOMultiError, or nil if none found.
func (m *GroomingPetDetailDTO) ValidateAll() error {
	return m.validate(true)
}

func (m *GroomingPetDetailDTO) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for GroomingId

	// no validation rules for PetId

	// no validation rules for StaffId

	// no validation rules for ServiceId

	// no validation rules for ServiceTime

	// no validation rules for ServicePrice

	// no validation rules for StartTime

	// no validation rules for EndTime

	// no validation rules for ScopeTypePrice

	// no validation rules for ScopeTypeTime

	// no validation rules for StarStaffId

	// no validation rules for ServiceName

	// no validation rules for ServiceType

	// no validation rules for ServiceStatus

	// no validation rules for StaffFirstName

	// no validation rules for StaffLastName

	// no validation rules for PetName

	// no validation rules for PetLifeStatus

	// no validation rules for EnableOperation

	// no validation rules for ColorCode

	// no validation rules for WorkMode

	for idx, item := range m.GetOperationList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GroomingPetDetailDTOValidationError{
						field:  fmt.Sprintf("OperationList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GroomingPetDetailDTOValidationError{
						field:  fmt.Sprintf("OperationList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GroomingPetDetailDTOValidationError{
					field:  fmt.Sprintf("OperationList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ServiceItemType

	// no validation rules for LodgingId

	// no validation rules for StartDate

	// no validation rules for EndDate

	// no validation rules for PriceUnit

	// no validation rules for SpecificDates

	// no validation rules for AssociatedServiceId

	// no validation rules for Quantity

	// no validation rules for PriceOverrideType

	// no validation rules for DurationOverrideType

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GroomingPetDetailDTOValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GroomingPetDetailDTOValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GroomingPetDetailDTOValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for QuantityPerDay

	// no validation rules for RequireDedicatedStaff

	// no validation rules for DateType

	// no validation rules for OrderLineItemId

	// no validation rules for ExternalId

	if len(errors) > 0 {
		return GroomingPetDetailDTOMultiError(errors)
	}

	return nil
}

// GroomingPetDetailDTOMultiError is an error wrapping multiple validation
// errors returned by GroomingPetDetailDTO.ValidateAll() if the designated
// constraints aren't met.
type GroomingPetDetailDTOMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GroomingPetDetailDTOMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GroomingPetDetailDTOMultiError) AllErrors() []error { return m }

// GroomingPetDetailDTOValidationError is the validation error returned by
// GroomingPetDetailDTO.Validate if the designated constraints aren't met.
type GroomingPetDetailDTOValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GroomingPetDetailDTOValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GroomingPetDetailDTOValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GroomingPetDetailDTOValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GroomingPetDetailDTOValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GroomingPetDetailDTOValidationError) ErrorName() string {
	return "GroomingPetDetailDTOValidationError"
}

// Error satisfies the builtin error interface
func (e GroomingPetDetailDTOValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGroomingPetDetailDTO.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GroomingPetDetailDTOValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GroomingPetDetailDTOValidationError{}

// Validate checks the field values on GroomingServiceOperationDTO with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GroomingServiceOperationDTO) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GroomingServiceOperationDTO with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GroomingServiceOperationDTOMultiError, or nil if none found.
func (m *GroomingServiceOperationDTO) ValidateAll() error {
	return m.validate(true)
}

func (m *GroomingServiceOperationDTO) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for BusinessId

	// no validation rules for GroomingId

	// no validation rules for GroomingServiceId

	// no validation rules for PetId

	// no validation rules for StaffId

	// no validation rules for OperationName

	// no validation rules for StartTime

	// no validation rules for Duration

	// no validation rules for Comment

	// no validation rules for Price

	// no validation rules for PriceRatio

	if len(errors) > 0 {
		return GroomingServiceOperationDTOMultiError(errors)
	}

	return nil
}

// GroomingServiceOperationDTOMultiError is an error wrapping multiple
// validation errors returned by GroomingServiceOperationDTO.ValidateAll() if
// the designated constraints aren't met.
type GroomingServiceOperationDTOMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GroomingServiceOperationDTOMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GroomingServiceOperationDTOMultiError) AllErrors() []error { return m }

// GroomingServiceOperationDTOValidationError is the validation error returned
// by GroomingServiceOperationDTO.Validate if the designated constraints
// aren't met.
type GroomingServiceOperationDTOValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GroomingServiceOperationDTOValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GroomingServiceOperationDTOValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GroomingServiceOperationDTOValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GroomingServiceOperationDTOValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GroomingServiceOperationDTOValidationError) ErrorName() string {
	return "GroomingServiceOperationDTOValidationError"
}

// Error satisfies the builtin error interface
func (e GroomingServiceOperationDTOValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGroomingServiceOperationDTO.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GroomingServiceOperationDTOValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GroomingServiceOperationDTOValidationError{}
