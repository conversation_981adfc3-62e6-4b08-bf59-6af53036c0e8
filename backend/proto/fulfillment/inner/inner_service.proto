syntax = "proto3";
// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: This is a legacy inner service package that doesn't follow versioned package naming convention. --)
package backend.proto.fulfillment.inner;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "backend/proto/fulfillment/inner/inner.proto"; // 这里需要引入inner.proto
import "backend/proto/fulfillment/v1/common.proto"; // 这里需要引入common.proto

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/inner;fulfillmentinnerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.inner";

// 通过预约ID获取美容详情请求
message GetGroomingDetailByAppointmentIdRequest {
  // 预约ID
  int64 appointment_id = 1;
  // 服务项目类型列表 (可选，对应原SQL中的serviceItems参数)
  repeated backend.proto.fulfillment.v1.ServiceType service_item_types = 2;
}

// 通过预约ID获取美容详情响应
message GetGroomingDetailByAppointmentIdResponse {
  // 美容详情列表
  repeated GroomingPetDetailDTO grooming_details = 1;
}

// 内部服务接口，用于处理fulfillment模块内部的业务逻辑
// (-- api-linter: core::0191::file-layout=disabled
//     aip.dev/not-precedent: Legacy service layout maintained for backward compatibility. --)
service InnerService {
  // 通过预约ID获取美容详情信息，包括宠物详情、服务操作等相关数据
  // (-- api-linter: core::0136::prepositions=disabled
  //     api-linter: core::0131::response-message-name=disabled
  //     aip.dev/not-precedent: Legacy method naming convention maintained for backward compatibility. --)
  rpc GetGroomingDetailByAppointmentId(GetGroomingDetailByAppointmentIdRequest) returns (GetGroomingDetailByAppointmentIdResponse);
}
