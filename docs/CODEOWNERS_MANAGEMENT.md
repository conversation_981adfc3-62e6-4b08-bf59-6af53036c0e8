# CODEOWNERS 管理系统

## 概述

本系统提供自动化的CODEOWNERS管理功能，支持：
1. 在服务级别配置CODEOWNERS
2. 自动聚合到根目录的CODEOWNERS文件
3. 新服务创建时自动设置CODEOWNERS

## 文件结构

```
moego/
├── scripts/
│   ├── generate_codeowners.sh          # 生成CODEOWNERS文件
│   └── .create_app.sh                  # 创建新服务（已集成CODEOWNERS功能）
├── backend/app/
│   ├── customer/
│   │   └── metadata.yaml               # 包含codeowners配置
│   └── [其他服务]/
└── CODEOWNERS                          # 自动生成的文件
```

## 使用方法

### 1. 为新服务设置CODEOWNERS

创建新服务时，系统会自动设置CODEOWNERS：

```bash
# 创建新服务
make create module=your_module service=your_service

# 系统会自动：
# 1. 在metadata.yaml中添加codeowners配置
# 2. 生成根目录的CODEOWNERS文件
```

### 2. 手动更新CODEOWNERS文件

```bash
# 重新生成CODEOWNERS文件
make codeowners

# 或者直接运行脚本
./scripts/generate_codeowners.sh
```

## 配置格式

### metadata.yaml 配置

在服务的 `metadata.yaml` 文件中添加 `codeowners` 字段：

```yaml
spec:
  name: moego-customer
  description: a service for the Moego
  cd: true
  codeowners:
    - "@moego-backend-crm"
    - "<EMAIL>"
```

### 支持的格式

- 团队：`@team-name`
- 个人：`@username`
- 邮箱：`<EMAIL>`

## 生成的CODEOWNERS文件格式

```gitignore
# This file is auto-generated by scripts/generate_codeowners.sh
# Do not edit this file manually. Edit metadata.yaml files in each app directory instead.

# Code owners for customer service
/backend/app/customer/ @crm-team

# Code owners for search service
/backend/app/search/ <EMAIL>



# Default code owners for everything else
* <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL>
```

## 最佳实践

1. **服务级别管理**：每个服务的CODEOWNERS配置在各自的 `metadata.yaml` 文件中
2. **团队优先**：优先使用团队级别的CODEOWNERS，而不是个人
3. **按需配置**：只有需要特殊权限控制的服务才配置codeowners，其他服务由全局规则控制
4. **备份机制**：系统会自动备份原有的CODEOWNERS文件到 `backup/codeowners/` 目录

## 故障排除

### 常见问题

1. **脚本权限问题**
   ```bash
   chmod +x scripts/generate_codeowners.sh
   ```

2. **yq工具缺失**
   ```bash
   # macOS
   brew install yq
   
   # Linux
   wget https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 -O /usr/bin/yq
   chmod +x /usr/bin/yq
   ```

3. **备份文件恢复**
   ```bash
   # 查看备份文件
   ls -la backup/codeowners/CODEOWNERS.backup.*
   
   # 恢复备份
   cp backup/codeowners/CODEOWNERS.backup.YYYYMMDD_HHMMSS CODEOWNERS
   ```

## 维护者

- jett <<EMAIL>> 