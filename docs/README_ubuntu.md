# moego - Ubuntu 用户指南

## 目录
- [moego - Ubuntu 用户指南](#moego---ubuntu-用户指南)
  - [目录](#目录)
  - [什么是 mono-repo](#什么是-mono-repo)
  - [环境依赖](#环境依赖)
  - [初始化项目](#初始化项目)
  - [构建项目](#构建项目)
  - [创建新项目](#创建新项目)
  - [项目结构](#项目结构)

## 什么是 mono-repo
详情请参阅 [什么是 mono-repo](./docs/WHATSMONOREPO.md)

关于 Bazel 的说明请参阅 [Bazel 使用指南](./docs/USE_BAZEL.md)

## 环境依赖
在 Ubuntu 系统上，您需要安装以下依赖：

1. Golang > 1.24.0
   ```bash
   # 使用 apt 安装
   sudo apt update
   sudo apt install golang-go
   
   # 或者从官网下载最新版本
   wget https://go.dev/dl/go1.24.0.linux-amd64.tar.gz
   sudo rm -rf /usr/local/go && sudo tar -C /usr/local -xzf go1.24.0.linux-amd64.tar.gz
   echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.profile
   source ~/.profile
   ```

2. lcov 工具，用于单测报告解析
   ```bash
   sudo apt install lcov
   ```

3. buf 工具，用于 proto 文件管理
   ```bash
   # 安装 buf
   GO111MODULE=on go install github.com/bufbuild/buf/cmd/buf@latest
   
   # 或者使用官方安装脚本
   curl -sSL https://github.com/bufbuild/buf/releases/download/v1.30.0/buf-Linux-x86_64 -o buf
   chmod +x buf
   sudo mv buf /usr/local/bin/
   ```

4. Bazelisk >= 1.24.1
   ```bash
   # 从 GitHub 下载最新版本
   wget https://github.com/bazelbuild/bazelisk/releases/download/v1.19.0/bazelisk-linux-amd64
   chmod +x bazelisk-linux-amd64
   sudo mv bazelisk-linux-amd64 /usr/local/bin/bazelisk
   sudo ln -s /usr/local/bin/bazelisk /usr/local/bin/bazel
   
   # 或在执行 make init 时会自动通过 go install 安装
   ```

5. （可选）执行 API 测试时，需要 openapi-generator-cli，详情请参阅 [API 测试说明](./backend/test/api_integration/README.md)
   ```bash
   # 安装 Node.js 和 npm（如果尚未安装）
   sudo apt install nodejs npm
   
   # 安装 openapi-generator-cli
   npm install @openapitools/openapi-generator-cli -g
   ```

## 初始化项目
开始之前，请确保您的 git 配置正确，包括但不限于 token、邮箱、用户名等。

并检查 go env 配置，确保以下环境变量正确：
```bash
export GOPRIVATE=github.com/MoeGolibrary 
```

\>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 准备起飞 🛫🛫🛫

执行命令：
```bash
make init
```

`make init` 会安装项目所需基本依赖，包括但不限于：
- golangci-lint 
- goimports-reviser 
- buf 
- ...

并且会初始化 API 测试需要的代码，使用 gazelle 更新所有 BUILD.bazel 文件。

## 构建项目
编译使用 [Makefile](./Makefile)，会基于 bazelisk 进行编译。

**编译前使用 gazelle 管理依赖**
```bash
make gazelle
```
`mode=all` 更新 `moego` 项目下（除了 [.bazelignore](.bazelignore)）所有 `BUILD.bazel` 文件。

编译项目的产物不会提交到远程仓库。

- 编译所有项目（"backend" 目录下所有项目），会自动执行 `make gazelle`
  ```bash
  make build
  ```
- 当需要指定编译目录时，使用 `dir` 参数
  ```bash
  # 编译指定目录
  make build dir=//backend/common/rpc/rpc/examples/helloworld/server
  # 编译指定目录下的所有项目
  make build dir=//backend/common/rpc/...
  ```

**注意**：虽然编译时并不需要把 proto 文件编译成 go、rpc、validate 等文件，但是本地调试时如果没有产物编译器会出现如找不到依赖、编译错误、无法跳转等问题，为了避免这一情况，提供了 [proto 编译脚本](./scripts/.proto.sh)，使用方式：
```bash
make proto
```

## 创建新项目
创建新项目前，需要先在 [modules.yml](./backend/.modules.yml) 文件中登记，格式为：
```yaml
# 实例，请参考 modules.yml 文件，根据具体情况修改
modules:
  - name: $(module_name) # 模块名暂定为部门名，比如 platform\foundation
    desc: $(module_desc)
    owner: $(module_owner)
    code: $(module_code)
```

Makefile 提供了创建新项目的命令，使用方式：
```bash
make create module=$(module) service=$(service)
```

该命令会调用 [create_app](./scripts/.create_app.sh) 脚本，脚本会自动执行以下操作（以 todo 项目为例）：
1. 在 backend 目录下创建新项目，并复制 [template-go](./template/template-go) 目录到新项目目录
2. 修改 [main.go](./backend/app/todo/main.go) 文件，将服务名替换为新项目名
3. 修改 [CODEOWNERS](./backend/app/todo/CODEOWNERS) 文件，将 owner 替换为新项目 owner，项目 owner 取当前执行命令的 git 邮箱
4. 在 backend/proto 目录下创建新项目的 [proto](./backend/proto/todo/v1/todo.proto) 文件
5. 为该项目生成一个唯一的错误码段
6. 修改 [proto](./backend/proto/todo/v1/todo.proto) 文件中的服务名，适配新项目
7. 通过 `gazelle` 在新项目中新增 `BUILD.bazel`

您需要自己动手的事情：
1. 修改 [config.yaml](./backend/app/todo/config.yaml) 文件，配置项目所需配置
2. 修改 [README.md](./backend/app/todo/README.md) 文件描述项目
3. 如果需要编译 proto 生成 `pb.go`，`.validate.go`，`.grpc.go` 等文件，请执行 `make proto`

## 项目结构
截止 2024-12-18，项目结构如下：
```
.
├── .github              # github actions\hooks 配置
├── backend              # 后端项目
│   ├── app              # 存放服务代码
│   │   └── helloworld   # 示例服务
│   ├── common           # 公共库
│   │   └── rpc          # rpc 库
│   │   └── utils        # 工具库
│   ├── docker           # docker 配置
│   ├── test             # 存放api测试代码
│   ├── tools            # 存放工具代码
│   └── proto            # 存放proto文件
├── bazel                # bazel 配置
│   ├── out              # 编译产物, 仅本地存在  
│   └── tools            # bazel 工具
├── scripts              # 脚本
├── docs                 # 文档
└── template             # 模板
    └── template-go      # go项目模板
``` 