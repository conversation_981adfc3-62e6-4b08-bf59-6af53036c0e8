#!/bin/bash

# Setup protobuf dependencies for IDE support
# This script exports external proto dependencies to backend/proto/deps
# so that IDEs can resolve proto imports correctly

set -e

DEPS_DIR="backend/proto/deps"

echo "Setting up protobuf dependencies..."

# Create deps directory if it doesn't exist
mkdir -p "$DEPS_DIR"

# Export protoc-gen-validate protos
echo "Exporting protoc-gen-validate..."
buf export buf.build/envoyproxy/protoc-gen-validate -o "$DEPS_DIR"

# Export googleapis protos
echo "Exporting googleapis..."
buf export buf.build/googleapis/googleapis -o "$DEPS_DIR"

# Create .gitkeep to preserve directory structure
touch "$DEPS_DIR/.gitkeep"

echo "✅ Protobuf dependencies setup complete!"
echo "📁 Dependencies exported to: $DEPS_DIR"
echo ""
echo "Files exported:"
echo "  - validate/validate.proto (from protoc-gen-validate)"
echo "  - google/api/*.proto (from googleapis)"
echo "  - google/protobuf/*.proto (from googleapis)"
echo ""
echo "💡 If you still see import errors in your IDE:"
echo "   1. Restart your IDE"
echo "   2. Or use 'Invalidate Caches and Restart' in GoLand"
