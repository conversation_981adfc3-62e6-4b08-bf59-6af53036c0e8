load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "aws",
    srcs = [
        "client.go",
        "secret_manager.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/cli/devops_executor/utils/aws",
    visibility = ["//visibility:public"],
    deps = [
        "//cli/devops_executor/utils",
        "@com_github_aws_aws_sdk_go_v2//aws",
        "@com_github_aws_aws_sdk_go_v2_config//:config",
        "@com_github_aws_aws_sdk_go_v2_credentials//:credentials",
        "@com_github_aws_aws_sdk_go_v2_service_secretsmanager//:secretsmanager",
        "@com_github_aws_aws_sdk_go_v2_service_secretsmanager//types",
    ],
)
