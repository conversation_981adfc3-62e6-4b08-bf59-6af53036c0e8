package utils

import (
	"errors"
	"sort"
	"strings"
)

func IsBlank(str string) bool {
	return len(strings.TrimSpace(str)) == 0
}

func MergeMap(a, b map[string]string) {
	if a != nil && b != nil && 0 < len(b) {
		for k, v := range b {
			a[k] = v
		}
	}
}

func MaskText(text string) string {
	n := len(text)
	if n > 16 {
		return text[:4] + "********" + text[n-4:]
	}
	if n > 12 {
		return text[:3] + "********" + text[n-3:]
	}
	if n > 8 {
		return text[:2] + "********" + text[n-2:]
	}
	if n > 5 {
		return text[:1] + "********" + text[n-1:]
	}

	return "****************"
}

func ParseLineToMap(line string, separator string, keys map[string]bool) (map[string]string, error) {
	if IsBlank(line) {
		return nil, errors.New("string is empty")
	}

	result := make(map[string]string)
	pairs := strings.SplitN(line, separator, len(keys))
	for _, pair := range pairs {
		k, v, err := ParsePair(pair)
		if err != nil {
			return nil, err
		}
		_, allowKey := keys[k]
		if !allowKey {
			return nil, errors.New("invalid key found: " + k)
		}
		_, existsKey := result[k]
		if existsKey {
			return nil, errors.New("duplicate key found: " + k)
		}

		result[k] = v
	}

	for k, v := range keys {
		if v {
			_, ok := result[k]
			if !ok {
				return nil, errors.New("missing required field: " + k)
			}
		}
	}

	return result, nil
}

func ParsePair(pair string) (string, string, error) {
	kv := strings.SplitN(pair, "=", 2)
	if len(kv) != 2 {
		return "", "", errors.New("invalid key-value format: " + pair)
	}
	return strings.TrimSpace(kv[0]), strings.TrimSpace(kv[1]), nil
}

func Replace(content string, dictionary map[string]string) (string, int) {
	indexes := findPairs(content, dictionary)
	text := replaceByIndexes(content, indexes)
	return text, len(indexes)
}

func findPairs(content string, dictionary map[string]string) []Pair[Pair[int, int], string] {
	var result []Pair[Pair[int, int], string]

	for k, v := range dictionary {
		indexes := findIndexes(content, k)
		for _, pair := range indexes {
			result = append(result, Pair[Pair[int, int], string]{
				Key:   pair,
				Value: v,
			})
		}
	}

	sort.Slice(result, func(i, j int) bool {
		return result[i].Key.Key < result[j].Key.Key
	})

	return result
}

func findIndexes(content, key string) []Pair[int, int] {
	target := "${" + key + "}"
	var result []Pair[int, int]
	index := strings.Index(content, target)

	if index >= 0 {
		endIndex := index + len(target)
		result = append(result, Pair[int, int]{Key: index, Value: endIndex})

		for startIndex := endIndex; startIndex < len(content); {
			pos := strings.Index(content[startIndex:], target)
			if pos < 0 {
				break
			}
			pos += startIndex
			endIndex = pos + len(target)
			result = append(result, Pair[int, int]{Key: pos, Value: endIndex})
			startIndex = endIndex
		}
	}

	return result
}

func replaceByIndexes(src string, pairs []Pair[Pair[int, int], string]) string {
	if len(pairs) == 0 {
		return src
	}

	var builder strings.Builder
	lastIndex := 0
	for _, pair := range pairs {
		builder.WriteString(src[lastIndex:pair.Key.Key])
		builder.WriteString(pair.Value)
		lastIndex = pair.Key.Value
	}

	if lastIndex < len(src) {
		builder.WriteString(src[lastIndex:])
	}

	return builder.String()
}
