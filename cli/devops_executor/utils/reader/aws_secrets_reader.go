package reader

import (
	"context"
	"errors"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager/types"

	"github.com/MoeGolibrary/moego/cli/devops_executor/utils"
	"github.com/MoeGolibrary/moego/cli/devops_executor/utils/aws"
)

type AWSSecretsReader struct {
	client   *secretsmanager.Client
	Name     string
	Prefix   string
	Optional bool
}

func newAWSSecretsReader(client *secretsmanager.Client, name string, prefix string, optional bool) PairsReader {
	return &AWSSecretsReader{client: client, Name: name, Prefix: prefix, Optional: optional}
}

func (reader *AWSSecretsReader) Type() string {
	return "AWSSecretsReader"
}

func (reader *AWSSecretsReader) Read(ctx context.Context) (map[string]string, error) {
	content, err := aws.GetSecret(ctx, reader.client, reader.Name)
	if err != nil {
		var notFound *types.ResourceNotFoundException
		if errors.As(err, &notFound) {
			fmt.Println("NOT found aws secret manager: ", reader.Name)
			if reader.Optional {
				return make(map[string]string), nil
			}
			return nil, err
		}

		return nil, fmt.Errorf("failed to get secret: %s %v", reader.Name, err)
	}

	secrets, err := aws.SecretsToMap(content.SecretString)
	if err != nil {
		return nil, err
	}

	if utils.IsBlank(reader.Prefix) {
		return secrets, nil
	}

	result := make(map[string]string)
	for k, v := range secrets {
		result[reader.Prefix+k] = v
	}
	return result, nil
}
