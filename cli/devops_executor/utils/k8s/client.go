package k8s

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"

	"github.com/MoeGolibrary/moego/cli/devops_executor/utils"
)

var CreateClient = createClient

func createClient(cluster string) (kubernetes.Interface, error) {
	// load config
	var kubeconfig = os.Getenv("KUBECONFIG")
	if utils.IsBlank(kubeconfig) {
		if home := homedir.HomeDir(); home != "" {
			kubeconfig = filepath.Join(home, ".kube", "config")
		}
	}
	if utils.IsBlank(kubeconfig) {
		fmt.Println("NOT found file: kubeconfig")
		return nil, errors.New("NOT found kubeconfig")
	}

	var config *rest.Config
	var err error

	// create config
	if utils.IsBlank(cluster) {
		config, err = clientcmd.BuildConfigFromFlags("", kubeconfig)
	} else {
		config, err = clientcmd.NewNonInteractiveDeferredLoadingClientConfig(
			&clientcmd.ClientConfigLoadingRules{ExplicitPath: kubeconfig},
			&clientcmd.ConfigOverrides{CurrentContext: cluster},
		).ClientConfig()
	}
	if err != nil {
		fmt.Printf("create config failed: %v\n", err)
		return nil, err
	}

	// create client
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		fmt.Printf("create client failed: %v\n", err)
		return nil, err
	}

	return clientset, nil
}
