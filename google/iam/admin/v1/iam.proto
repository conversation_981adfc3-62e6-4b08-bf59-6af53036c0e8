// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.iam.admin.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/type/expr.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Iam.Admin.V1";
option go_package = "cloud.google.com/go/iam/admin/apiv1/adminpb;adminpb";
option java_multiple_files = true;
option java_package = "com.google.iam.admin.v1";
option php_namespace = "Google\\Cloud\\Iam\\Admin\\V1";

// Creates and manages Identity and Access Management (IAM) resources.
//
// You can use this service to work with all of the following resources:
//
// * **Service accounts**, which identify an application or a virtual machine
//   (VM) instance rather than a person
// * **Service account keys**, which service accounts use to authenticate with
//   Google APIs
// * **IAM policies for service accounts**, which specify the roles that a
//   principal has for the service account
// * **IAM custom roles**, which help you limit the number of permissions that
//   you grant to principals
//
// In addition, you can use this service to complete the following tasks, among
// others:
//
// * Test whether a service account can use specific permissions
// * Check which roles you can grant for a specific resource
// * Lint, or validate, condition expressions in an IAM policy
//
// When you read data from the IAM API, each read is eventually consistent. In
// other words, if you write data with the IAM API, then immediately read that
// data, the read operation might return an older version of the data. To deal
// with this behavior, your application can retry the request with truncated
// exponential backoff.
//
// In contrast, writing data to the IAM API is sequentially consistent. In other
// words, write operations are always processed in the order in which they were
// received.
service IAM {
  option (google.api.default_host) = "iam.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists every [ServiceAccount][google.iam.admin.v1.ServiceAccount] that belongs to a specific project.
  rpc ListServiceAccounts(ListServiceAccountsRequest) returns (ListServiceAccountsResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*}/serviceAccounts"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a [ServiceAccount][google.iam.admin.v1.ServiceAccount].
  rpc GetServiceAccount(GetServiceAccountRequest) returns (ServiceAccount) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/serviceAccounts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a [ServiceAccount][google.iam.admin.v1.ServiceAccount].
  rpc CreateServiceAccount(CreateServiceAccountRequest) returns (ServiceAccount) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*}/serviceAccounts"
      body: "*"
    };
    option (google.api.method_signature) = "name,account_id,service_account";
  }

  // **Note:** We are in the process of deprecating this method. Use
  // [PatchServiceAccount][google.iam.admin.v1.IAM.PatchServiceAccount] instead.
  //
  // Updates a [ServiceAccount][google.iam.admin.v1.ServiceAccount].
  //
  // You can update only the `display_name` field.
  rpc UpdateServiceAccount(ServiceAccount) returns (ServiceAccount) {
    option (google.api.http) = {
      put: "/v1/{name=projects/*/serviceAccounts/*}"
      body: "*"
    };
  }

  // Patches a [ServiceAccount][google.iam.admin.v1.ServiceAccount].
  rpc PatchServiceAccount(PatchServiceAccountRequest) returns (ServiceAccount) {
    option (google.api.http) = {
      patch: "/v1/{service_account.name=projects/*/serviceAccounts/*}"
      body: "*"
    };
  }

  // Deletes a [ServiceAccount][google.iam.admin.v1.ServiceAccount].
  //
  // **Warning:** After you delete a service account, you might not be able to
  // undelete it. If you know that you need to re-enable the service account in
  // the future, use [DisableServiceAccount][google.iam.admin.v1.IAM.DisableServiceAccount] instead.
  //
  // If you delete a service account, IAM permanently removes the service
  // account 30 days later. Google Cloud cannot recover the service account
  // after it is permanently removed, even if you file a support request.
  //
  // To help avoid unplanned outages, we recommend that you disable the service
  // account before you delete it. Use [DisableServiceAccount][google.iam.admin.v1.IAM.DisableServiceAccount] to disable the
  // service account, then wait at least 24 hours and watch for unintended
  // consequences. If there are no unintended consequences, you can delete the
  // service account.
  rpc DeleteServiceAccount(DeleteServiceAccountRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/serviceAccounts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Restores a deleted [ServiceAccount][google.iam.admin.v1.ServiceAccount].
  //
  // **Important:** It is not always possible to restore a deleted service
  // account. Use this method only as a last resort.
  //
  // After you delete a service account, IAM permanently removes the service
  // account 30 days later. There is no way to restore a deleted service account
  // that has been permanently removed.
  rpc UndeleteServiceAccount(UndeleteServiceAccountRequest) returns (UndeleteServiceAccountResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/serviceAccounts/*}:undelete"
      body: "*"
    };
  }

  // Enables a [ServiceAccount][google.iam.admin.v1.ServiceAccount] that was disabled by
  // [DisableServiceAccount][google.iam.admin.v1.IAM.DisableServiceAccount].
  //
  // If the service account is already enabled, then this method has no effect.
  //
  // If the service account was disabled by other means—for example, if Google
  // disabled the service account because it was compromised—you cannot use this
  // method to enable the service account.
  rpc EnableServiceAccount(EnableServiceAccountRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/serviceAccounts/*}:enable"
      body: "*"
    };
  }

  // Disables a [ServiceAccount][google.iam.admin.v1.ServiceAccount] immediately.
  //
  // If an application uses the service account to authenticate, that
  // application can no longer call Google APIs or access Google Cloud
  // resources. Existing access tokens for the service account are rejected, and
  // requests for new access tokens will fail.
  //
  // To re-enable the service account, use [EnableServiceAccount][google.iam.admin.v1.IAM.EnableServiceAccount]. After you
  // re-enable the service account, its existing access tokens will be accepted,
  // and you can request new access tokens.
  //
  // To help avoid unplanned outages, we recommend that you disable the service
  // account before you delete it. Use this method to disable the service
  // account, then wait at least 24 hours and watch for unintended consequences.
  // If there are no unintended consequences, you can delete the service account
  // with [DeleteServiceAccount][google.iam.admin.v1.IAM.DeleteServiceAccount].
  rpc DisableServiceAccount(DisableServiceAccountRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/serviceAccounts/*}:disable"
      body: "*"
    };
  }

  // Lists every [ServiceAccountKey][google.iam.admin.v1.ServiceAccountKey] for a service account.
  rpc ListServiceAccountKeys(ListServiceAccountKeysRequest) returns (ListServiceAccountKeysResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/serviceAccounts/*}/keys"
    };
    option (google.api.method_signature) = "name,key_types";
  }

  // Gets a [ServiceAccountKey][google.iam.admin.v1.ServiceAccountKey].
  rpc GetServiceAccountKey(GetServiceAccountKeyRequest) returns (ServiceAccountKey) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/serviceAccounts/*/keys/*}"
    };
    option (google.api.method_signature) = "name,public_key_type";
  }

  // Creates a [ServiceAccountKey][google.iam.admin.v1.ServiceAccountKey].
  rpc CreateServiceAccountKey(CreateServiceAccountKeyRequest) returns (ServiceAccountKey) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/serviceAccounts/*}/keys"
      body: "*"
    };
    option (google.api.method_signature) = "name,private_key_type,key_algorithm";
  }

  // Uploads the public key portion of a key pair that you manage, and
  // associates the public key with a [ServiceAccount][google.iam.admin.v1.ServiceAccount].
  //
  // After you upload the public key, you can use the private key from the key
  // pair as a service account key.
  rpc UploadServiceAccountKey(UploadServiceAccountKeyRequest) returns (ServiceAccountKey) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/serviceAccounts/*}/keys:upload"
      body: "*"
    };
  }

  // Deletes a [ServiceAccountKey][google.iam.admin.v1.ServiceAccountKey]. Deleting a service account key does not
  // revoke short-lived credentials that have been issued based on the service
  // account key.
  rpc DeleteServiceAccountKey(DeleteServiceAccountKeyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/serviceAccounts/*/keys/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Disable a [ServiceAccountKey][google.iam.admin.v1.ServiceAccountKey]. A disabled service account key can be
  // re-enabled with [EnableServiceAccountKey][google.iam.admin.v1.IAM.EnableServiceAccountKey].
  rpc DisableServiceAccountKey(DisableServiceAccountKeyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/serviceAccounts/*/keys/*}:disable"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Enable a [ServiceAccountKey][google.iam.admin.v1.ServiceAccountKey].
  rpc EnableServiceAccountKey(EnableServiceAccountKeyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/serviceAccounts/*/keys/*}:enable"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // **Note:** This method is deprecated. Use the
  // [`signBlob`](https://cloud.google.com/iam/help/rest-credentials/v1/projects.serviceAccounts/signBlob)
  // method in the IAM Service Account Credentials API instead. If you currently
  // use this method, see the [migration
  // guide](https://cloud.google.com/iam/help/credentials/migrate-api) for
  // instructions.
  //
  // Signs a blob using the system-managed private key for a [ServiceAccount][google.iam.admin.v1.ServiceAccount].
  rpc SignBlob(SignBlobRequest) returns (SignBlobResponse) {
    option deprecated = true;
    option (google.api.http) = {
      post: "/v1/{name=projects/*/serviceAccounts/*}:signBlob"
      body: "*"
    };
    option (google.api.method_signature) = "name,bytes_to_sign";
  }

  // **Note:** This method is deprecated. Use the
  // [`signJwt`](https://cloud.google.com/iam/help/rest-credentials/v1/projects.serviceAccounts/signJwt)
  // method in the IAM Service Account Credentials API instead. If you currently
  // use this method, see the [migration
  // guide](https://cloud.google.com/iam/help/credentials/migrate-api) for
  // instructions.
  //
  // Signs a JSON Web Token (JWT) using the system-managed private key for a
  // [ServiceAccount][google.iam.admin.v1.ServiceAccount].
  rpc SignJwt(SignJwtRequest) returns (SignJwtResponse) {
    option deprecated = true;
    option (google.api.http) = {
      post: "/v1/{name=projects/*/serviceAccounts/*}:signJwt"
      body: "*"
    };
    option (google.api.method_signature) = "name,payload";
  }

  // Gets the IAM policy that is attached to a [ServiceAccount][google.iam.admin.v1.ServiceAccount]. This IAM
  // policy specifies which principals have access to the service account.
  //
  // This method does not tell you whether the service account has been granted
  // any roles on other resources. To check whether a service account has role
  // grants on a resource, use the `getIamPolicy` method for that resource. For
  // example, to view the role grants for a project, call the Resource Manager
  // API's
  // [`projects.getIamPolicy`](https://cloud.google.com/resource-manager/reference/rest/v1/projects/getIamPolicy)
  // method.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest) returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/serviceAccounts/*}:getIamPolicy"
    };
    option (google.api.method_signature) = "resource";
  }

  // Sets the IAM policy that is attached to a [ServiceAccount][google.iam.admin.v1.ServiceAccount].
  //
  // Use this method to grant or revoke access to the service account. For
  // example, you could grant a principal the ability to impersonate the service
  // account.
  //
  // This method does not enable the service account to access other resources.
  // To grant roles to a service account on a resource, follow these steps:
  //
  // 1. Call the resource's `getIamPolicy` method to get its current IAM policy.
  // 2. Edit the policy so that it binds the service account to an IAM role for
  // the resource.
  // 3. Call the resource's `setIamPolicy` method to update its IAM policy.
  //
  // For detailed instructions, see
  // [Manage access to project, folders, and
  // organizations](https://cloud.google.com/iam/help/service-accounts/granting-access-to-service-accounts)
  // or [Manage access to other
  // resources](https://cloud.google.com/iam/help/access/manage-other-resources).
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest) returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/serviceAccounts/*}:setIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource,policy";
  }

  // Tests whether the caller has the specified permissions on a
  // [ServiceAccount][google.iam.admin.v1.ServiceAccount].
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest) returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/serviceAccounts/*}:testIamPermissions"
      body: "*"
    };
    option (google.api.method_signature) = "resource,permissions";
  }

  // Lists roles that can be granted on a Google Cloud resource. A role is
  // grantable if the IAM policy for the resource can contain bindings to the
  // role.
  rpc QueryGrantableRoles(QueryGrantableRolesRequest) returns (QueryGrantableRolesResponse) {
    option (google.api.http) = {
      post: "/v1/roles:queryGrantableRoles"
      body: "*"
    };
    option (google.api.method_signature) = "full_resource_name";
  }

  // Lists every predefined [Role][google.iam.admin.v1.Role] that IAM supports, or every custom role
  // that is defined for an organization or project.
  rpc ListRoles(ListRolesRequest) returns (ListRolesResponse) {
    option (google.api.http) = {
      get: "/v1/roles"
      additional_bindings {
        get: "/v1/{parent=organizations/*}/roles"
      }
      additional_bindings {
        get: "/v1/{parent=projects/*}/roles"
      }
    };
  }

  // Gets the definition of a [Role][google.iam.admin.v1.Role].
  rpc GetRole(GetRoleRequest) returns (Role) {
    option (google.api.http) = {
      get: "/v1/{name=roles/*}"
      additional_bindings {
        get: "/v1/{name=organizations/*/roles/*}"
      }
      additional_bindings {
        get: "/v1/{name=projects/*/roles/*}"
      }
    };
  }

  // Creates a new custom [Role][google.iam.admin.v1.Role].
  rpc CreateRole(CreateRoleRequest) returns (Role) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*}/roles"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=projects/*}/roles"
        body: "*"
      }
    };
  }

  // Updates the definition of a custom [Role][google.iam.admin.v1.Role].
  rpc UpdateRole(UpdateRoleRequest) returns (Role) {
    option (google.api.http) = {
      patch: "/v1/{name=organizations/*/roles/*}"
      body: "role"
      additional_bindings {
        patch: "/v1/{name=projects/*/roles/*}"
        body: "role"
      }
    };
  }

  // Deletes a custom [Role][google.iam.admin.v1.Role].
  //
  // When you delete a custom role, the following changes occur immediately:
  //
  // * You cannot bind a principal to the custom role in an IAM
  // [Policy][google.iam.v1.Policy].
  // * Existing bindings to the custom role are not changed, but they have no
  // effect.
  // * By default, the response from [ListRoles][google.iam.admin.v1.IAM.ListRoles] does not include the custom
  // role.
  //
  // You have 7 days to undelete the custom role. After 7 days, the following
  // changes occur:
  //
  // * The custom role is permanently deleted and cannot be recovered.
  // * If an IAM policy contains a binding to the custom role, the binding is
  // permanently removed.
  rpc DeleteRole(DeleteRoleRequest) returns (Role) {
    option (google.api.http) = {
      delete: "/v1/{name=organizations/*/roles/*}"
      additional_bindings {
        delete: "/v1/{name=projects/*/roles/*}"
      }
    };
  }

  // Undeletes a custom [Role][google.iam.admin.v1.Role].
  rpc UndeleteRole(UndeleteRoleRequest) returns (Role) {
    option (google.api.http) = {
      post: "/v1/{name=organizations/*/roles/*}:undelete"
      body: "*"
      additional_bindings {
        post: "/v1/{name=projects/*/roles/*}:undelete"
        body: "*"
      }
    };
  }

  // Lists every permission that you can test on a resource. A permission is
  // testable if you can check whether a principal has that permission on the
  // resource.
  rpc QueryTestablePermissions(QueryTestablePermissionsRequest) returns (QueryTestablePermissionsResponse) {
    option (google.api.http) = {
      post: "/v1/permissions:queryTestablePermissions"
      body: "*"
    };
  }

  // Returns a list of services that allow you to opt into audit logs that are
  // not generated by default.
  //
  // To learn more about audit logs, see the [Logging
  // documentation](https://cloud.google.com/logging/docs/audit).
  rpc QueryAuditableServices(QueryAuditableServicesRequest) returns (QueryAuditableServicesResponse) {
    option (google.api.http) = {
      post: "/v1/iamPolicies:queryAuditableServices"
      body: "*"
    };
  }

  // Lints, or validates, an IAM policy. Currently checks the
  // [google.iam.v1.Binding.condition][google.iam.v1.Binding.condition] field, which contains a condition
  // expression for a role binding.
  //
  // Successful calls to this method always return an HTTP `200 OK` status code,
  // even if the linter detects an issue in the IAM policy.
  rpc LintPolicy(LintPolicyRequest) returns (LintPolicyResponse) {
    option (google.api.http) = {
      post: "/v1/iamPolicies:lintPolicy"
      body: "*"
    };
  }
}

// An IAM service account.
//
// A service account is an account for an application or a virtual machine (VM)
// instance, not a person. You can use a service account to call Google APIs. To
// learn more, read the [overview of service
// accounts](https://cloud.google.com/iam/help/service-accounts/overview).
//
// When you create a service account, you specify the project ID that owns the
// service account, as well as a name that must be unique within the project.
// IAM uses these values to create an email address that identifies the service
// account.
message ServiceAccount {
  option (google.api.resource) = {
    type: "iam.googleapis.com/ServiceAccount"
    pattern: "projects/{project}/serviceAccounts/{service_account}"
  };

  // The resource name of the service account.
  //
  // Use one of the following formats:
  //
  // * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}`
  // * `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}`
  //
  // As an alternative, you can use the `-` wildcard character instead of the
  // project ID:
  //
  // * `projects/-/serviceAccounts/{EMAIL_ADDRESS}`
  // * `projects/-/serviceAccounts/{UNIQUE_ID}`
  //
  // When possible, avoid using the `-` wildcard character, because it can cause
  // response messages to contain misleading error codes. For example, if you
  // try to get the service account
  // `projects/-/serviceAccounts/<EMAIL>`, which does not exist, the
  // response contains an HTTP `403 Forbidden` error instead of a `404 Not
  // Found` error.
  string name = 1;

  // Output only. The ID of the project that owns the service account.
  string project_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The unique, stable numeric ID for the service account.
  //
  // Each service account retains its unique ID even if you delete the service
  // account. For example, if you delete a service account, then create a new
  // service account with the same name, the new service account has a different
  // unique ID than the deleted service account.
  string unique_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The email address of the service account.
  string email = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A user-specified, human-readable name for the service account. The maximum
  // length is 100 UTF-8 bytes.
  string display_name = 6 [(google.api.field_behavior) = OPTIONAL];

  // Deprecated. Do not use.
  bytes etag = 7 [deprecated = true];

  // Optional. A user-specified, human-readable description of the service account. The
  // maximum length is 256 UTF-8 bytes.
  string description = 8 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The OAuth 2.0 client ID for the service account.
  string oauth2_client_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the service account is disabled.
  bool disabled = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The service account create request.
message CreateServiceAccountRequest {
  // Required. The resource name of the project associated with the service
  // accounts, such as `projects/my-project-123`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The account id that is used to generate the service account
  // email address and a stable unique id. It is unique within a project,
  // must be 6-30 characters long, and match the regular expression
  // `[a-z]([-a-z0-9]*[a-z0-9])` to comply with RFC1035.
  string account_id = 2 [(google.api.field_behavior) = REQUIRED];

  // The [ServiceAccount][google.iam.admin.v1.ServiceAccount] resource to
  // create. Currently, only the following values are user assignable:
  // `display_name` and `description`.
  ServiceAccount service_account = 3;
}

// The service account list request.
message ListServiceAccountsRequest {
  // Required. The resource name of the project associated with the service
  // accounts, such as `projects/my-project-123`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Optional limit on the number of service accounts to include in the
  // response. Further accounts can subsequently be obtained by including the
  // [ListServiceAccountsResponse.next_page_token][google.iam.admin.v1.ListServiceAccountsResponse.next_page_token]
  // in a subsequent request.
  //
  // The default is 20, and the maximum is 100.
  int32 page_size = 2;

  // Optional pagination token returned in an earlier
  // [ListServiceAccountsResponse.next_page_token][google.iam.admin.v1.ListServiceAccountsResponse.next_page_token].
  string page_token = 3;
}

// The service account list response.
message ListServiceAccountsResponse {
  // The list of matching service accounts.
  repeated ServiceAccount accounts = 1;

  // To retrieve the next page of results, set
  // [ListServiceAccountsRequest.page_token][google.iam.admin.v1.ListServiceAccountsRequest.page_token]
  // to this value.
  string next_page_token = 2;
}

// The service account get request.
message GetServiceAccountRequest {
  // Required. The resource name of the service account in the following format:
  // `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`.
  // Using `-` as a wildcard for the `PROJECT_ID` will infer the project from
  // the account. The `ACCOUNT` value can be the `email` address or the
  // `unique_id` of the service account.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];
}

// The service account delete request.
message DeleteServiceAccountRequest {
  // Required. The resource name of the service account in the following format:
  // `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`.
  // Using `-` as a wildcard for the `PROJECT_ID` will infer the project from
  // the account. The `ACCOUNT` value can be the `email` address or the
  // `unique_id` of the service account.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];
}

// The service account patch request.
//
// You can patch only the `display_name` and `description` fields. You must use
// the `update_mask` field to specify which of these fields you want to patch.
//
// Only the fields specified in the request are guaranteed to be returned in
// the response. Other fields may be empty in the response.
message PatchServiceAccountRequest {
  ServiceAccount service_account = 1;

  google.protobuf.FieldMask update_mask = 2;
}

// The service account undelete request.
message UndeleteServiceAccountRequest {
  // The resource name of the service account in the following format:
  // `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT_UNIQUE_ID}`.
  // Using `-` as a wildcard for the `PROJECT_ID` will infer the project from
  // the account.
  string name = 1;
}

message UndeleteServiceAccountResponse {
  // Metadata for the restored service account.
  ServiceAccount restored_account = 1;
}

// The service account enable request.
message EnableServiceAccountRequest {
  // The resource name of the service account in the following format:
  // `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`.
  // Using `-` as a wildcard for the `PROJECT_ID` will infer the project from
  // the account. The `ACCOUNT` value can be the `email` address or the
  // `unique_id` of the service account.
  string name = 1;
}

// The service account disable request.
message DisableServiceAccountRequest {
  // The resource name of the service account in the following format:
  // `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`.
  // Using `-` as a wildcard for the `PROJECT_ID` will infer the project from
  // the account. The `ACCOUNT` value can be the `email` address or the
  // `unique_id` of the service account.
  string name = 1;
}

// The service account keys list request.
message ListServiceAccountKeysRequest {
  // `KeyType` filters to selectively retrieve certain varieties
  // of keys.
  enum KeyType {
    // Unspecified key type. The presence of this in the
    // message will immediately result in an error.
    KEY_TYPE_UNSPECIFIED = 0;

    // User-managed keys (managed and rotated by the user).
    USER_MANAGED = 1;

    // System-managed keys (managed and rotated by Google).
    SYSTEM_MANAGED = 2;
  }

  // Required. The resource name of the service account in the following format:
  // `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`.
  //
  // Using `-` as a wildcard for the `PROJECT_ID`, will infer the project from
  // the account. The `ACCOUNT` value can be the `email` address or the
  // `unique_id` of the service account.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];

  // Filters the types of keys the user wants to include in the list
  // response. Duplicate key types are not allowed. If no key type
  // is provided, all keys are returned.
  repeated KeyType key_types = 2;
}

// The service account keys list response.
message ListServiceAccountKeysResponse {
  // The public keys for the service account.
  repeated ServiceAccountKey keys = 1;
}

// The service account key get by id request.
message GetServiceAccountKeyRequest {
  // Required. The resource name of the service account key in the following format:
  // `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}/keys/{key}`.
  //
  // Using `-` as a wildcard for the `PROJECT_ID` will infer the project from
  // the account. The `ACCOUNT` value can be the `email` address or the
  // `unique_id` of the service account.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/Key"
    }
  ];

  // Optional. The output format of the public key. The default is `TYPE_NONE`, which
  // means that the public key is not returned.
  ServiceAccountPublicKeyType public_key_type = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Supported key algorithms.
enum ServiceAccountKeyAlgorithm {
  // An unspecified key algorithm.
  KEY_ALG_UNSPECIFIED = 0;

  // 1k RSA Key.
  KEY_ALG_RSA_1024 = 1;

  // 2k RSA Key.
  KEY_ALG_RSA_2048 = 2;
}

// Supported private key output formats.
enum ServiceAccountPrivateKeyType {
  // Unspecified. Equivalent to `TYPE_GOOGLE_CREDENTIALS_FILE`.
  TYPE_UNSPECIFIED = 0;

  // PKCS12 format.
  // The password for the PKCS12 file is `notasecret`.
  // For more information, see https://tools.ietf.org/html/rfc7292.
  TYPE_PKCS12_FILE = 1;

  // Google Credentials File format.
  TYPE_GOOGLE_CREDENTIALS_FILE = 2;
}

// Supported public key output formats.
enum ServiceAccountPublicKeyType {
  // Do not return the public key.
  TYPE_NONE = 0;

  // X509 PEM format.
  TYPE_X509_PEM_FILE = 1;

  // Raw public key.
  TYPE_RAW_PUBLIC_KEY = 2;
}

// Service Account Key Origin.
enum ServiceAccountKeyOrigin {
  // Unspecified key origin.
  ORIGIN_UNSPECIFIED = 0;

  // Key is provided by user.
  USER_PROVIDED = 1;

  // Key is provided by Google.
  GOOGLE_PROVIDED = 2;
}

// Represents a service account key.
//
// A service account has two sets of key-pairs: user-managed, and
// system-managed.
//
// User-managed key-pairs can be created and deleted by users.  Users are
// responsible for rotating these keys periodically to ensure security of
// their service accounts.  Users retain the private key of these key-pairs,
// and Google retains ONLY the public key.
//
// System-managed keys are automatically rotated by Google, and are used for
// signing for a maximum of two weeks. The rotation process is probabilistic,
// and usage of the new key will gradually ramp up and down over the key's
// lifetime.
//
// If you cache the public key set for a service account, we recommend that you
// update the cache every 15 minutes. User-managed keys can be added and removed
// at any time, so it is important to update the cache frequently. For
// Google-managed keys, Google will publish a key at least 6 hours before it is
// first used for signing and will keep publishing it for at least 6 hours after
// it was last used for signing.
//
// Public keys for all service accounts are also published at the OAuth2
// Service Account API.
message ServiceAccountKey {
  option (google.api.resource) = {
    type: "iam.googleapis.com/Key"
    pattern: "projects/{project}/serviceAccounts/{service_account}/keys/{key}"
  };

  // The resource name of the service account key in the following format
  // `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}/keys/{key}`.
  string name = 1;

  // The output format for the private key.
  // Only provided in `CreateServiceAccountKey` responses, not
  // in `GetServiceAccountKey` or `ListServiceAccountKey` responses.
  //
  // Google never exposes system-managed private keys, and never retains
  // user-managed private keys.
  ServiceAccountPrivateKeyType private_key_type = 2;

  // Specifies the algorithm (and possibly key size) for the key.
  ServiceAccountKeyAlgorithm key_algorithm = 8;

  // The private key data. Only provided in `CreateServiceAccountKey`
  // responses. Make sure to keep the private key data secure because it
  // allows for the assertion of the service account identity.
  // When base64 decoded, the private key data can be used to authenticate with
  // Google API client libraries and with
  // <a href="/sdk/gcloud/reference/auth/activate-service-account">gcloud
  // auth activate-service-account</a>.
  bytes private_key_data = 3;

  // The public key data. Only provided in `GetServiceAccountKey` responses.
  bytes public_key_data = 7;

  // The key can be used after this timestamp.
  google.protobuf.Timestamp valid_after_time = 4;

  // The key can be used before this timestamp.
  // For system-managed key pairs, this timestamp is the end time for the
  // private key signing operation. The public key could still be used
  // for verification for a few hours after this time.
  google.protobuf.Timestamp valid_before_time = 5;

  // The key origin.
  ServiceAccountKeyOrigin key_origin = 9;

  // The key type.
  ListServiceAccountKeysRequest.KeyType key_type = 10;

  // The key status.
  bool disabled = 11;
}

// The service account key create request.
message CreateServiceAccountKeyRequest {
  // Required. The resource name of the service account in the following format:
  // `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`.
  // Using `-` as a wildcard for the `PROJECT_ID` will infer the project from
  // the account. The `ACCOUNT` value can be the `email` address or the
  // `unique_id` of the service account.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];

  // The output format of the private key. The default value is
  // `TYPE_GOOGLE_CREDENTIALS_FILE`, which is the Google Credentials File
  // format.
  ServiceAccountPrivateKeyType private_key_type = 2;

  // Which type of key and algorithm to use for the key.
  // The default is currently a 2K RSA key.  However this may change in the
  // future.
  ServiceAccountKeyAlgorithm key_algorithm = 3;
}

// The service account key upload request.
message UploadServiceAccountKeyRequest {
  // The resource name of the service account in the following format:
  // `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`.
  // Using `-` as a wildcard for the `PROJECT_ID` will infer the project from
  // the account. The `ACCOUNT` value can be the `email` address or the
  // `unique_id` of the service account.
  string name = 1;

  // The public key to associate with the service account. Must be an RSA public
  // key that is wrapped in an X.509 v3 certificate. Include the first line,
  // `-----BEGIN CERTIFICATE-----`, and the last line,
  // `-----END CERTIFICATE-----`.
  bytes public_key_data = 2;
}

// The service account key delete request.
message DeleteServiceAccountKeyRequest {
  // Required. The resource name of the service account key in the following format:
  // `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}/keys/{key}`.
  // Using `-` as a wildcard for the `PROJECT_ID` will infer the project from
  // the account. The `ACCOUNT` value can be the `email` address or the
  // `unique_id` of the service account.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/Key"
    }
  ];
}

// The service account key disable request.
message DisableServiceAccountKeyRequest {
  // Required. The resource name of the service account key in the following format:
  // `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}/keys/{key}`.
  //
  // Using `-` as a wildcard for the `PROJECT_ID` will infer the project from
  // the account. The `ACCOUNT` value can be the `email` address or the
  // `unique_id` of the service account.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/Key"
    }
  ];
}

// The service account key enable request.
message EnableServiceAccountKeyRequest {
  // Required. The resource name of the service account key in the following format:
  // `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}/keys/{key}`.
  //
  // Using `-` as a wildcard for the `PROJECT_ID` will infer the project from
  // the account. The `ACCOUNT` value can be the `email` address or the
  // `unique_id` of the service account.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/Key"
    }
  ];
}

// Deprecated. [Migrate to Service Account Credentials
// API](https://cloud.google.com/iam/help/credentials/migrate-api).
//
// The service account sign blob request.
message SignBlobRequest {
  // Required. Deprecated. [Migrate to Service Account Credentials
  // API](https://cloud.google.com/iam/help/credentials/migrate-api).
  //
  // The resource name of the service account in the following format:
  // `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`.
  // Using `-` as a wildcard for the `PROJECT_ID` will infer the project from
  // the account. The `ACCOUNT` value can be the `email` address or the
  // `unique_id` of the service account.
  string name = 1 [
    deprecated = true,
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];

  // Required. Deprecated. [Migrate to Service Account Credentials
  // API](https://cloud.google.com/iam/help/credentials/migrate-api).
  //
  // The bytes to sign.
  bytes bytes_to_sign = 2 [
    deprecated = true,
    (google.api.field_behavior) = REQUIRED
  ];
}

// Deprecated. [Migrate to Service Account Credentials
// API](https://cloud.google.com/iam/help/credentials/migrate-api).
//
// The service account sign blob response.
message SignBlobResponse {
  // Deprecated. [Migrate to Service Account Credentials
  // API](https://cloud.google.com/iam/help/credentials/migrate-api).
  //
  // The id of the key used to sign the blob.
  string key_id = 1 [deprecated = true];

  // Deprecated. [Migrate to Service Account Credentials
  // API](https://cloud.google.com/iam/help/credentials/migrate-api).
  //
  // The signed blob.
  bytes signature = 2 [deprecated = true];
}

// Deprecated. [Migrate to Service Account Credentials
// API](https://cloud.google.com/iam/help/credentials/migrate-api).
//
// The service account sign JWT request.
message SignJwtRequest {
  // Required. Deprecated. [Migrate to Service Account Credentials
  // API](https://cloud.google.com/iam/help/credentials/migrate-api).
  //
  // The resource name of the service account in the following format:
  // `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`.
  // Using `-` as a wildcard for the `PROJECT_ID` will infer the project from
  // the account. The `ACCOUNT` value can be the `email` address or the
  // `unique_id` of the service account.
  string name = 1 [
    deprecated = true,
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];

  // Required. Deprecated. [Migrate to Service Account Credentials
  // API](https://cloud.google.com/iam/help/credentials/migrate-api).
  //
  // The JWT payload to sign. Must be a serialized JSON object that contains a
  // JWT Claims Set. For example: `{"sub": "<EMAIL>", "iat": 313435}`
  //
  // If the JWT Claims Set contains an expiration time (`exp`) claim, it must be
  // an integer timestamp that is not in the past and no more than 12 hours in
  // the future.
  //
  // If the JWT Claims Set does not contain an expiration time (`exp`) claim,
  // this claim is added automatically, with a timestamp that is 1 hour in the
  // future.
  string payload = 2 [
    deprecated = true,
    (google.api.field_behavior) = REQUIRED
  ];
}

// Deprecated. [Migrate to Service Account Credentials
// API](https://cloud.google.com/iam/help/credentials/migrate-api).
//
// The service account sign JWT response.
message SignJwtResponse {
  // Deprecated. [Migrate to Service Account Credentials
  // API](https://cloud.google.com/iam/help/credentials/migrate-api).
  //
  // The id of the key used to sign the JWT.
  string key_id = 1 [deprecated = true];

  // Deprecated. [Migrate to Service Account Credentials
  // API](https://cloud.google.com/iam/help/credentials/migrate-api).
  //
  // The signed JWT.
  string signed_jwt = 2 [deprecated = true];
}

// A role in the Identity and Access Management API.
message Role {
  // A stage representing a role's lifecycle phase.
  enum RoleLaunchStage {
    // The user has indicated this role is currently in an Alpha phase. If this
    // launch stage is selected, the `stage` field will not be included when
    // requesting the definition for a given role.
    ALPHA = 0;

    // The user has indicated this role is currently in a Beta phase.
    BETA = 1;

    // The user has indicated this role is generally available.
    GA = 2;

    // The user has indicated this role is being deprecated.
    DEPRECATED = 4;

    // This role is disabled and will not contribute permissions to any
    // principals it is granted to in policies.
    DISABLED = 5;

    // The user has indicated this role is currently in an EAP phase.
    EAP = 6;
  }

  // The name of the role.
  //
  // When Role is used in CreateRole, the role name must not be set.
  //
  // When Role is used in output and other input such as UpdateRole, the role
  // name is the complete path, e.g., roles/logging.viewer for predefined roles
  // and organizations/{ORGANIZATION_ID}/roles/logging.viewer for custom roles.
  string name = 1;

  // Optional. A human-readable title for the role.  Typically this
  // is limited to 100 UTF-8 bytes.
  string title = 2;

  // Optional. A human-readable description for the role.
  string description = 3;

  // The names of the permissions this role grants when bound in an IAM policy.
  repeated string included_permissions = 7;

  // The current launch stage of the role. If the `ALPHA` launch stage has been
  // selected for a role, the `stage` field will not be included in the
  // returned definition for the role.
  RoleLaunchStage stage = 8;

  // Used to perform a consistent read-modify-write.
  bytes etag = 9;

  // The current deleted state of the role. This field is read only.
  // It will be ignored in calls to CreateRole and UpdateRole.
  bool deleted = 11;
}

// The grantable role query request.
message QueryGrantableRolesRequest {
  // Required. The full resource name to query from the list of grantable roles.
  //
  // The name follows the Google Cloud Platform resource format.
  // For example, a Cloud Platform project with id `my-project` will be named
  // `//cloudresourcemanager.googleapis.com/projects/my-project`.
  string full_resource_name = 1 [(google.api.field_behavior) = REQUIRED];

  RoleView view = 2;

  // Optional limit on the number of roles to include in the response.
  //
  // The default is 300, and the maximum is 1,000.
  int32 page_size = 3;

  // Optional pagination token returned in an earlier
  // QueryGrantableRolesResponse.
  string page_token = 4;
}

// The grantable role query response.
message QueryGrantableRolesResponse {
  // The list of matching roles.
  repeated Role roles = 1;

  // To retrieve the next page of results, set
  // `QueryGrantableRolesRequest.page_token` to this value.
  string next_page_token = 2;
}

// A view for Role objects.
enum RoleView {
  // Omits the `included_permissions` field.
  // This is the default value.
  BASIC = 0;

  // Returns all fields.
  FULL = 1;
}

// The request to get all roles defined under a resource.
message ListRolesRequest {
  // The `parent` parameter's value depends on the target resource for the
  // request, namely
  // [`roles`](https://cloud.google.com/iam/reference/rest/v1/roles),
  // [`projects`](https://cloud.google.com/iam/reference/rest/v1/projects.roles),
  // or
  // [`organizations`](https://cloud.google.com/iam/reference/rest/v1/organizations.roles).
  // Each resource type's `parent` value format is described below:
  //
  // * [`roles.list()`](https://cloud.google.com/iam/reference/rest/v1/roles/list): An empty string.
  //   This method doesn't require a resource; it simply returns all
  //   [predefined
  //   roles](https://cloud.google.com/iam/docs/understanding-roles#predefined_roles)
  //   in Cloud IAM. Example request URL: `https://iam.googleapis.com/v1/roles`
  //
  // * [`projects.roles.list()`](https://cloud.google.com/iam/reference/rest/v1/projects.roles/list):
  //   `projects/{PROJECT_ID}`. This method lists all project-level
  //   [custom
  //   roles](https://cloud.google.com/iam/docs/understanding-custom-roles).
  //   Example request URL:
  //   `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles`
  //
  // * [`organizations.roles.list()`](https://cloud.google.com/iam/reference/rest/v1/organizations.roles/list):
  //   `organizations/{ORGANIZATION_ID}`. This method lists all
  //   organization-level [custom
  //   roles](https://cloud.google.com/iam/docs/understanding-custom-roles).
  //   Example request URL:
  //   `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles`
  //
  // Note: Wildcard (*) values are invalid; you must specify a complete project
  // ID or organization ID.
  string parent = 1 [(google.api.resource_reference) = {
                       type: "*"
                     }];

  // Optional limit on the number of roles to include in the response.
  //
  // The default is 300, and the maximum is 1,000.
  int32 page_size = 2;

  // Optional pagination token returned in an earlier ListRolesResponse.
  string page_token = 3;

  // Optional view for the returned Role objects. When `FULL` is specified,
  // the `includedPermissions` field is returned, which includes a list of all
  // permissions in the role. The default value is `BASIC`, which does not
  // return the `includedPermissions` field.
  RoleView view = 4;

  // Include Roles that have been deleted.
  bool show_deleted = 6;
}

// The response containing the roles defined under a resource.
message ListRolesResponse {
  // The Roles defined on this resource.
  repeated Role roles = 1;

  // To retrieve the next page of results, set
  // `ListRolesRequest.page_token` to this value.
  string next_page_token = 2;
}

// The request to get the definition of an existing role.
message GetRoleRequest {
  // The `name` parameter's value depends on the target resource for the
  // request, namely
  // [`roles`](https://cloud.google.com/iam/reference/rest/v1/roles),
  // [`projects`](https://cloud.google.com/iam/reference/rest/v1/projects.roles),
  // or
  // [`organizations`](https://cloud.google.com/iam/reference/rest/v1/organizations.roles).
  // Each resource type's `name` value format is described below:
  //
  // * [`roles.get()`](https://cloud.google.com/iam/reference/rest/v1/roles/get): `roles/{ROLE_NAME}`.
  //   This method returns results from all
  //   [predefined
  //   roles](https://cloud.google.com/iam/docs/understanding-roles#predefined_roles)
  //   in Cloud IAM. Example request URL:
  //   `https://iam.googleapis.com/v1/roles/{ROLE_NAME}`
  //
  // * [`projects.roles.get()`](https://cloud.google.com/iam/reference/rest/v1/projects.roles/get):
  //   `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method returns only
  //   [custom
  //   roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that
  //   have been created at the project level. Example request URL:
  //   `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`
  //
  // * [`organizations.roles.get()`](https://cloud.google.com/iam/reference/rest/v1/organizations.roles/get):
  //   `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method
  //   returns only [custom
  //   roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that
  //   have been created at the organization level. Example request URL:
  //   `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`
  //
  // Note: Wildcard (*) values are invalid; you must specify a complete project
  // ID or organization ID.
  string name = 1 [(google.api.resource_reference) = {
                     type: "*"
                   }];
}

// The request to create a new role.
message CreateRoleRequest {
  // The `parent` parameter's value depends on the target resource for the
  // request, namely
  // [`projects`](https://cloud.google.com/iam/reference/rest/v1/projects.roles)
  // or
  // [`organizations`](https://cloud.google.com/iam/reference/rest/v1/organizations.roles).
  // Each resource type's `parent` value format is described below:
  //
  // * [`projects.roles.create()`](https://cloud.google.com/iam/reference/rest/v1/projects.roles/create):
  //   `projects/{PROJECT_ID}`. This method creates project-level
  //   [custom
  //   roles](https://cloud.google.com/iam/docs/understanding-custom-roles).
  //   Example request URL:
  //   `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles`
  //
  // * [`organizations.roles.create()`](https://cloud.google.com/iam/reference/rest/v1/organizations.roles/create):
  //   `organizations/{ORGANIZATION_ID}`. This method creates organization-level
  //   [custom
  //   roles](https://cloud.google.com/iam/docs/understanding-custom-roles).
  //   Example request URL:
  //   `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles`
  //
  // Note: Wildcard (*) values are invalid; you must specify a complete project
  // ID or organization ID.
  string parent = 1 [(google.api.resource_reference) = {
                       type: "*"
                     }];

  // The role ID to use for this role.
  //
  // A role ID may contain alphanumeric characters, underscores (`_`), and
  // periods (`.`). It must contain a minimum of 3 characters and a maximum of
  // 64 characters.
  string role_id = 2;

  // The Role resource to create.
  Role role = 3;
}

// The request to update a role.
message UpdateRoleRequest {
  // The `name` parameter's value depends on the target resource for the
  // request, namely
  // [`projects`](https://cloud.google.com/iam/reference/rest/v1/projects.roles)
  // or
  // [`organizations`](https://cloud.google.com/iam/reference/rest/v1/organizations.roles).
  // Each resource type's `name` value format is described below:
  //
  // * [`projects.roles.patch()`](https://cloud.google.com/iam/reference/rest/v1/projects.roles/patch):
  //   `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method updates only
  //   [custom
  //   roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that
  //   have been created at the project level. Example request URL:
  //   `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`
  //
  // * [`organizations.roles.patch()`](https://cloud.google.com/iam/reference/rest/v1/organizations.roles/patch):
  //   `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method
  //   updates only [custom
  //   roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that
  //   have been created at the organization level. Example request URL:
  //   `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`
  //
  // Note: Wildcard (*) values are invalid; you must specify a complete project
  // ID or organization ID.
  string name = 1 [(google.api.resource_reference) = {
                     type: "*"
                   }];

  // The updated role.
  Role role = 2;

  // A mask describing which fields in the Role have changed.
  google.protobuf.FieldMask update_mask = 3;
}

// The request to delete an existing role.
message DeleteRoleRequest {
  // The `name` parameter's value depends on the target resource for the
  // request, namely
  // [`projects`](https://cloud.google.com/iam/reference/rest/v1/projects.roles)
  // or
  // [`organizations`](https://cloud.google.com/iam/reference/rest/v1/organizations.roles).
  // Each resource type's `name` value format is described below:
  //
  // * [`projects.roles.delete()`](https://cloud.google.com/iam/reference/rest/v1/projects.roles/delete):
  //   `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method deletes only
  //   [custom
  //   roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that
  //   have been created at the project level. Example request URL:
  //   `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`
  //
  // * [`organizations.roles.delete()`](https://cloud.google.com/iam/reference/rest/v1/organizations.roles/delete):
  //   `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method
  //   deletes only [custom
  //   roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that
  //   have been created at the organization level. Example request URL:
  //   `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`
  //
  // Note: Wildcard (*) values are invalid; you must specify a complete project
  // ID or organization ID.
  string name = 1 [(google.api.resource_reference) = {
                     type: "*"
                   }];

  // Used to perform a consistent read-modify-write.
  bytes etag = 2;
}

// The request to undelete an existing role.
message UndeleteRoleRequest {
  // The `name` parameter's value depends on the target resource for the
  // request, namely
  // [`projects`](https://cloud.google.com/iam/reference/rest/v1/projects.roles)
  // or
  // [`organizations`](https://cloud.google.com/iam/reference/rest/v1/organizations.roles).
  // Each resource type's `name` value format is described below:
  //
  // * [`projects.roles.undelete()`](https://cloud.google.com/iam/reference/rest/v1/projects.roles/undelete):
  //   `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method undeletes
  //   only [custom
  //   roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that
  //   have been created at the project level. Example request URL:
  //   `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`
  //
  // * [`organizations.roles.undelete()`](https://cloud.google.com/iam/reference/rest/v1/organizations.roles/undelete):
  //   `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method
  //   undeletes only [custom
  //   roles](https://cloud.google.com/iam/docs/understanding-custom-roles) that
  //   have been created at the organization level. Example request URL:
  //   `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`
  //
  // Note: Wildcard (*) values are invalid; you must specify a complete project
  // ID or organization ID.
  string name = 1 [(google.api.resource_reference) = {
                     type: "*"
                   }];

  // Used to perform a consistent read-modify-write.
  bytes etag = 2;
}

// A permission which can be included by a role.
message Permission {
  // A stage representing a permission's lifecycle phase.
  enum PermissionLaunchStage {
    // The permission is currently in an alpha phase.
    ALPHA = 0;

    // The permission is currently in a beta phase.
    BETA = 1;

    // The permission is generally available.
    GA = 2;

    // The permission is being deprecated.
    DEPRECATED = 3;
  }

  // The state of the permission with regards to custom roles.
  enum CustomRolesSupportLevel {
    // Default state. Permission is fully supported for custom role use.
    SUPPORTED = 0;

    // Permission is being tested to check custom role compatibility.
    TESTING = 1;

    // Permission is not supported for custom role use.
    NOT_SUPPORTED = 2;
  }

  // The name of this Permission.
  string name = 1;

  // The title of this Permission.
  string title = 2;

  // A brief description of what this Permission is used for.
  // This permission can ONLY be used in predefined roles.
  string description = 3;

  bool only_in_predefined_roles = 4 [deprecated = true];

  // The current launch stage of the permission.
  PermissionLaunchStage stage = 5;

  // The current custom role support level.
  CustomRolesSupportLevel custom_roles_support_level = 6;

  // The service API associated with the permission is not enabled.
  bool api_disabled = 7;

  // The preferred name for this permission. If present, then this permission is
  // an alias of, and equivalent to, the listed primary_permission.
  string primary_permission = 8;
}

// A request to get permissions which can be tested on a resource.
message QueryTestablePermissionsRequest {
  // Required. The full resource name to query from the list of testable
  // permissions.
  //
  // The name follows the Google Cloud Platform resource format.
  // For example, a Cloud Platform project with id `my-project` will be named
  // `//cloudresourcemanager.googleapis.com/projects/my-project`.
  string full_resource_name = 1;

  // Optional limit on the number of permissions to include in the response.
  //
  // The default is 100, and the maximum is 1,000.
  int32 page_size = 2;

  // Optional pagination token returned in an earlier
  // QueryTestablePermissionsRequest.
  string page_token = 3;
}

// The response containing permissions which can be tested on a resource.
message QueryTestablePermissionsResponse {
  // The Permissions testable on the requested resource.
  repeated Permission permissions = 1;

  // To retrieve the next page of results, set
  // `QueryTestableRolesRequest.page_token` to this value.
  string next_page_token = 2;
}

// A request to get the list of auditable services for a resource.
message QueryAuditableServicesRequest {
  // Required. The full resource name to query from the list of auditable
  // services.
  //
  // The name follows the Google Cloud Platform resource format.
  // For example, a Cloud Platform project with id `my-project` will be named
  // `//cloudresourcemanager.googleapis.com/projects/my-project`.
  string full_resource_name = 1;
}

// A response containing a list of auditable services for a resource.
message QueryAuditableServicesResponse {
  // Contains information about an auditable service.
  message AuditableService {
    // Public name of the service.
    // For example, the service name for Cloud IAM is 'iam.googleapis.com'.
    string name = 1;
  }

  // The auditable services for a resource.
  repeated AuditableService services = 1;
}

// The request to lint a Cloud IAM policy object.
message LintPolicyRequest {
  // The full resource name of the policy this lint request is about.
  //
  // The name follows the Google Cloud Platform (GCP) resource format.
  // For example, a GCP project with ID `my-project` will be named
  // `//cloudresourcemanager.googleapis.com/projects/my-project`.
  //
  // The resource name is not used to read the policy instance from the Cloud
  // IAM database. The candidate policy for lint has to be provided in the same
  // request object.
  string full_resource_name = 1;

  // Required. The Cloud IAM object to be linted.
  oneof lint_object {
    // [google.iam.v1.Binding.condition] [google.iam.v1.Binding.condition] object to be linted.
    google.type.Expr condition = 5;
  }
}

// Structured response of a single validation unit.
message LintResult {
  // Possible Level values of a validation unit corresponding to its domain
  // of discourse.
  enum Level {
    // Level is unspecified.
    LEVEL_UNSPECIFIED = 0;

    // A validation unit which operates on an individual condition within a
    // binding.
    CONDITION = 3;
  }

  // Possible Severity values of an issued result.
  enum Severity {
    // Severity is unspecified.
    SEVERITY_UNSPECIFIED = 0;

    // A validation unit returns an error only for critical issues. If an
    // attempt is made to set the problematic policy without rectifying the
    // critical issue, it causes the `setPolicy` operation to fail.
    ERROR = 1;

    // Any issue which is severe enough but does not cause an error.
    // For example, suspicious constructs in the input object will not
    // necessarily fail `setPolicy`, but there is a high likelihood that they
    // won't behave as expected during policy evaluation in `checkPolicy`.
    // This includes the following common scenarios:
    //
    // - Unsatisfiable condition: Expired timestamp in date/time condition.
    // - Ineffective condition: Condition on a <principal, role> pair which is
    //   granted unconditionally in another binding of the same policy.
    WARNING = 2;

    // Reserved for the issues that are not severe as `ERROR`/`WARNING`, but
    // need special handling. For instance, messages about skipped validation
    // units are issued as `NOTICE`.
    NOTICE = 3;

    // Any informative statement which is not severe enough to raise
    // `ERROR`/`WARNING`/`NOTICE`, like auto-correction recommendations on the
    // input content. Note that current version of the linter does not utilize
    // `INFO`.
    INFO = 4;

    // Deprecated severity level.
    DEPRECATED = 5;
  }

  // The validation unit level.
  Level level = 1;

  // The validation unit name, for instance
  // "lintValidationUnits/ConditionComplexityCheck".
  string validation_unit_name = 2;

  // The validation unit severity.
  Severity severity = 3;

  // The name of the field for which this lint result is about.
  //
  // For nested messages `field_name` consists of names of the embedded fields
  // separated by period character. The top-level qualifier is the input object
  // to lint in the request. For example, the `field_name` value
  // `condition.expression` identifies a lint result for the `expression` field
  // of the provided condition.
  string field_name = 5;

  // 0-based character position of problematic construct within the object
  // identified by `field_name`. Currently, this is populated only for condition
  // expression.
  int32 location_offset = 6;

  // Human readable debug message associated with the issue.
  string debug_message = 7;
}

// The response of a lint operation. An empty response indicates
// the operation was able to fully execute and no lint issue was found.
message LintPolicyResponse {
  // List of lint results sorted by `severity` in descending order.
  repeated LintResult lint_results = 1;
}
