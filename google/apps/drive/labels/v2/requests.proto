// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.labels.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/apps/drive/labels/v2/common.proto";
import "google/apps/drive/labels/v2/field.proto";
import "google/apps/drive/labels/v2/label.proto";
import "google/apps/drive/labels/v2/label_lock.proto";
import "google/apps/drive/labels/v2/label_permission.proto";
import "google/protobuf/field_mask.proto";

option go_package = "google.golang.org/genproto/googleapis/apps/drive/labels/v2;labels";
option java_multiple_files = true;
option java_outer_classname = "RequestsProto";
option java_package = "com.google.apps.drive.labels.v2";
option objc_class_prefix = "DLBL";

// Resource view that can be applied to label responses. The default value
// `LABEL_VIEW_BASIC` implies the field mask:
 // `name,id,revision_id,label_type,properties.*`\
enum LabelView {
// Implies the field mask:
// `name,id,revision_id,label_type,properties.*`
LABEL_VIEW_BASIC = 0;

// All possible fields.
LABEL_VIEW_FULL = 1;
}

// Provides control over how write requests are executed. When not specified,
// the last write wins.
message WriteControl {
  // Determines the revision of the label to write to and how the request
  // should behave if that revision is not the current revision of the
  // label.
  oneof control {
    // The [revision_id][google.apps.drive.labels.v1.Label.revision_id] of the
    // label that the write request will be applied to. If this is not the
    // latest revision of the label, the request will not be processed and will
    // return a 400 Bad Request error.
    string required_revision_id = 1;
  }
}

// Request to get the capabilities for a user.
message GetUserCapabilitiesRequest {
  // Required. The resource name of the user. Only "users/me/capabilities" is
  // supported.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "drivelabels.googleapis.com/UserCapabilities"
    }
  ];

  // The customer to scope this request to.
  // For example: "customers/abcd1234".
  // If unset, will return settings within the current customer.
  string customer = 2 [(google.api.resource_reference) = {
    type: "cloudidentity.googleapis.com/Customer"
  }];
}

// Request to create a Label.
message CreateLabelRequest {
  // Required. The label to create.
  Label label = 1 [(google.api.field_behavior) = REQUIRED];

  // Set to `true` in order to use the user's admin privileges. The server
  // will verify the user is an admin before allowing access.
  bool use_admin_access = 2;

  // The BCP-47 language code to use for evaluating localized Field labels in
  // response. When not specified, values in the default configured language
  // will be used.
  string language_code = 3;
}

// Request to get a label by resource name.
message GetLabelRequest {
  // Required. Label resource name.
  //
  // May be any of:
  //
  // * `labels/{id}` (equivalent to labels/{id}@latest)
  // * `labels/{id}@latest`
  // * `labels/{id}@published`
  // * `labels/{id}@{revision_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "drivelabels.googleapis.com/Label"
    }
  ];

  // Set to `true` in order to use the user's admin credentials. The server
  // verifies that the user is an admin for the label before allowing access.
  bool use_admin_access = 2;

  // The BCP-47 language code to use for evaluating localized field labels.
  // When not specified, values in the default configured language are used.
  string language_code = 3;

  // When specified, only certain fields belonging to the indicated view are
  // returned.
  LabelView view = 4;
}

// The set of requests for updating aspects of a Label. If any request is not
// valid, no requests will be applied.
message DeltaUpdateLabelRequest {
  // A single kind of update to apply to a Label.
  message Request {
    // The kind of update. Exactly one Field is required.
    oneof kind {
      // Updates the Label properties.
      UpdateLabelPropertiesRequest update_label = 1;

      // Creates a new Field.
      CreateFieldRequest create_field = 2;

      // Updates basic properties of a Field.
      UpdateFieldPropertiesRequest update_field = 3;

      // Update Field type and/or type options.
      UpdateFieldTypeRequest update_field_type = 4;

      // Enables the Field.
      EnableFieldRequest enable_field = 5;

      // Disables the Field.
      DisableFieldRequest disable_field = 6;

      // Deletes a Field from the label.
      DeleteFieldRequest delete_field = 7;

      // Creates Choice within a Selection field.
      CreateSelectionChoiceRequest create_selection_choice = 8;

      // Update a Choice properties within a Selection Field.
      UpdateSelectionChoicePropertiesRequest
          update_selection_choice_properties = 9;

      // Enable a Choice within a Selection Field.
      EnableSelectionChoiceRequest enable_selection_choice = 10;

      // Disable a Choice within a Selection Field.
      DisableSelectionChoiceRequest disable_selection_choice = 11;

      // Delete a Choice within a Selection Field.
      DeleteSelectionChoiceRequest delete_selection_choice = 12;
    }
  }

  // Updates basic properties of a Label.
  message UpdateLabelPropertiesRequest {
    // The fields that should be updated. At least one field must be specified.
    // The root `label_properties` is implied and should not be specified. A
    // single `*` can be used as short-hand for updating every field.
    google.protobuf.FieldMask update_mask = 1;

    // Required. Label properties to update.
    Label.Properties properties = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Request to disable the Field.
  message DisableFieldRequest {
    // The fields that should be updated. At least one field must be specified.
    // The root `disabled_policy` is implied and should not be specified. A
    // single `*` can be used as short-hand for updating every field.
    google.protobuf.FieldMask update_mask = 1;

    // Required. Key of the Field to disable.
    string id = 2 [(google.api.field_behavior) = REQUIRED];

    // Required. Field Disabled Policy.
    Lifecycle.DisabledPolicy disabled_policy = 3
        [(google.api.field_behavior) = REQUIRED];
  }

  // Request to enable the Field.
  message EnableFieldRequest {
    // Required. ID of the Field to enable.
    string id = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Request to delete the Field.
  message DeleteFieldRequest {
    // Required. ID of the Field to delete.
    string id = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Request to create a Field within a Label.
  message CreateFieldRequest {
    // Required. Field to create.
    Field field = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Request to update Field properties.
  message UpdateFieldPropertiesRequest {
    // The fields that should be updated. At least one field must be specified.
    // The root `properties` is implied and should not be specified. A single
    // `*` can be used as short-hand for updating every field.
    google.protobuf.FieldMask update_mask = 1;

    // Required. The Field to update.
    string id = 2 [(google.api.field_behavior) = REQUIRED];

    // Required. Basic Field properties.
    Field.Properties properties = 3 [(google.api.field_behavior) = REQUIRED];
  }

  // Request to change the type of a Field.
  message UpdateFieldTypeRequest {
    oneof type_options {
      // Update field to Text.
      Field.TextOptions text_options = 3;

      // Update field to Integer.
      Field.IntegerOptions integer_options = 5;

      // Update field to Date.
      Field.DateOptions date_options = 6;

      // Update field to Selection.
      Field.SelectionOptions selection_options = 7;

      // Update field to User.
      Field.UserOptions user_options = 8;
    }

    // The fields that should be updated. At least one field must be specified.
    // The root of `type_options` is implied and should not be specified. A
    // single `*` can be used as short-hand for updating every field.
    google.protobuf.FieldMask update_mask = 1;

    // Required. The Field to update.
    string id = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Request to create a Selection Choice.
  message CreateSelectionChoiceRequest {
    // Required. The Selection Field in which a Choice will be created.
    string field_id = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The Choice to create.
    Field.SelectionOptions.Choice choice = 2
        [(google.api.field_behavior) = REQUIRED];
  }

  // Request to update a Choice properties.
  message UpdateSelectionChoicePropertiesRequest {
    // The fields that should be updated. At least one field must be specified.
    // The root `properties` is implied and should not be specified. A single
    // `*` can be used as short-hand for updating every field.
    google.protobuf.FieldMask update_mask = 1;

    // Required. The Selection Field to update.
    string field_id = 2 [(google.api.field_behavior) = REQUIRED];

    // Required. The Choice to update.
    string id = 3 [(google.api.field_behavior) = REQUIRED];

    // Required. The Choice properties to update.
    Field.SelectionOptions.Choice.Properties properties = 4
        [(google.api.field_behavior) = REQUIRED];
  }

  // Request to delete a Choice.
  message DeleteSelectionChoiceRequest {
    // Required. The Selection Field from which a Choice will be deleted.
    string field_id = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Choice to delete.
    string id = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Request to disable a Choice.
  message DisableSelectionChoiceRequest {
    // The fields that should be updated. At least one field must be specified.
    // The root `disabled_policy` is implied and should not be specified. A
    // single `*` can be used as short-hand for updating every field.
    google.protobuf.FieldMask update_mask = 1;

    // Required. The Selection Field in which a Choice will be disabled.
    string field_id = 2 [(google.api.field_behavior) = REQUIRED];

    // Required. Choice to disable.
    string id = 3 [(google.api.field_behavior) = REQUIRED];

    // Required. The disabled policy to update.
    Lifecycle.DisabledPolicy disabled_policy = 4
        [(google.api.field_behavior) = REQUIRED];
  }

  // Request to enable a Choice.
  message EnableSelectionChoiceRequest {
    // Required. The Selection Field in which a Choice will be enabled.
    string field_id = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Choice to enable.
    string id = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Required. The resource name of the Label to update.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "drivelabels.googleapis.com/Label"
    }
  ];

  // Provides control over how write requests are executed.
  WriteControl write_control = 2;

  // A list of updates to apply to the Label.
  // Requests will be applied in the order they are specified.
  repeated Request requests = 3;

  // Set to `true` in order to use the user's admin credentials. The server
  // will verify the user is an admin for the Label before allowing access.
  bool use_admin_access = 4;

  // When specified, only certain fields belonging to the indicated view will be
  // returned.
  LabelView view = 5;

  // The BCP-47 language code to use for evaluating localized Field labels when
  // `include_label_in_response` is `true`.
  string language_code = 6;
}

// Response for Label update.
message DeltaUpdateLabelResponse {
  // A single response from an update.
  message Response {
    // The response for the corresponding request.
    oneof response {
      // Updated basic properties of a Label.
      UpdateLabelPropertiesResponse update_label = 1;

      // Creates a new Field.
      CreateFieldResponse create_field = 2;

      // Updates basic properties of a Field.
      UpdateFieldPropertiesResponse update_field = 3;

      // Update Field type and/or type options.
      UpdateFieldTypeResponse update_field_type = 4;

      // Enables Field.
      EnableFieldResponse enable_field = 5;

      // Disables Field.
      DisableFieldResponse disable_field = 6;

      // Deletes a Field from the label.
      DeleteFieldResponse delete_field = 7;

      // Creates a new selection list option to add to a Selection Field.
      CreateSelectionChoiceResponse create_selection_choice = 8;

      // Updates a Choice within a Selection Field.
      UpdateSelectionChoicePropertiesResponse
          update_selection_choice_properties = 9;

      // Enables a Choice within a Selection Field.
      EnableSelectionChoiceResponse enable_selection_choice = 10;

      // Disables a Choice within a Selection Field.
      DisableSelectionChoiceResponse disable_selection_choice = 11;

      // Deletes a Choice from a Selection Field.
      DeleteSelectionChoiceResponse delete_selection_choice = 12;
    }
  }

  // Response following update to Label properties.
  message UpdateLabelPropertiesResponse {}

  // Response following Field create.
  message CreateFieldResponse {
    // The field of the created field. When left blank in a create request,
    // a key will be autogenerated and can be identified here.
    string id = 1;

    // The priority of the created field. The priority may change from what
    // was specified to assure contiguous priorities between fields (1-n).
    int32 priority = 2;
  }

  // Response following update to Field properties.
  message UpdateFieldPropertiesResponse {
    // The priority of the updated field. The priority may change from what
    // was specified to assure contiguous priorities between fields (1-n).
    int32 priority = 1;
  }

  // Response following update to Field type.
  message UpdateFieldTypeResponse {}

  // Response following Field enable.
  message EnableFieldResponse {}

  // Response following Field disable.
  message DisableFieldResponse {}

  // Response following Field delete.
  message DeleteFieldResponse {}

  // Response following Selection Choice create.
  message CreateSelectionChoiceResponse {
    // The server-generated id of the field.
    string field_id = 1;

    // The server-generated ID of the created choice within the Field
    string id = 2;
  }

  // Response following update to Selection Choice properties.
  message UpdateSelectionChoicePropertiesResponse {
    // The priority of the updated choice. The priority may change from what
    // was specified to assure contiguous priorities between choices (1-n).
    int32 priority = 1;
  }

  // Response following Choice enable.
  message EnableSelectionChoiceResponse {}

  // Response following Choice disable.
  message DisableSelectionChoiceResponse {}

  // Response following Choice delete.
  message DeleteSelectionChoiceResponse {}

  // The reply of the updates. This maps 1:1 with the updates, although
  // responses to some requests may be empty.
  repeated Response responses = 1;

  // The label after updates were applied. This is only set if
  // [BatchUpdateLabelResponse2.include_label_in_response] is `true` and there
  // were no errors.
  Label updated_label = 6;
}

// Request to update the `CopyMode` of the given Label. Changes to this policy
// are not revisioned, do not require publishing, and take effect immediately.
 // \
message UpdateLabelCopyModeRequest {
// Required. The resource name of the Label to update.
string name = 1 [
  (google.api.field_behavior) = REQUIRED,
  (google.api.resource_reference) = { type: "drivelabels.googleapis.com/Label" }
];

// Required. Indicates how the applied Label, and Field values should be copied
// when a Drive item is copied.
Label.AppliedLabelPolicy.CopyMode copy_mode = 2
    [(google.api.field_behavior) = REQUIRED];

// Set to `true` in order to use the user's admin credentials. The server
// will verify the user is an admin for the Label before allowing access.
bool use_admin_access = 3;

// The BCP-47 language code to use for evaluating localized field labels.
// When not specified, values in the default configured language will be used.
string language_code = 4;

// When specified, only certain fields belonging to the indicated view will be
// returned.
LabelView view = 5;
}

// Request to get the limits for a Label.
message GetLabelLimitsRequest {
  // Required. Label revision resource name
  // Must be: "limits/label"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "drivelabels.googleapis.com/Label"
    }
  ];
}

// Request to list labels available to the current user.
message ListLabelsRequest {
  oneof access {
    // Set to `true` in order to use the user's admin credentials. This will
    // return all Labels within the customer.
    bool use_admin_access = 3;

    // Specifies the level of access the user must have on the returned Labels.
    // The minimum role a user must have on a label.
    // Defaults to `READER`.
    LabelPermission.LabelRole minimum_role = 4;
  }

  // Whether to include only published labels in the results.
  //
  // * When `true`, only the current published label revisions are returned.
  //   Disabled labels are included. Returned label resource names
  //   reference the published revision (`labels/{id}/{revision_id}`).
  // * When `false`, the current label revisions are returned, which might not
  //   be published. Returned label resource names don't reference a specific
  //   revision (`labels/{id}`).
  bool published_only = 1;

  // The customer to scope this list request to.
  // For example: "customers/abcd1234".
  // If unset, will return all labels within the current customer.
  string customer = 2 [(google.api.resource_reference) = {
    type: "cloudidentity.googleapis.com/Customer"
  }];

  // The BCP-47 language code to use for evaluating localized field labels.
  // When not specified, values in the default configured language are used.
  string language_code = 5;

  // Maximum number of labels to return per page. Default: 50. Max: 200.
  int32 page_size = 6;

  // The token of the page to return.
  string page_token = 7;

  // When specified, only certain fields belonging to the indicated view are
  // returned.
  LabelView view = 8;
}

// Response for listing Labels.
message ListLabelsResponse {
  // Labels.
  repeated Label labels = 1;

  // The token of the next page in the response.
  string next_page_token = 2;
}

// Creates or updates a permission on the Label. Permissions affect the Label
// resource as a whole, are not revisioned, and do not require publishing.
message CreateLabelPermissionRequest {
  // Required. The parent Label resource name on the Label Permission is
  // created. Format: labels/{label}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "drivelabels.googleapis.com/Label"
    }
  ];

  // Required. The permission to create or update on the Label.
  LabelPermission label_permission = 2 [(google.api.field_behavior) = REQUIRED];

  // Set to `true` in order to use the user's admin credentials. The server
  // will verify the user is an admin for the Label before allowing access.
  bool use_admin_access = 3;
}

// Request to list the permissions on a Label.
message ListLabelPermissionsRequest {
  // Required. The parent Label resource name on which Label Permission are
  // listed. Format: labels/{label}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "drivelabels.googleapis.com/Label"
    }
  ];

  // Set to `true` in order to use the user's admin credentials. The server will
  // verify the user is an admin for the Label before allowing access.
  bool use_admin_access = 2;

  // Maximum number of permissions to return per page. Default: 50. Max: 200.
  int32 page_size = 3;

  // The token of the page to return.
  string page_token = 4;
}

// Response for listing the permissions on a Label.
message ListLabelPermissionsResponse {
  // Label permissions.
  repeated LabelPermission label_permissions = 1;

  // The token of the next page in the response.
  string next_page_token = 2;
}

// Updates a Label Permission. Permissions affect the Label resource as a whole,
// are not revisioned, and do not require publishing.
message UpdateLabelPermissionRequest {
  // Required. The parent Label resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "drivelabels.googleapis.com/Label"
    }
  ];

  // Required. The permission to create or update on the Label.
  LabelPermission label_permission = 2 [(google.api.field_behavior) = REQUIRED];

  // Set to `true` in order to use the user's admin credentials. The server
  // will verify the user is an admin for the Label before allowing access.
  bool use_admin_access = 3;
}

// Deletes a Label Permission. Permissions affect the Label resource as a whole,
// are not revisioned, and do not require publishing.
message DeleteLabelPermissionRequest {
  // Required. Label Permission resource name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "drivelabels.googleapis.com/LabelPermission"
    }
  ];

  // Set to `true` in order to use the user's admin credentials. The server
  // will verify the user is an admin for the Label before allowing access.
  bool use_admin_access = 2;
}

// Updates one or more Label Permissions.
message BatchUpdateLabelPermissionsRequest {
  // Required. The parent Label resource name shared by all permissions being
  // updated. Format: labels/{label} If this is set, the parent field in the
  // UpdateLabelPermissionRequest messages must either be empty or match this
  // field.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "drivelabels.googleapis.com/Label"
    }
  ];

  // Required. The request message specifying the resources to update.
  repeated UpdateLabelPermissionRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];

  // Set to `true` in order to use the user's admin credentials. The server
  // will verify the user is an admin for the Label before allowing access.
  // If this is set, the use_admin_access field in the
  // UpdateLabelPermissionRequest messages must either be empty or match this
  // field.
  bool use_admin_access = 3;
}

// Response for updating one or more Label Permissions.
message BatchUpdateLabelPermissionsResponse {
  // Required. Permissions updated.
  repeated LabelPermission permissions = 1
      [(google.api.field_behavior) = REQUIRED];
}

// Deletes one of more Label Permissions.
message BatchDeleteLabelPermissionsRequest {
  // Required. The request message specifying the resources to update.
  repeated DeleteLabelPermissionRequest requests = 1
      [(google.api.field_behavior) = REQUIRED];

  // Set to `true` in order to use the user's admin credentials. The server
  // will verify the user is an admin for the Label before allowing access.
  // If this is set, the use_admin_access field in the
  // DeleteLabelPermissionRequest messages must either be empty or match this
  // field.
  bool use_admin_access = 2;

  // Required. The parent Label resource name shared by all permissions being
  // deleted. Format: labels/{label} If this is set, the parent field in the
  // UpdateLabelPermissionRequest messages must either be empty or match this
  // field.
  string parent = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "drivelabels.googleapis.com/Label"
    }
  ];
}

// Request to deprecate a published Label.
message DisableLabelRequest {
  // The fields that should be updated. At least one field must be specified.
  // The root `disabled_policy` is implied and should not be specified. A
  // single `*` can be used as short-hand for updating every field.
  google.protobuf.FieldMask update_mask = 1;

  // Required. Label resource name.
  string name = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "drivelabels.googleapis.com/Label"
    }
  ];

  // Set to `true` in order to use the user's admin credentials. The server
  // will verify the user is an admin for the Label before allowing access.
  bool use_admin_access = 3;

  // Provides control over how write requests are executed. Defaults to unset,
  // which means last write wins.
  WriteControl write_control = 4;

  // Disabled policy to use.
  Lifecycle.DisabledPolicy disabled_policy = 5;

  // The BCP-47 language code to use for evaluating localized field labels.
  // When not specified, values in the default configured language will be used.
  string language_code = 6;
}

// Request to publish a label.
message PublishLabelRequest {
  // Required. Label resource name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "drivelabels.googleapis.com/Label"
    }
  ];

  // Set to `true` in order to use the user's admin credentials. The server
  // will verify the user is an admin for the Label before allowing access.
  bool use_admin_access = 2;

  // Provides control over how write requests are executed. Defaults to unset,
  // which means last write wins.
  WriteControl write_control = 3;

  // The BCP-47 language code to use for evaluating localized field labels.
  // When not specified, values in the default configured language will be used.
  string language_code = 4;
}

// Request to enable a label.
message EnableLabelRequest {
  // Required. Label resource name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "drivelabels.googleapis.com/Label"
    }
  ];

  // Set to `true` in order to use the user's admin credentials. The server
  // will verify the user is an admin for the Label before allowing access.
  bool use_admin_access = 2;

  // Provides control over how write requests are executed. Defaults to unset,
  // which means last write wins.
  WriteControl write_control = 3;

  // The BCP-47 language code to use for evaluating localized field labels.
  // When not specified, values in the default configured language will be used.
  string language_code = 4;
}

// Request to delete a label.
message DeleteLabelRequest {
  // Required. Label resource name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "drivelabels.googleapis.com/Label"
    }
  ];

  // Set to `true` in order to use the user's admin credentials. The server
  // will verify the user is an admin for the Label before allowing access.
  bool use_admin_access = 2;

  // Provides control over how write requests are executed. Defaults to unset,
  // which means last write wins.
  WriteControl write_control = 3;
}

// A request to list the LabelLocks on a Label.
message ListLabelLocksRequest {
  // Required. Label on which Locks are applied.
  // Format: labels/{label}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "drivelabels.googleapis.com/Label"
    }
  ];

  // Maximum number of Locks to return per page. Default: 100. Max: 200.
  int32 page_size = 2;

  // The token of the page to return.
  string page_token = 3;
}

// The response to a ListLabelLocksRequest.
message ListLabelLocksResponse {
  // LabelLocks.
  repeated LabelLock label_locks = 1;

  // The token of the next page in the response.
  string next_page_token = 2;
}
