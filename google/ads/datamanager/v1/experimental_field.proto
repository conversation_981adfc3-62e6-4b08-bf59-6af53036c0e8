// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.datamanager.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Ads.DataManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/datamanager/v1;datamanager";
option java_multiple_files = true;
option java_outer_classname = "ExperimentalFieldProto";
option java_package = "com.google.ads.datamanager.v1";
option php_namespace = "Google\\Ads\\DataManager\\V1";
option ruby_package = "Google::Ads::DataManager::V1";

// Experimental field representing unofficial fields.
message ExperimentalField {
  // Optional. The name of the field to use.
  string field = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value the field to set.
  string value = 2 [(google.api.field_behavior) = OPTIONAL];
}
