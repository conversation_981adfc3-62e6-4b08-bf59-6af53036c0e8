// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.datamanager.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Ads.DataManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/datamanager/v1;datamanager";
option java_multiple_files = true;
option java_outer_classname = "UserDataProto";
option java_package = "com.google.ads.datamanager.v1";
option php_namespace = "Google\\Ads\\DataManager\\V1";
option ruby_package = "Google::Ads::DataManager::V1";

// Data that identifies the user. At least one identifier is required.
message UserData {
  // Required. The identifiers for the user. It's possible to provide multiple
  // instances of the same type of data (for example, multiple email addresses).
  // To increase the likelihood of a match, provide as many identifiers as
  // possible. At most 10 `userIdentifiers` can be provided in a single
  // [AudienceMember][google.ads.datamanager.v1.AudienceMember] or
  // [Event][google.ads.datamanager.v1.Event].
  repeated UserIdentifier user_identifiers = 1
      [(google.api.field_behavior) = REQUIRED];
}

// A single identifier for the user.
message UserIdentifier {
  // Exactly one must be specified.
  oneof identifier {
    // Hashed email address using SHA-256 hash function after normalization.
    string email_address = 1;

    // Hashed phone number using SHA-256 hash function after normalization (E164
    // standard).
    string phone_number = 2;

    // The known components of a user's address. Holds a grouping
    // of identifiers that are matched all at once.
    AddressInfo address = 3;
  }
}

// Address information for the user.
message AddressInfo {
  // Required. Given (first) name of the user, all lowercase, with no
  // punctuation, no leading or trailing whitespace, and hashed as SHA-256.
  string given_name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Family (last) name of the user, all lowercase, with no
  // punctuation, no leading or trailing whitespace, and hashed as SHA-256.
  string family_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The 2-letter region code in ISO-3166-1 alpha-2 of the user's
  // address.
  string region_code = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The postal code of the user's address.
  string postal_code = 4 [(google.api.field_behavior) = REQUIRED];
}
