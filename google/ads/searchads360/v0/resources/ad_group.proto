// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/common/targeting_setting.proto";
import "google/ads/searchads360/v0/enums/ad_group_ad_rotation_mode.proto";
import "google/ads/searchads360/v0/enums/ad_group_engine_status.proto";
import "google/ads/searchads360/v0/enums/ad_group_status.proto";
import "google/ads/searchads360/v0/enums/ad_group_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdGroupProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the ad group resource.

// An ad group.
message AdGroup {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/AdGroup"
    pattern: "customers/{customer_id}/adGroups/{ad_group_id}"
  };

  // Immutable. The resource name of the ad group.
  // Ad group resource names have the form:
  //
  // `customers/{customer_id}/adGroups/{ad_group_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AdGroup"
    }
  ];

  // Output only. The ID of the ad group.
  optional int64 id = 34 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The name of the ad group.
  //
  // This field is required and should not be empty when creating new ad
  // groups.
  //
  // It must contain fewer than 255 UTF-8 full-width characters.
  //
  // It must not contain any null (code point 0x0), NL line feed
  // (code point 0xA) or carriage return (code point 0xD) characters.
  optional string name = 35;

  // The status of the ad group.
  google.ads.searchads360.v0.enums.AdGroupStatusEnum.AdGroupStatus status = 5;

  // Immutable. The type of the ad group.
  google.ads.searchads360.v0.enums.AdGroupTypeEnum.AdGroupType type = 12
      [(google.api.field_behavior) = IMMUTABLE];

  // The ad rotation mode of the ad group.
  google.ads.searchads360.v0.enums.AdGroupAdRotationModeEnum
      .AdGroupAdRotationMode ad_rotation_mode = 22;

  // The maximum CPC (cost-per-click) bid.
  optional int64 cpc_bid_micros = 39;

  // Output only. The timestamp when this ad_group was created. The timestamp is
  // in the customer's time zone and in "yyyy-MM-dd HH:mm:ss" format.
  string creation_time = 60 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Engine Status for ad group.
  optional google.ads.searchads360.v0.enums.AdGroupEngineStatusEnum
      .AdGroupEngineStatus engine_status = 61
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Setting for targeting related features.
  google.ads.searchads360.v0.common.TargetingSetting targeting_setting = 25;

  // Output only. The resource names of labels attached to this ad group.
  repeated string labels = 49 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AdGroupLabel"
    }
  ];

  // Output only. The resource names of effective labels attached to this ad
  // group. An effective label is a label inherited or directly assigned to this
  // ad group.
  repeated string effective_labels = 66 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AdGroupEffectiveLabel"
    }
  ];

  // Output only. ID of the ad group in the external engine account. This field
  // is for non-Google Ads account only, for example, Yahoo Japan, Microsoft,
  // Baidu etc. For Google Ads entity, use "ad_group.id" instead.
  string engine_id = 50 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Date when this ad group starts serving ads. By default, the ad
  // group starts now or the ad group's start date, whichever is later. If this
  // field is set, then the ad group starts at the beginning of the specified
  // date in the customer's time zone. This field is only available for
  // Microsoft Advertising and Facebook gateway accounts.
  //
  // Format: YYYY-MM-DD
  // Example: 2019-03-14
  string start_date = 51 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Date when the ad group ends serving ads. By default, the ad
  // group ends on the ad group's end date. If this field is set, then the ad
  // group ends at the end of the specified date in the customer's time zone.
  // This field is only available for Microsoft Advertising and Facebook gateway
  // accounts.
  //
  // Format: YYYY-MM-DD
  // Example: 2019-03-14
  string end_date = 52 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The language of the ads and keywords in an ad group. This
  // field is only available for Microsoft Advertising accounts. More details:
  // https://docs.microsoft.com/en-us/advertising/guides/ad-languages?view=bingads-13#adlanguage
  string language_code = 53 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The datetime when this ad group was last modified. The
  // datetime is in the customer's time zone and in "yyyy-MM-dd HH:mm:ss.ssssss"
  // format.
  string last_modified_time = 55 [(google.api.field_behavior) = OUTPUT_ONLY];
}
