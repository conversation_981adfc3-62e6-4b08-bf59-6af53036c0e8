// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/common/criteria.proto";
import "google/ads/searchads360/v0/enums/campaign_criterion_status.proto";
import "google/ads/searchads360/v0/enums/criterion_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignCriterionProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the Campaign Criterion resource.

// A campaign criterion.
message CampaignCriterion {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/CampaignCriterion"
    pattern: "customers/{customer_id}/campaignCriteria/{campaign_id}~{criterion_id}"
  };

  // Immutable. The resource name of the campaign criterion.
  // Campaign criterion resource names have the form:
  //
  // `customers/{customer_id}/campaignCriteria/{campaign_id}~{criterion_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/CampaignCriterion"
    }
  ];

  // Output only. The ID of the criterion.
  //
  // This field is ignored during mutate.
  optional int64 criterion_id = 38 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The display name of the criterion.
  //
  // This field is ignored for mutates.
  string display_name = 43 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The modifier for the bids when the criterion matches. The modifier must be
  // in the range: 0.1 - 10.0. Most targetable criteria types support modifiers.
  // Use 0 to opt out of a Device type.
  optional float bid_modifier = 39;

  // Immutable. Whether to target (`false`) or exclude (`true`) the criterion.
  optional bool negative = 40 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The type of the criterion.
  google.ads.searchads360.v0.enums.CriterionTypeEnum.CriterionType type = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The status of the criterion.
  google.ads.searchads360.v0.enums.CampaignCriterionStatusEnum
      .CampaignCriterionStatus status = 35;

  // Output only. The datetime when this campaign criterion was last modified.
  // The datetime is in the customer's time zone and in "yyyy-MM-dd
  // HH:mm:ss.ssssss" format.
  string last_modified_time = 44 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The campaign criterion.
  //
  // Exactly one must be set.
  oneof criterion {
    // Immutable. Keyword.
    google.ads.searchads360.v0.common.KeywordInfo keyword = 8
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Location.
    google.ads.searchads360.v0.common.LocationInfo location = 12
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Device.
    google.ads.searchads360.v0.common.DeviceInfo device = 13
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Age range.
    google.ads.searchads360.v0.common.AgeRangeInfo age_range = 16
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Gender.
    google.ads.searchads360.v0.common.GenderInfo gender = 17
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. User List.
    google.ads.searchads360.v0.common.UserListInfo user_list = 22
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Language.
    google.ads.searchads360.v0.common.LanguageInfo language = 26
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Webpage.
    google.ads.searchads360.v0.common.WebpageInfo webpage = 31
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Location Group
    google.ads.searchads360.v0.common.LocationGroupInfo location_group = 34
        [(google.api.field_behavior) = IMMUTABLE];
  }
}
