// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/enums/customer_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomerClientProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the CustomerClient resource.

// A link between the given customer and a client customer. CustomerClients only
// exist for manager customers. All direct and indirect client customers are
// included, as well as the manager itself.
message CustomerClient {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/CustomerClient"
    pattern: "customers/{customer_id}/customerClients/{client_external_customer_id}"
  };

  // Output only. The resource name of the customer client.
  // CustomerClient resource names have the form:
  // `customers/{customer_id}/customerClients/{client_customer_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/CustomerClient"
    }
  ];

  // Output only. The resource name of the client-customer which is linked to
  // the given customer. Read only.
  optional string client_customer = 12 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/Customer"
    }
  ];

  // Output only. Specifies whether this is a hidden account.
  //
  // Read only.
  optional bool hidden = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Distance between given customer and client. For self link, the
  // level value will be 0. Read only.
  optional int64 level = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Common Locale Data Repository (CLDR) string representation of
  // the time zone of the client, for example, America/Los_Angeles. Read only.
  optional string time_zone = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies if the client is a test account. Read only.
  optional bool test_account = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies if the client is a manager. Read only.
  optional bool manager = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Descriptive name for the client. Read only.
  optional string descriptive_name = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Currency code (for example, 'USD', 'EUR') for the client. Read
  // only.
  optional string currency_code = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of the client customer. Read only.
  optional int64 id = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource names of the labels owned by the requesting
  // customer that are applied to the client customer. Label resource names have
  // the form:
  //
  // `customers/{customer_id}/labels/{label_id}`
  repeated string applied_labels = 21 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/Label"
    }
  ];

  // Output only. The status of the client customer. Read only.
  google.ads.searchads360.v0.enums.CustomerStatusEnum.CustomerStatus status = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
