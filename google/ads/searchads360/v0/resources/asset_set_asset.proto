// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/enums/asset_set_asset_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AssetSetAssetProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// AssetSetAsset is the link between an asset and an asset set.
// Adding an AssetSetAsset links an asset with an asset set.
message AssetSetAsset {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/AssetSetAsset"
    pattern: "customers/{customer_id}/assetSetAssets/{asset_set_id}~{asset_id}"
  };

  // Immutable. The resource name of the asset set asset.
  // Asset set asset resource names have the form:
  //
  // `customers/{customer_id}/assetSetAssets/{asset_set_id}~{asset_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AssetSetAsset"
    }
  ];

  // Immutable. The asset set which this asset set asset is linking to.
  string asset_set = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AssetSet"
    }
  ];

  // Immutable. The asset which this asset set asset is linking to.
  string asset = 3 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/Asset"
    }
  ];

  // Output only. The status of the asset set asset. Read-only.
  google.ads.searchads360.v0.enums.AssetSetAssetStatusEnum.AssetSetAssetStatus
      status = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}
