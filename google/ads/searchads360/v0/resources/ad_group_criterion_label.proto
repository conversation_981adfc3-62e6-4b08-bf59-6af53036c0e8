// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdGroupCriterionLabelProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Proto file describing the ad group criterion label resource.

// A relationship between an ad group criterion and a label.
message AdGroupCriterionLabel {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/AdGroupCriterionLabel"
    pattern: "customers/{customer_id}/adGroupCriterionLabels/{ad_group_id}~{criterion_id}~{entity_id}"
  };

  // Immutable. The resource name of the ad group criterion label.
  // Ad group criterion label resource names have the form:
  // `customers/{customer_id}/adGroupCriterionLabels/{ad_group_id}~{criterion_id}~{label_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AdGroupCriterionLabel"
    }
  ];

  // Immutable. The ad group criterion to which the label is attached.
  optional string ad_group_criterion = 4 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AdGroupCriterion"
    }
  ];

  // Immutable. The label assigned to the ad group criterion.
  optional string label = 5 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/Label"
    }
  ];

  // Output only. The ID of the Customer which owns the label.
  optional int64 owner_customer_id = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
