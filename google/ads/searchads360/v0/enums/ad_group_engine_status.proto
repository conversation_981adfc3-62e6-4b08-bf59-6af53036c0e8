// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.enums;

option csharp_namespace = "Google.Ads.SearchAds360.V0.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AdGroupEngineStatusProto";
option java_package = "com.google.ads.searchads360.v0.enums";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Enums";
option ruby_package = "Google::Ads::SearchAds360::V0::Enums";

// Proto file describing engine status for the ad_group_criterion.

// Container for enum describing possible AdGroup engine statuses.
message AdGroupEngineStatusEnum {
  // Status of the ad group engine.
  enum AdGroupEngineStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Deprecated. Do not use.
    AD_GROUP_ELIGIBLE = 2 [deprecated = true];

    // No ads are running for this ad group, because the ad group's end date has
    // passed.
    AD_GROUP_EXPIRED = 3;

    // The ad group has been deleted.
    AD_GROUP_REMOVED = 4;

    // No ads are running for this ad group because the associated ad group is
    // still in draft form.
    AD_GROUP_DRAFT = 5;

    // The ad group has been paused.
    AD_GROUP_PAUSED = 6;

    // The ad group is active and currently serving ads.
    AD_GROUP_SERVING = 7;

    // The ad group has been submitted (Microsoft Bing Ads legacy status).
    AD_GROUP_SUBMITTED = 8;

    // No ads are running for this ad group, because the campaign has been
    // paused.
    CAMPAIGN_PAUSED = 9;

    // No ads are running for this ad group, because the account has been
    // paused.
    ACCOUNT_PAUSED = 10;
  }
}
