// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.enums;

option csharp_namespace = "Google.Ads.SearchAds360.V0.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "AssetFieldTypeProto";
option java_package = "com.google.ads.searchads360.v0.enums";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Enums";
option ruby_package = "Google::Ads::SearchAds360::V0::Enums";

// Proto file describing asset type.

// Container for enum describing the possible placements of an asset.
message AssetFieldTypeEnum {
  // Enum describing the possible placements of an asset.
  enum AssetFieldType {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // The asset is linked for use as a headline.
    HEADLINE = 2;

    // The asset is linked for use as a description.
    DESCRIPTION = 3;

    // The asset is linked for use as mandatory ad text.
    MANDATORY_AD_TEXT = 4;

    // The asset is linked for use as a marketing image.
    MARKETING_IMAGE = 5;

    // The asset is linked for use as a media bundle.
    MEDIA_BUNDLE = 6;

    // The asset is linked for use as a YouTube video.
    YOUTUBE_VIDEO = 7;

    // The asset is linked to indicate that a hotels campaign is "Book on
    // Google" enabled.
    BOOK_ON_GOOGLE = 8;

    // The asset is linked for use as a Lead Form extension.
    LEAD_FORM = 9;

    // The asset is linked for use as a Promotion extension.
    PROMOTION = 10;

    // The asset is linked for use as a Callout extension.
    CALLOUT = 11;

    // The asset is linked for use as a Structured Snippet extension.
    STRUCTURED_SNIPPET = 12;

    // The asset is linked for use as a Sitelink.
    SITELINK = 13;

    // The asset is linked for use as a Mobile App extension.
    MOBILE_APP = 14;

    // The asset is linked for use as a Hotel Callout extension.
    HOTEL_CALLOUT = 15;

    // The asset is linked for use as a Call extension.
    CALL = 16;

    // The asset is linked for use as a Price extension.
    PRICE = 24;

    // The asset is linked for use as a long headline.
    LONG_HEADLINE = 17;

    // The asset is linked for use as a business name.
    BUSINESS_NAME = 18;

    // The asset is linked for use as a square marketing image.
    SQUARE_MARKETING_IMAGE = 19;

    // The asset is linked for use as a portrait marketing image.
    PORTRAIT_MARKETING_IMAGE = 20;

    // The asset is linked for use as a logo.
    LOGO = 21;

    // The asset is linked for use as a landscape logo.
    LANDSCAPE_LOGO = 22;

    // The asset is linked for use as a non YouTube logo.
    VIDEO = 23;

    // The asset is linked for use to select a call-to-action.
    CALL_TO_ACTION_SELECTION = 25;

    // The asset is linked for use to select an ad image.
    AD_IMAGE = 26;

    // The asset is linked for use as a business logo.
    BUSINESS_LOGO = 27;

    // The asset is linked for use as a hotel property in a Performance Max for
    // travel goals campaign.
    HOTEL_PROPERTY = 28;

    // The asset is linked for use as a discovery carousel card.
    DISCOVERY_CAROUSEL_CARD = 29;
  }
}
