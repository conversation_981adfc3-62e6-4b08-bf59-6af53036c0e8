// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.services;

import "google/api/annotations.proto";
import "google/api/client.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/services;services";
option java_multiple_files = true;
option java_outer_classname = "CustomerServiceProto";
option java_package = "com.google.ads.searchads360.v0.services";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Services";
option ruby_package = "Google::Ads::SearchAds360::V0::Services";

// Proto file describing the Customer service.

// Service to manage customers.
service CustomerService {
  option (google.api.default_host) = "searchads360.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/doubleclicksearch";

  // Returns resource names of customers directly accessible by the
  // user authenticating the call.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc ListAccessibleCustomers(ListAccessibleCustomersRequest)
      returns (ListAccessibleCustomersResponse) {
    option (google.api.http) = {
      get: "/v0/customers:listAccessibleCustomers"
    };
  }
}

// Request message for
// [CustomerService.ListAccessibleCustomers][google.ads.searchads360.v0.services.CustomerService.ListAccessibleCustomers].
message ListAccessibleCustomersRequest {}

// Response message for
// [CustomerService.ListAccessibleCustomers][google.ads.searchads360.v0.services.CustomerService.ListAccessibleCustomers].
message ListAccessibleCustomersResponse {
  // Resource name of customers directly accessible by the
  // user authenticating the call.
  repeated string resource_names = 1;
}
