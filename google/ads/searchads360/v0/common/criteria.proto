// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.common;

import "google/ads/searchads360/v0/enums/age_range_type.proto";
import "google/ads/searchads360/v0/enums/day_of_week.proto";
import "google/ads/searchads360/v0/enums/device.proto";
import "google/ads/searchads360/v0/enums/gender_type.proto";
import "google/ads/searchads360/v0/enums/keyword_match_type.proto";
import "google/ads/searchads360/v0/enums/listing_group_type.proto";
import "google/ads/searchads360/v0/enums/location_group_radius_units.proto";
import "google/ads/searchads360/v0/enums/minute_of_hour.proto";
import "google/ads/searchads360/v0/enums/webpage_condition_operand.proto";
import "google/ads/searchads360/v0/enums/webpage_condition_operator.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/common;common";
option java_multiple_files = true;
option java_outer_classname = "CriteriaProto";
option java_package = "com.google.ads.searchads360.v0.common";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Common";
option ruby_package = "Google::Ads::SearchAds360::V0::Common";

// Proto file describing criteria types.

// A keyword criterion.
message KeywordInfo {
  // The text of the keyword (at most 80 characters and 10 words).
  optional string text = 3;

  // The match type of the keyword.
  google.ads.searchads360.v0.enums.KeywordMatchTypeEnum.KeywordMatchType
      match_type = 2;
}

// A location criterion.
message LocationInfo {
  // The geo target constant resource name.
  optional string geo_target_constant = 2;
}

// A device criterion.
message DeviceInfo {
  // Type of the device.
  google.ads.searchads360.v0.enums.DeviceEnum.Device type = 1;
}

// A listing group criterion.
message ListingGroupInfo {
  // Type of the listing group.
  google.ads.searchads360.v0.enums.ListingGroupTypeEnum.ListingGroupType type =
      1;
}

// Represents an AdSchedule criterion.
//
// AdSchedule is specified as the day of the week and a time interval
// within which ads will be shown.
//
// No more than six AdSchedules can be added for the same day.
message AdScheduleInfo {
  // Minutes after the start hour at which this schedule starts.
  //
  // This field is required for CREATE operations and is prohibited on UPDATE
  // operations.
  google.ads.searchads360.v0.enums.MinuteOfHourEnum.MinuteOfHour start_minute =
      1;

  // Minutes after the end hour at which this schedule ends. The schedule is
  // exclusive of the end minute.
  //
  // This field is required for CREATE operations and is prohibited on UPDATE
  // operations.
  google.ads.searchads360.v0.enums.MinuteOfHourEnum.MinuteOfHour end_minute = 2;

  // Starting hour in 24 hour time.
  // This field must be between 0 and 23, inclusive.
  //
  // This field is required for CREATE operations and is prohibited on UPDATE
  // operations.
  optional int32 start_hour = 6;

  // Ending hour in 24 hour time; 24 signifies end of the day.
  // This field must be between 0 and 24, inclusive.
  //
  // This field is required for CREATE operations and is prohibited on UPDATE
  // operations.
  optional int32 end_hour = 7;

  // Day of the week the schedule applies to.
  //
  // This field is required for CREATE operations and is prohibited on UPDATE
  // operations.
  google.ads.searchads360.v0.enums.DayOfWeekEnum.DayOfWeek day_of_week = 5;
}

// An age range criterion.
message AgeRangeInfo {
  // Type of the age range.
  google.ads.searchads360.v0.enums.AgeRangeTypeEnum.AgeRangeType type = 1;
}

// A gender criterion.
message GenderInfo {
  // Type of the gender.
  google.ads.searchads360.v0.enums.GenderTypeEnum.GenderType type = 1;
}

// A User List criterion. Represents a user list that is defined by the
// advertiser to be targeted.
message UserListInfo {
  // The User List resource name.
  optional string user_list = 2;
}

// A language criterion.
message LanguageInfo {
  // The language constant resource name.
  optional string language_constant = 2;
}

// Represents a criterion for targeting webpages of an advertiser's website.
message WebpageInfo {
  // The name of the criterion that is defined by this parameter. The name value
  // will be used for identifying, sorting and filtering criteria with this type
  // of parameters.
  //
  // This field is required for CREATE operations and is prohibited on UPDATE
  // operations.
  optional string criterion_name = 3;

  // Conditions, or logical expressions, for webpage targeting. The list of
  // webpage targeting conditions are and-ed together when evaluated
  // for targeting. An empty list of conditions indicates all pages of the
  // campaign's website are targeted.
  //
  // This field is required for CREATE operations and is prohibited on UPDATE
  // operations.
  repeated WebpageConditionInfo conditions = 2;

  // Website criteria coverage percentage. This is the computed percentage
  // of website coverage based on the website target, negative website target
  // and negative keywords in the ad group and campaign. For instance, when
  // coverage returns as 1, it indicates it has 100% coverage. This field is
  // read-only.
  double coverage_percentage = 4;
}

// Logical expression for targeting webpages of an advertiser's website.
message WebpageConditionInfo {
  // Operand of webpage targeting condition.
  google.ads.searchads360.v0.enums.WebpageConditionOperandEnum
      .WebpageConditionOperand operand = 1;

  // Operator of webpage targeting condition.
  google.ads.searchads360.v0.enums.WebpageConditionOperatorEnum
      .WebpageConditionOperator
      operator = 2;

  // Argument of webpage targeting condition.
  optional string argument = 4;
}

// A radius around a list of locations specified through a feed.
message LocationGroupInfo {
  // Geo target constant(s) restricting the scope of the geographic area within
  // the feed. Currently only one geo target constant is allowed.
  repeated string geo_target_constants = 6;

  // Distance in units specifying the radius around targeted locations.
  // This is required and must be set in CREATE operations.
  optional int64 radius = 7;

  // Unit of the radius. Miles and meters are supported for geo target
  // constants. Milli miles and meters are supported for feed item sets.
  // This is required and must be set in CREATE operations.
  google.ads.searchads360.v0.enums.LocationGroupRadiusUnitsEnum
      .LocationGroupRadiusUnits radius_units = 4;

  // FeedItemSets whose FeedItems are targeted. If multiple IDs are specified,
  // then all items that appear in at least one set are targeted. This field
  // cannot be used with geo_target_constants. This is optional and can only be
  // set in CREATE operations.
  repeated string feed_item_sets = 8;
}

// An audience criterion.
message AudienceInfo {
  // The Audience resource name.
  string audience = 1;
}
