// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v20.resources;

import "google/ads/googleads/v20/enums/conversion_action_category.proto";
import "google/ads/googleads/v20/enums/conversion_origin.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V20.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v20/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomerConversionGoalProto";
option java_package = "com.google.ads.googleads.v20.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V20\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V20::Resources";

// Biddability control for conversion actions with a matching category and
// origin.
message CustomerConversionGoal {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CustomerConversionGoal"
    pattern: "customers/{customer_id}/customerConversionGoals/{category}~{source}"
  };

  // Immutable. The resource name of the customer conversion goal.
  // Customer conversion goal resource names have the form:
  //
  // `customers/{customer_id}/customerConversionGoals/{category}~{origin}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomerConversionGoal"
    }
  ];

  // The conversion category of this customer conversion goal. Only
  // conversion actions that have this category will be included in this goal.
  google.ads.googleads.v20.enums.ConversionActionCategoryEnum
      .ConversionActionCategory category = 2;

  // The conversion origin of this customer conversion goal. Only
  // conversion actions that have this conversion origin will be included in
  // this goal.
  google.ads.googleads.v20.enums.ConversionOriginEnum.ConversionOrigin origin =
      3;

  // The biddability of the customer conversion goal.
  bool biddable = 4;
}
