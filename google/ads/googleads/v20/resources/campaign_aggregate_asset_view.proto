// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v20.resources;

import "google/ads/googleads/v20/enums/asset_field_type.proto";
import "google/ads/googleads/v20/enums/asset_source.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V20.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v20/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignAggregateAssetViewProto";
option java_package = "com.google.ads.googleads.v20.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V20\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V20::Resources";

// Proto file describing the CampaignAggregateAsset resource.

// A campaign-level aggregate asset view that shows where the asset is linked,
// performamce of the asset and stats.
message CampaignAggregateAssetView {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CampaignAggregateAssetView"
    pattern: "customers/{customer_id}/campaignAggregateAssetViews/{campaign_id}~{asset_id}~{asset_link_source}~{field_type}"
  };

  // Output only. The resource name of the campaign aggregate asset view.
  // Campaign aggregate asset view resource names have the form:
  //
  // `customers/{customer_id}/campaignAggregateAssetViews/{Campaign.campaign_id}~{Asset.asset_id}~{AssetLinkSource.asset_link_source}~{AssetFieldType.field_type}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignAggregateAssetView"
    }
  ];

  // Output only. Campaign in which the asset served.
  optional string campaign = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Output only. The ID of the asset.
  optional string asset = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Asset" }
  ];

  // Output only. Source of the asset link.
  optional google.ads.googleads.v20.enums.AssetSourceEnum.AssetSource
      asset_source = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. FieldType of the asset.
  optional google.ads.googleads.v20.enums.AssetFieldTypeEnum.AssetFieldType
      field_type = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}
