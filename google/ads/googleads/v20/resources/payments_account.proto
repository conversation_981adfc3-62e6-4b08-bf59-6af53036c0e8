// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v20.resources;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V20.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v20/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "PaymentsAccountProto";
option java_package = "com.google.ads.googleads.v20.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V20\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V20::Resources";

// Proto file describing the PaymentsAccount resource.

// A payments account, which can be used to set up billing for an Ads customer.
message PaymentsAccount {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/PaymentsAccount"
    pattern: "customers/{customer_id}/paymentsAccounts/{payments_account_id}"
  };

  // Output only. The resource name of the payments account.
  // PaymentsAccount resource names have the form:
  //
  // `customers/{customer_id}/paymentsAccounts/{payments_account_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/PaymentsAccount"
    }
  ];

  // Output only. A 16 digit ID used to identify a payments account.
  optional string payments_account_id = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the payments account.
  optional string name = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The currency code of the payments account.
  // A subset of the currency codes derived from the ISO 4217 standard is
  // supported.
  optional string currency_code = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A 12 digit ID used to identify the payments profile associated
  // with the payments account.
  optional string payments_profile_id = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A secondary payments profile ID present in uncommon
  // situations, for example, when a sequential liability agreement has been
  // arranged.
  optional string secondary_payments_profile_id = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Paying manager of this payment account.
  optional string paying_manager_customer = 13 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Customer"
    }
  ];
}
