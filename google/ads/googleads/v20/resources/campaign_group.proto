// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v20.resources;

import "google/ads/googleads/v20/enums/campaign_group_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V20.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v20/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignGroupProto";
option java_package = "com.google.ads.googleads.v20.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V20\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V20::Resources";

// Proto file describing the Campaign group resource.

// A campaign group.
message CampaignGroup {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CampaignGroup"
    pattern: "customers/{customer_id}/campaignGroups/{campaign_group_id}"
  };

  // Immutable. The resource name of the campaign group.
  // Campaign group resource names have the form:
  //
  // `customers/{customer_id}/campaignGroups/{campaign_group_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignGroup"
    }
  ];

  // Output only. The ID of the campaign group.
  int64 id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The name of the campaign group.
  //
  // This field is required and should not be empty when creating new campaign
  // groups.
  //
  // It must not contain any null (code point 0x0), NL line feed
  // (code point 0xA) or carriage return (code point 0xD) characters.
  string name = 4;

  // The status of the campaign group.
  //
  // When a new campaign group is added, the status defaults to ENABLED.
  google.ads.googleads.v20.enums.CampaignGroupStatusEnum.CampaignGroupStatus
      status = 5;
}
