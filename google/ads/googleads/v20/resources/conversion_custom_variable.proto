// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v20.resources;

import "google/ads/googleads/v20/enums/conversion_custom_variable_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V20.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v20/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "ConversionCustomVariableProto";
option java_package = "com.google.ads.googleads.v20.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V20\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V20::Resources";

// Proto file describing the Conversion Custom Variable resource.

// A conversion custom variable
// See "About custom variables for conversions" at
// https://support.google.com/google-ads/answer/9964350
message ConversionCustomVariable {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/ConversionCustomVariable"
    pattern: "customers/{customer_id}/conversionCustomVariables/{conversion_custom_variable_id}"
  };

  // Immutable. The resource name of the conversion custom variable.
  // Conversion custom variable resource names have the form:
  //
  // `customers/{customer_id}/conversionCustomVariables/{conversion_custom_variable_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/ConversionCustomVariable"
    }
  ];

  // Output only. The ID of the conversion custom variable.
  int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The name of the conversion custom variable.
  // Name should be unique. The maximum length of name is 100 characters.
  // There should not be any extra spaces before and after.
  string name = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Immutable. The tag of the conversion custom variable. It is used
  // in the event snippet and sent to Google Ads along with conversion pings.
  // For conversion uploads in Google Ads API, the resource name of the
  // conversion custom variable is used. Tag should be unique. The maximum size
  // of tag is 100 bytes. There should not be any extra spaces before and after.
  // Currently only lowercase letters, numbers and underscores are allowed in
  // the tag.
  string tag = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // The status of the conversion custom variable for conversion event accrual.
  google.ads.googleads.v20.enums.ConversionCustomVariableStatusEnum
      .ConversionCustomVariableStatus status = 5;

  // Output only. The resource name of the customer that owns the conversion
  // custom variable.
  string owner_customer = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Customer"
    }
  ];
}
