// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v20.resources;

import "google/ads/googleads/v20/enums/campaign_draft_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V20.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v20/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CampaignDraftProto";
option java_package = "com.google.ads.googleads.v20.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V20\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V20::Resources";

// Proto file describing the Campaign Draft resource.

// A campaign draft.
message CampaignDraft {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CampaignDraft"
    pattern: "customers/{customer_id}/campaignDrafts/{base_campaign_id}~{draft_id}"
  };

  // Immutable. The resource name of the campaign draft.
  // Campaign draft resource names have the form:
  //
  // `customers/{customer_id}/campaignDrafts/{base_campaign_id}~{draft_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CampaignDraft"
    }
  ];

  // Output only. The ID of the draft.
  //
  // This field is read-only.
  optional int64 draft_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The base campaign to which the draft belongs.
  optional string base_campaign = 10 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // The name of the campaign draft.
  //
  // This field is required and should not be empty when creating new
  // campaign drafts.
  //
  // It must not contain any null (code point 0x0), NL line feed
  // (code point 0xA) or carriage return (code point 0xD) characters.
  optional string name = 11;

  // Output only. Resource name of the Campaign that results from overlaying the
  // draft changes onto the base campaign.
  //
  // This field is read-only.
  optional string draft_campaign = 12 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/Campaign"
    }
  ];

  // Output only. The status of the campaign draft. This field is read-only.
  //
  // When a new campaign draft is added, the status defaults to PROPOSED.
  google.ads.googleads.v20.enums.CampaignDraftStatusEnum.CampaignDraftStatus
      status = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether there is an experiment based on this draft currently
  // serving.
  optional bool has_experiment_running = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource name of the long-running operation that can be
  // used to poll for completion of draft promotion. This is only set if the
  // draft promotion is in progress or finished.
  optional string long_running_operation = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
