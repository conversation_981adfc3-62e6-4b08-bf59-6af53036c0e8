# moego

Monorepo for <PERSON><PERSON><PERSON>, [welcome to join us!](./docs/WHATSMONOREPO.md)

- [moego](#moego)
  - [what's mono-repo](#whats-mono-repo)
  - [environmental dependency](#environmental-dependency)
  - [init project](#init-project)
  - [build project](#build-project)
  - [create a new project](#create-a-new-project)
  - [project structure](#project-structure)


## what's mono-repo
[what's mono-repo](./docs/WHATSMONOREPO.md)

[what's bazel](./docs/USE_BAZEL.md)

## environmental dependency
> 如果你是Ubuntu 用户，请参考 [UBUNTU README](./docs/README_ubuntu.md)
1. Golang > 1.24.0 
    ```bash
    # 推荐使用homebrew安装
    brew install golang
    ```
2. lcov工具, 用作单测报告解析
   ```bash
   # 推荐使用homebrew安装
   brew install lcov
   ```
3. buf工具, 用作proto文件管理
    ```bash
    brew install bufbuild/buf/buf
    ```
    > 以下命令均已集成于`make init` , 会通过`go install` 安装, 当然也可以选择自行安装
4. Bazelisk >= 1.24.1
    ```bash
    brew install bazelisk
    # 或者make init时会自动使用go install 安装
    ```
5. (可选) 当执行api测试时, 需要openai-generator-cli, 详细请看[api测试.md](./backend/test/api_integration/README.md)
    ```bash
    # brew 不支持 openai-generator-cli, 需要使用npm安装
    npm install @openapitools/openapi-generator-cli -g
    ```

## init project
开始之前, 请确保您的git配置正确, 包括但不限于 token, 邮箱, 用户名等

并检查go env配置, 确保以下环境变量正确:
```bash
export GOPRIVATE=github.com/MoeGolibrary 
```
\>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 准备起飞 🛫🛫🛫

执行命令:
```bash
make init
```
`make init` 会安装项目所需基本依赖,包括但不限于:
- golangci-lint 
- goimports-reviser 
- buf 
- ...

并且会初始化api测试需要的代码, 使用gazelle更新所有BUILD.bazel文件

## build project
编译使用[Makefile](./Makefile), 会基于bazelisk进行编译

**编译前使用 gazelle 管理依赖** 

```bash
make gazelle
```
`mode=all` 更新`moego`项目下(除了[.bazelignore](.bazelignore))所有`BUILD.bazel`文件

编译项目的产物不会提交到远程仓库

- 编译所有项目("backend"目录下所有项目), 会自动执行`make gazelle`
    ```bash
    make build
    ```
- 当需要指定编译目录时, 使用`dir`参数
    ```bash
    # 编译指定目录
    make build dir=//backend/common/rpc/rpc/examples/helloworld/server
    # 编译指定目录下的所有项目
    make build dir=//backend/common/rpc/...
    ```

项目编译原理

编译会自动查找依赖并编译, 举个例子, 编译 [helloworld](./backend/common/rpc/rpc/examples/helloworld/server/main.go) 项目时, 根据其目录下的[BUILD.bazel](./backend/common/rpc/rpc/examples/helloworld/server/BUILD.bazel)文件
```
 deps = [
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/rpc",
        "//backend/proto/helloworld",
    ],
```
会自动查找并编译 `//backend/common/rpc/codec/grpc`, `//backend/common/rpc/rpc`, `//backend/proto/helloworld` 三个包。此时本地并不需要存在这三个包, bazel会以一种拓扑的方式, 递归到最叶子节点, 从叶子节点开始编译
```
    A         A包依赖BC两个目录, BC目录依赖D,E,F,G四个目录, 而G又刚好是proto目录
   / \        此时项目中只有hello.proto, 还没有编译成go\valida\grpc等文件
  B   C       但是bazel会在本次编译周期中先从G开始编译, 并生成go文件放入临时目录中
 / \ / \      然后再编译其他包
D  E F  G (proto)
```

**注意**：虽然编译时并不需要把proto文件编译成go、rpc、validate等文件, 但是本地调试时如果没有产物编译器会出现如找不到依赖、编译错误、无法跳转等问题, 为了避免这一情况, 提供了[proto编译脚本](./scripts/.proto.sh), 使用方式:
```bash
 make proto
```
## create a new project
创建新项目前，需要先在[modules.yml](./backend/.modules.yml) 文件中登记, 格式为:
```yaml
# 实例, 请参考modules.yml文件, 根据具体情况修改
modules:
  - name: $(module_name) # 模块名暂定为部门名，比如platform\foundation
    desc: $(module_desc)
    owner: $(module_owner)
    code: $(module_code)
```

Makefile 提供了创建新项目的命令, 使用方式:
```bash
make create module=$(module) service=$(service)
```
该命令会调用[create_app](./scripts/.create_app.sh) 脚本, 脚本会自动执行以下操作(以todo项目为例子):
1. 在backend目录下创建新项目, 并复制[template-go](./template/template-go) 目录到新项目目录
2. 修改[main.go](./backend/app/todo/main.go) 文件, 将服务名替换为新项目名
3. 修改[CODEOWNERS](./backend/app/todo/CODEOWNERS) 文件, 将owner替换为新项目owner, 项目owner取当前执行命令的git邮箱
4. 在backend/proto目录下创建新项目的[proto](./backend/proto/todo/v1/todo.proto)文件
5. 为该项目生成一个唯一的错误码段 
6. 修改[proto](./backend/proto/todo/v1/todo.proto)文件中的服务名, 适配新项目
7. 通过`gazelle`在新项目中新增`BUILD.bazel`


您需要自己动手的事情:
1. 修改[config.yaml](./backend/app/todo/config.yaml) 文件, 配置项目所需配置
2. 修改[README.md](./backend/app/todo/README.md) 文件描述项目
3. 如果需要编译proto生成`pb.go`, `.validate.go`, `.grpc.go`等文件, 请执行 `make proto`


做完以上事情后, 您就可以开始编写项目代码了

## develop code on an existing project
如果在已有项目中开发新的api 需要做的是(以下用pet服务距离)
1. 在[proto文件](./backend/proto/pet/v1/pet.proto)中新增代码
```proto
// PetService
service PetService {
  // search pet by term (pet name/customer name)
  rpc SearchPet(SearchPetRequest) returns (SearchPetResponse);

  // sync customer and pet data to elasticsearch
  // now, only used for airflow scheduled tasks
  rpc IndexPetDocument(IndexPetDocumentRequest) returns (IndexPetDocumentResponse);

  >>>>>>> 在这里新增你的api
}

>>>>>>> 在这里新增你的request / response
```
2. 检查proto lint
只需要执行
```
make lint
```
proto api-linter 会检查所有proto文件规范, 最终生成在[api-linter-report](./backend/proto/api-linter-report.yaml) 文件中

3. lint 通过, 编译proto

只需要在根目录执行
```Makefile
make proto
```
会在proto 目录下生成编译产物(pb.go)
```
backend/proto/pet/v1
├── BUILD.bazel
├── pet.pb.go
├── pet.pb.validate.go
├── pet.proto
└── pet_grpc.pb.go
```
3. 回到go项目中继续开发(比如pet服务的[main.go](./backend/app/pet/main.go))
4. 开发完后，执行lint检查golang 代码是否符合规范, 通过后, 还会根据变更 BUILD.bazel
```
> make lint
Running lint checks...
--------------------------- Proto Files ---------------------------
Running api linter...                             ✓
--------------------------- Code Linting -------------------------
Running go mod tidy...                            ✓
Running goimports-reviser...                      ✓
Running golangci-lint...                          ✓
--------------------------- Bazel ---------------------------
Running Bazel files buildifier...                 ✓
All lint checks passed! ✨
```
5. 提交代码即可, GitHub action 会自动编译和部署

## project structure
截止2024-12-18, 项目结构如下:
```
.
├── .github              # github actions\hooks 配置
├── backend              # 后端项目
│   ├── app              # 存放服务代码
│   │   └── helloworld   # 示例服务
│   ├── common           # 公共库
│   │   └── rpc          # rpc 库
│   │   └── utils        # 工具库
│   ├── docker           # docker 配置
│   ├── test             # 存放api测试代码
│   ├── tools            # 存放工具代码
│   └── proto            # 存放proto文件
├── bazel                # bazel 配置
│   ├── out              # 编译产物, 仅本地存在  
│   └── tools            # bazel 工具
├── scripts              # 脚本
├── docs                 # 文档
└── template             # 模板
    └── template-go      # go项目模板
```
