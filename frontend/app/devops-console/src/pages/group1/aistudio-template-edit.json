{"type": "page", "title": "ai-studio-模版编辑", "body": [{"type": "form", "id": "template_form", "title": "表单", "body": [{"type": "input-text", "label": "模版名", "name": "name", "row": 1, "colSize": "1", "id": "u:1390e4061139"}, {"type": "select", "label": "<strong>模型</strong>", "name": "model", "row": 2, "id": "id:model", "options": [{"label": "gemini-2.0-flash(token多，速度快)", "value": "gemini-2.0-flash"}, {"label": "gemini-2.5-pro(token少，速度慢，效果好)", "value": "gemini-2.5-pro"}, {"label": "gemini-2.5-flash(token少，速度慢，效果好)", "value": "gemini-2.5-flash"}], "colSize": "1", "multiple": false, "value": ""}, {"type": "textarea", "label": "<strong>Prompt：</strong>最好为英文，中文有时会任务失败，这是Prompt的示例", "name": "prompt", "row": 3, "id": "id:prompt", "value": "", "minRows": 3, "maxRows": 20, "colSize": "1"}, {"type": "checkboxes", "label": "<strong>mcp列表：</strong>选取这个Prompt会使用的MCP，不要多选。如果你选择了对应的MCP，则必须在下面填写相关的环境变量", "name": "mcps", "row": 4, "id": "id:mcps", "colSize": "1", "value": "", "multiple": true, "options": [{"label": "mcp-atlassian", "value": "mcp-atlassian"}, {"label": "datadog", "value": "datadog"}, {"label": "mcp-slack", "value": "mcp-slack"}, {"label": "unix_timestamps_mcp", "value": "unix_timestamps_mcp"}], "checkAll": false, "joinValues": true}, {"type": "textarea", "label": "<strong>环境变量Keys：</strong>只需要填写键，不需要值，多个用逗号分隔", "name": "env<PERSON><PERSON><PERSON>", "row": 5, "colSize": "1", "id": "id:envs", "minRows": 3, "maxRows": 20, "value": ""}], "dsType": "api", "labelAlign": "top", "mode": "flex", "actions": [{"type": "button", "label": "保存模版", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "template_form"}]}}, "level": "primary", "id": "u:a3dd75d89041"}], "reload": "", "resetAfterSubmit": false, "api": {"url": "/moego.bff/devops/ai-studio/updateAiStudioTemplate", "method": "post", "dataType": "json", "data": {"id": "${template_id}", "model": "${model}", "prompt": "${prompt}", "mcps": "${mcps}", "name": "${name}", "envKeys": "${envKeys}"}}, "feat": "Insert", "persistData": false}], "id": "u:cbc9859498b0", "asideResizor": false, "pullRefresh": {"disabled": true}, "initApi": {"method": "post", "url": "/moego.bff/devops/ai-studio/getAiStudioTemplate", "data": {"id": "${template_id}"}}}